apiVersion: catalog.cattle.io/v1
kind: App
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
spec:
  chart:
    metadata:
      name: kubedoom-extension
      version: 0.1.0
      description: "KubeDoom Extension - Play Doom to kill Kubernetes pods"
      annotations:
        catalog.cattle.io/display-name: "KubeDoom Extension"
        catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
        catalog.cattle.io/type: "rancher-ui-extension"
        catalog.cattle.io/ui-extensions-version: ">=1.0.0"
  projectId: ""
  targetNamespace: cattle-ui-plugin-system
  values:
    plugin:
      name: kubedoom-extension
      version: 0.1.0
      endpoint: kubedoom-extension-0.1.0.umd.min.js
      noCache: false
