<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>kubedoom-extension [26 May 2025 at 20:52]</title>
    <link rel="shortcut icon" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAABrVBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////+O1foceMD///+J0/qK1Pr7/v8Xdr/9///W8P4UdL7L7P0Scr2r4Pyj3vwad8D5/f/2/f+55f3E6f34+/2H0/ojfMKpzOd0rNgQcb3F3O/j9f7c8v6g3Pz0/P/w+v/q+P7n9v6T1/uQ1vuE0vqLut/y+v+Z2fvt+f+15Pzv9fuc2/vR7v2V2Pvd6/bg9P7I6/285/2y4/yp3/zp8vk8i8kqgMT7/P31+fyv4vxGkcz6/P6/6P3j7vfS5PNnpNUxhcbO7f7F6v3O4vHK3/DA2u631Ouy0eqXweKJud5wqthfoNMMbLvY8f73+v2dxeR8sNtTmdDx9/zX6PSjyeaCtd1YnNGX2PuQveCGt95Nls42h8dLlM3F4vBtAAAAM3RSTlMAAyOx0/sKBvik8opWGBMOAe3l1snDm2E9LSb06eHcu5JpHbarfHZCN9CBb08zzkdNS0kYaptYAAAFV0lEQVRYw92X51/aYBDHHS2O2qqttVbrqNq9m+TJIAYIShBkWwqIiCgoWvfeq7Z2/s29hyQNyUcR7LveGwVyXy6XH8/9rqxglLfUPLxVduUor3h0rfp2TYvpivk37929TkG037hffoX0+peVtZQc1589rigVUdXS/ABSAyEmGIO/1XfvldSK8vs3OqB6u3m0nxmIrvgB0dj7rr7Y9IbuF68hnfFaiHA/sxqm0wciIG43P60qKv9WXWc1RXGh/mFESFABTSBi0sNAKzqet17eCtOb3kZIDwxEEU0oAIJGYxNBDhBND29e0rtXXbcpuPmED9IhEAAQ/AXEaF8EPmnrrKsv0LvWR3fg5sWDNAFZOgAgaKvZDogHNU9MFwnnYROkc56RD5CjAbQX9Ow4g7upCsvYu55aSI/Nj0H1akgKQEUM94dwK65hYRmFU9MIcH/fqJYOZYcnuJSU/waKDgTOEVaVKhwrTRP5XzgSpAITYzom7UvkhFX5VutmxeNnWDjjswTKTyfgluNDGbUpWissXhF3s7mlSml+czWkg3D0l1nNjGNjz3myOQOa1KM/jOS6ebdbAVTCi4gljHSFrviza7tOgRWcS0MOUX9zdNgag5w7rRqA44Lzw0hr1WqES36dFliSJFlh2rXIae3FFcDDgKdxrUIDePr8jGcSClV1u7A9xeN0ModY/pHMxmR1EzRh8TJiwqsHmKW0l4FCEZI+jHio+JdPPE9qwQtTRxku2D8sIeRL2LnxWSllANCQGOIiqVHAz2ye2JR0DcH+HoxDkaADLjgxjKQ+AwCX/g0+DNgdG0ukYCONAe+dbc2IAc6fwt1ARoDSezNHxV2Cmzwv3O6lDMV55edBGwGK9n1+x2F8EDfAGCxug8MhpsMEcTEAWf3rx2vZhe/LAmtIn/6apE6PN0ULKgywD9mmdxbmFl3OvD5AS5fW5zLbv/YHmcsBTjf/afDz3MaZTVCfAP9z6/Bw6ycv8EUBWJIn9zYcoAWWlW9+OzO3vkTy8H+RANLmdrpOuYWdZYEXpo+TlCJrW5EARb7fF+bWdqf3hhyZI1nWJQHgznErZhbjoEsWqi8dQNoE294aldzFurwSABL2XXMf9+H1VQGke9exw5P/AnA5Pv5ngMul7LOvO922iwACu8WkCwLCafvM4CeWPxfA8lNHcWZSoi8EwMAIciKX2Z4SWCMAa3snCZ/G4EA8D6CMLNFsGQhkkz/gQNEBbPCbWsxGUpYVu3z8IyNAknwJkfPMEhLyrdi5RTyUVACkw4GSFRNWJNEW+fgPGwHD8/JxnRuLabN4CGNRkAE23na2+VmEAUmrYymSGjMAYqH84YUIyzgzs3XC7gNgH36Vcc4zKY9o9fgPBXUAiHHwVboBHGLiX6Zcjp1f2wu4tvzZKo0ecPnDtQYDQvJXaBeNzce45Fp28ZQLrEZVuFqgBwOalArKXnW1UzlnSusQKJqKYNuz4tOnI6sZG4zanpemv+7ySU2jbA9h6uhcgpfy6G2PahirDZ6zvq6zDduMVFTKvzw8wgyEdelwY9in3XkEPs3osJuwRQ4qTkfzifndg9Gfc4pdsu82+tTnHZTBa2EAMrqr2t43pguc8tNm7JQVQ2S0ukj2d22dhXYP0/veWtwKrCkNoNimAN5+Xr/oLrxswKbVJjteWrX7eR63o4j9q0GxnaBdWgGA5VStpanIjQmEhV0/nVt5VOFUvix6awJhPcAaTEShgrG+iGyvb5a0Ndb1YGHFPEwoqAinoaykaID1o1pdPNu7XsnCKQ3R+hwWIIhGvORcJUBYXe3Xa3vq/mF/N9V13ugufMkfXn+KHsRD0B8AAAAASUVORK5CYII=" type="image/x-icon" />

    <script>
      window.enableWebSocket = false;
    </script>
    <!-- viewer.js -->
<script>/*! For license information please see viewer.js.LICENSE.txt */
(()=>{var e={4184:(e,t)=>{var n;
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var r={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var a=i.apply(null,n);a&&e.push(a)}}else if("object"===o)if(n.toString===Object.prototype.toString)for(var s in n)r.call(n,s)&&n[s]&&e.push(s);else e.push(n.toString())}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(n=function(){return i}.apply(t,[]))||(e.exports=n)}()},3908:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Button__button{background:#fff;border:1px solid #aaa;border-radius:4px;cursor:pointer;display:inline-block;font:var(--main-font);outline:none;padding:5px 7px;transition:background .3s ease;white-space:nowrap}.Button__button:focus,.Button__button:hover{background:#ffefd7}.Button__button.Button__active{background:orange;color:#000}.Button__button[disabled]{cursor:default}","",{version:3,sources:["webpack://./client/components/Button.css"],names:[],mappings:"AAAA,gBACE,eAAgB,CAChB,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CACf,oBAAqB,CACrB,qBAAsB,CACtB,YAAa,CACb,eAAgB,CAChB,8BAA+B,CAC/B,kBACF,CAEA,4CAEE,kBACF,CAEA,+BACE,iBAAmB,CACnB,UACF,CAEA,0BACE,cACF",sourcesContent:[".button {\n  background: #fff;\n  border: 1px solid #aaa;\n  border-radius: 4px;\n  cursor: pointer;\n  display: inline-block;\n  font: var(--main-font);\n  outline: none;\n  padding: 5px 7px;\n  transition: background .3s ease;\n  white-space: nowrap;\n}\n\n.button:focus,\n.button:hover {\n  background: #ffefd7;\n}\n\n.button.active {\n  background: #ffa500;\n  color: #000;\n}\n\n.button[disabled] {\n  cursor: default;\n}\n"],sourceRoot:""}]),a.locals={button:"Button__button",active:"Button__active"};const s=a},2396:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Checkbox__label{display:inline-block}.Checkbox__checkbox,.Checkbox__label{cursor:pointer}.Checkbox__itemText{margin-left:3px;position:relative;top:-2px;vertical-align:middle}","",{version:3,sources:["webpack://./client/components/Checkbox.css"],names:[],mappings:"AAAA,iBAEE,oBACF,CAEA,qCAJE,cAMF,CAEA,oBACE,eAAgB,CAChB,iBAAkB,CAClB,QAAS,CACT,qBACF",sourcesContent:[".label {\n  cursor: pointer;\n  display: inline-block;\n}\n\n.checkbox {\n  cursor: pointer;\n}\n\n.itemText {\n  margin-left: 3px;\n  position: relative;\n  top: -2px;\n  vertical-align: middle;\n}\n"],sourceRoot:""}]),a.locals={label:"Checkbox__label",checkbox:"Checkbox__checkbox",itemText:"Checkbox__itemText"};const s=a},3213:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".CheckboxList__container{font:var(--main-font);white-space:nowrap}.CheckboxList__label{font-size:11px;font-weight:700;margin-bottom:7px}.CheckboxList__item+.CheckboxList__item{margin-top:1px}","",{version:3,sources:["webpack://./client/components/CheckboxList.css"],names:[],mappings:"AAAA,yBACE,qBAAsB,CACtB,kBACF,CAEA,qBACE,cAAe,CACf,eAAiB,CACjB,iBACF,CAEA,wCACE,cACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-size: 11px;\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.item + .item {\n  margin-top: 1px;\n}\n"],sourceRoot:""}]),a.locals={container:"CheckboxList__container",label:"CheckboxList__label",item:"CheckboxList__item"};const s=a},580:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".ContextMenu__container{background:#fff;border:1px solid #aaa;border-radius:4px;font:var(--main-font);list-style:none;opacity:1;padding:0;position:absolute;transition:opacity .2s ease,visibility .2s ease;visibility:visible;white-space:nowrap}.ContextMenu__hidden{opacity:0;visibility:hidden}","",{version:3,sources:["webpack://./client/components/ContextMenu.css"],names:[],mappings:"AAAA,wBAKE,eAAgB,CAChB,qBAAsB,CAFtB,iBAAkB,CAHlB,qBAAsB,CAMtB,eAAgB,CAChB,SAAU,CALV,SAAU,CADV,iBAAkB,CASlB,+CAAiD,CADjD,kBAAmB,CADnB,kBAGF,CAEA,qBACE,SAAU,CACV,iBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  position: absolute;\n  padding: 0;\n  border-radius: 4px;\n  background: #fff;\n  border: 1px solid #aaa;\n  list-style: none;\n  opacity: 1;\n  white-space: nowrap;\n  visibility: visible;\n  transition: opacity .2s ease, visibility .2s ease;\n}\n\n.hidden {\n  opacity: 0;\n  visibility: hidden;\n}\n"],sourceRoot:""}]),a.locals={container:"ContextMenu__container",hidden:"ContextMenu__hidden"};const s=a},9270:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".ContextMenuItem__item{cursor:pointer;margin:0;padding:8px 14px;-webkit-user-select:none;user-select:none}.ContextMenuItem__item:hover{background:#ffefd7}.ContextMenuItem__disabled{color:grey;cursor:default}.ContextMenuItem__item.ContextMenuItem__disabled:hover{background:transparent}","",{version:3,sources:["webpack://./client/components/ContextMenuItem.css"],names:[],mappings:"AAAA,uBACE,cAAe,CACf,QAAS,CACT,gBAAiB,CACjB,wBAAiB,CAAjB,gBACF,CAEA,6BACE,kBACF,CAEA,2BAEE,UAAW,CADX,cAEF,CAEA,uDACE,sBACF",sourcesContent:[".item {\n  cursor: pointer;\n  margin: 0;\n  padding: 8px 14px;\n  user-select: none;\n}\n\n.item:hover {\n  background: #ffefd7;\n}\n\n.disabled {\n  cursor: default;\n  color: gray;\n}\n\n.item.disabled:hover {\n  background: transparent;\n}\n"],sourceRoot:""}]),a.locals={item:"ContextMenuItem__item",disabled:"ContextMenuItem__disabled"};const s=a},1746:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Icon__icon{background:no-repeat 50%/contain;display:inline-block}","",{version:3,sources:["webpack://./client/components/Icon.css"],names:[],mappings:"AAAA,YACE,gCAAoC,CACpC,oBACF",sourcesContent:[".icon {\n  background: no-repeat center/contain;\n  display: inline-block;\n}\n"],sourceRoot:""}]),a.locals={icon:"Icon__icon"};const s=a},697:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o),s=n(1667),u=n.n(s),l=n(4911),c=n(8752),h=n(4150),f=n(8868),d=a()(i()),p=u()(l.Z),g=u()(c.Z),b=u()(h.Z),v=u()(f.Z);d.push([e.id,".ModuleItem__container{background:no-repeat 0;cursor:pointer;margin-bottom:4px;padding-left:18px;position:relative;white-space:nowrap}.ModuleItem__container.ModuleItem__module{background-image:url("+p+");background-position-x:1px}.ModuleItem__container.ModuleItem__folder{background-image:url("+g+")}.ModuleItem__container.ModuleItem__chunk{background-image:url("+b+")}.ModuleItem__container.ModuleItem__invisible:hover:before{background:url("+v+') no-repeat 0;content:"";height:100%;left:0;position:absolute;top:1px;width:13px}',"",{version:3,sources:["webpack://./client/components/ModuleItem.css"],names:[],mappings:"AAAA,uBACE,sBAAiC,CACjC,cAAe,CACf,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,kBACF,CAEA,0CACE,wDAAkD,CAClD,yBACF,CAEA,0CACE,wDACF,CAEA,yCACE,wDACF,CAEA,0DACE,8DAAqE,CACrE,UAAW,CACX,WAAY,CACZ,MAAO,CAEP,iBAAkB,CADlB,OAAQ,CAER,UACF",sourcesContent:[".container {\n  background: no-repeat left center;\n  cursor: pointer;\n  margin-bottom: 4px;\n  padding-left: 18px;\n  position: relative;\n  white-space: nowrap;\n}\n\n.container.module {\n  background-image: url('../assets/icon-module.svg');\n  background-position-x: 1px;\n}\n\n.container.folder {\n  background-image: url('../assets/icon-folder.svg');\n}\n\n.container.chunk {\n  background-image: url('../assets/icon-chunk.svg');\n}\n\n.container.invisible:hover::before {\n  background: url('../assets/icon-invisible.svg') no-repeat left center;\n  content: \"\";\n  height: 100%;\n  left: 0;\n  top: 1px;\n  position: absolute;\n  width: 13px;\n}\n"],sourceRoot:""}]),d.locals={container:"ModuleItem__container",module:"ModuleItem__module",folder:"ModuleItem__folder",chunk:"ModuleItem__chunk",invisible:"ModuleItem__invisible"};const m=d},3784:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".ModulesList__container{font:var(--main-font)}","",{version:3,sources:["webpack://./client/components/ModulesList.css"],names:[],mappings:"AAAA,wBACE,qBACF",sourcesContent:[".container {\n  font: var(--main-font);\n}\n"],sourceRoot:""}]),a.locals={container:"ModulesList__container"};const s=a},2393:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".ModulesTreemap__container{align-items:stretch;display:flex;height:100%;position:relative;width:100%}.ModulesTreemap__map{flex:1}.ModulesTreemap__sidebarGroup{font:var(--main-font);margin-bottom:20px}.ModulesTreemap__showOption{margin-top:5px}.ModulesTreemap__activeSize{font-weight:700}.ModulesTreemap__foundModulesInfo{display:flex;font:var(--main-font);margin:8px 0 0}.ModulesTreemap__foundModulesInfoItem+.ModulesTreemap__foundModulesInfoItem{margin-left:15px}.ModulesTreemap__foundModulesContainer{margin-top:15px;max-height:600px;overflow:auto}.ModulesTreemap__foundModulesChunk+.ModulesTreemap__foundModulesChunk{margin-top:15px}.ModulesTreemap__foundModulesChunkName{cursor:pointer;font:var(--main-font);font-weight:700;margin-bottom:7px}.ModulesTreemap__foundModulesList{margin-left:7px}","",{version:3,sources:["webpack://./client/components/ModulesTreemap.css"],names:[],mappings:"AAAA,2BACE,mBAAoB,CACpB,YAAa,CACb,WAAY,CACZ,iBAAkB,CAClB,UACF,CAEA,qBACE,MACF,CAEA,8BACE,qBAAsB,CACtB,kBACF,CAEA,4BACE,cACF,CAEA,4BACE,eACF,CAEA,kCACE,YAAa,CACb,qBAAsB,CACtB,cACF,CAEA,4EACE,gBACF,CAEA,uCACE,eAAgB,CAChB,gBAAiB,CACjB,aACF,CAEA,sEACE,eACF,CAEA,uCACE,cAAe,CACf,qBAAsB,CACtB,eAAiB,CACjB,iBACF,CAEA,kCACE,eACF",sourcesContent:[".container {\n  align-items: stretch;\n  display: flex;\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n\n.map {\n  flex: 1;\n}\n\n.sidebarGroup {\n  font: var(--main-font);\n  margin-bottom: 20px;\n}\n\n.showOption {\n  margin-top: 5px;\n}\n\n.activeSize {\n  font-weight: bold;\n}\n\n.foundModulesInfo {\n  display: flex;\n  font: var(--main-font);\n  margin: 8px 0 0;\n}\n\n.foundModulesInfoItem + .foundModulesInfoItem {\n  margin-left: 15px;\n}\n\n.foundModulesContainer {\n  margin-top: 15px;\n  max-height: 600px;\n  overflow: auto;\n}\n\n.foundModulesChunk + .foundModulesChunk {\n  margin-top: 15px;\n}\n\n.foundModulesChunkName {\n  cursor: pointer;\n  font: var(--main-font);\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.foundModulesList {\n  margin-left: 7px;\n}\n"],sourceRoot:""}]),a.locals={container:"ModulesTreemap__container",map:"ModulesTreemap__map",sidebarGroup:"ModulesTreemap__sidebarGroup",showOption:"ModulesTreemap__showOption",activeSize:"ModulesTreemap__activeSize",foundModulesInfo:"ModulesTreemap__foundModulesInfo",foundModulesInfoItem:"ModulesTreemap__foundModulesInfoItem",foundModulesContainer:"ModulesTreemap__foundModulesContainer",foundModulesChunk:"ModulesTreemap__foundModulesChunk",foundModulesChunkName:"ModulesTreemap__foundModulesChunkName",foundModulesList:"ModulesTreemap__foundModulesList"};const s=a},9976:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Search__container{font:var(--main-font);white-space:nowrap}.Search__label{font-weight:700;margin-bottom:7px}.Search__row{display:flex}.Search__input{border:1px solid #aaa;border-radius:4px;display:block;flex:1;padding:5px}.Search__clear{flex:0 0 auto;line-height:1;margin-left:3px;padding:5px 8px 7px}","",{version:3,sources:["webpack://./client/components/Search.css"],names:[],mappings:"AAAA,mBACE,qBAAsB,CACtB,kBACF,CAEA,eACE,eAAiB,CACjB,iBACF,CAEA,aACE,YACF,CAEA,eACE,qBAAsB,CACtB,iBAAkB,CAClB,aAAc,CACd,MAAO,CACP,WACF,CAEA,eACE,aAAc,CACd,aAAc,CACd,eAAgB,CAChB,mBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.row {\n  display: flex;\n}\n\n.input {\n  border: 1px solid #aaa;\n  border-radius: 4px;\n  display: block;\n  flex: 1;\n  padding: 5px;\n}\n\n.clear {\n  flex: 0 0 auto;\n  line-height: 1;\n  margin-left: 3px;\n  padding: 5px 8px 7px;\n}\n"],sourceRoot:""}]),a.locals={container:"Search__container",label:"Search__label",row:"Search__row",input:"Search__input",clear:"Search__clear"};const s=a},4826:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Sidebar__container{background:#fff;border:none;border-right:1px solid #aaa;box-sizing:border-box;max-width:calc(50% - 10px);opacity:.95;z-index:1}.Sidebar__container:not(.Sidebar__hidden){min-width:200px}.Sidebar__container:not(.Sidebar__pinned){bottom:0;position:absolute;top:0;transition:transform .2s ease}.Sidebar__container.Sidebar__pinned{position:relative}.Sidebar__container.Sidebar__left{left:0}.Sidebar__container.Sidebar__left.Sidebar__hidden{transform:translateX(calc(-100% + 7px))}.Sidebar__content{box-sizing:border-box;height:100%;overflow-y:auto;padding:25px 20px 20px;width:100%}.Sidebar__empty.Sidebar__pinned .Sidebar__content{padding:0}.Sidebar__pinButton,.Sidebar__toggleButton{cursor:pointer;height:26px;line-height:0;position:absolute;top:10px;width:27px}.Sidebar__pinButton{right:47px}.Sidebar__toggleButton{padding-left:6px;right:15px}.Sidebar__hidden .Sidebar__toggleButton{right:-35px;transition:transform .2s ease}.Sidebar__hidden .Sidebar__toggleButton:hover{transform:translateX(4px)}.Sidebar__resizer{bottom:0;cursor:col-resize;position:absolute;right:0;top:0;width:7px}","",{version:3,sources:["webpack://./client/components/Sidebar.css"],names:[],mappings:"AAEA,oBACE,eAAgB,CAEhB,WAA4B,CAA5B,2BAA4B,CAC5B,qBAAsB,CACtB,0BAA2B,CAC3B,WAAa,CACb,SACF,CAEA,0CACE,eACF,CAEA,0CACE,QAAS,CACT,iBAAkB,CAClB,KAAM,CACN,6BACF,CAEA,oCACE,iBACF,CAEA,kCACE,MACF,CAEA,kDACE,uCACF,CAEA,kBACE,qBAAsB,CACtB,WAAY,CACZ,eAAgB,CAChB,sBAAuB,CACvB,UACF,CAEA,kDACE,SACF,CAEA,2CAEE,cAAe,CACf,WAAY,CACZ,aAAc,CACd,iBAAkB,CAClB,QAAS,CACT,UACF,CAEA,oBACE,UACF,CAEA,uBACE,gBAAiB,CACjB,UACF,CAEA,wCACE,WAAY,CACZ,6BACF,CAEA,8CACE,yBACF,CAEA,kBACE,QAAS,CACT,iBAAkB,CAClB,iBAAkB,CAClB,OAAQ,CACR,KAAM,CACN,SACF",sourcesContent:["@value toggleTime: 200ms;\n\n.container {\n  background: #fff;\n  border: none;\n  border-right: 1px solid #aaa;\n  box-sizing: border-box;\n  max-width: calc(50% - 10px);\n  opacity: 0.95;\n  z-index: 1;\n}\n\n.container:not(.hidden) {\n  min-width: 200px;\n}\n\n.container:not(.pinned) {\n  bottom: 0;\n  position: absolute;\n  top: 0;\n  transition: transform toggleTime ease;\n}\n\n.container.pinned {\n  position: relative;\n}\n\n.container.left {\n  left: 0;\n}\n\n.container.left.hidden {\n  transform: translateX(calc(-100% + 7px));\n}\n\n.content {\n  box-sizing: border-box;\n  height: 100%;\n  overflow-y: auto;\n  padding: 25px 20px 20px;\n  width: 100%;\n}\n\n.empty.pinned .content {\n  padding: 0;\n}\n\n.pinButton,\n.toggleButton {\n  cursor: pointer;\n  height: 26px;\n  line-height: 0;\n  position: absolute;\n  top: 10px;\n  width: 27px;\n}\n\n.pinButton {\n  right: 47px;\n}\n\n.toggleButton {\n  padding-left: 6px;\n  right: 15px;\n}\n\n.hidden .toggleButton {\n  right: -35px;\n  transition: transform .2s ease;\n}\n\n.hidden .toggleButton:hover {\n  transform: translateX(4px);\n}\n\n.resizer {\n  bottom: 0;\n  cursor: col-resize;\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 7px;\n}\n"],sourceRoot:""}]),a.locals={toggleTime:".2s",container:"Sidebar__container",hidden:"Sidebar__hidden",pinned:"Sidebar__pinned",left:"Sidebar__left",content:"Sidebar__content",empty:"Sidebar__empty",pinButton:"Sidebar__pinButton",toggleButton:"Sidebar__toggleButton",resizer:"Sidebar__resizer"};const s=a},6897:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Switcher__container{font:var(--main-font);white-space:nowrap}.Switcher__label{font-size:11px;font-weight:700;margin-bottom:7px}.Switcher__item+.Switcher__item{margin-left:5px}","",{version:3,sources:["webpack://./client/components/Switcher.css"],names:[],mappings:"AAAA,qBACE,qBAAsB,CACtB,kBACF,CAEA,iBAEE,cAAe,CADf,eAAiB,CAEjB,iBACF,CAEA,gCACE,eACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-weight: bold;\n  font-size: 11px;\n  margin-bottom: 7px;\n}\n\n.item + .item {\n  margin-left: 5px;\n}\n"],sourceRoot:""}]),a.locals={container:"Switcher__container",label:"Switcher__label",item:"Switcher__item"};const s=a},7527:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,".Tooltip__container{background:#fff;border:1px solid #aaa;border-radius:4px;font:var(--main-font);opacity:.9;padding:5px 10px;position:absolute;transition:opacity .2s ease,visibility .2s ease;visibility:visible;white-space:nowrap}.Tooltip__hidden{opacity:0;visibility:hidden}","",{version:3,sources:["webpack://./client/components/Tooltip.css"],names:[],mappings:"AAAA,oBAKE,eAAgB,CAChB,qBAAsB,CAFtB,iBAAkB,CAHlB,qBAAsB,CAMtB,UAAY,CAJZ,gBAAiB,CADjB,iBAAkB,CAQlB,+CAAiD,CADjD,kBAAmB,CADnB,kBAGF,CAEA,iBACE,SAAU,CACV,iBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  position: absolute;\n  padding: 5px 10px;\n  border-radius: 4px;\n  background: #fff;\n  border: 1px solid #aaa;\n  opacity: 0.9;\n  white-space: nowrap;\n  visibility: visible;\n  transition: opacity .2s ease, visibility .2s ease;\n}\n\n.hidden {\n  opacity: 0;\n  visibility: hidden;\n}\n"],sourceRoot:""}]),a.locals={container:"Tooltip__container",hidden:"Tooltip__hidden"};const s=a},1194:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(4015),i=n.n(r),o=n(3645),a=n.n(o)()(i());a.push([e.id,":root{--main-font:normal 11px Verdana,sans-serif}#app,body,html{height:100%;margin:0;overflow:hidden;padding:0;width:100%}body.resizing{-webkit-user-select:none!important;user-select:none!important}body.resizing *{pointer-events:none}body.resizing.col{cursor:col-resize!important}","",{version:3,sources:["webpack://./client/viewer.css"],names:[],mappings:"AAAA,MACE,0CACF,CAEA,eAGE,WAAY,CACZ,QAAS,CACT,eAAgB,CAChB,SAAU,CACV,UACF,CAEA,cACE,kCAA4B,CAA5B,0BACF,CAEA,gBACE,mBACF,CAEA,kBACE,2BACF",sourcesContent:[":root {\n  --main-font: normal 11px Verdana, sans-serif;\n}\n\n:global html,\n:global body,\n:global #app {\n  height: 100%;\n  margin: 0;\n  overflow: hidden;\n  padding: 0;\n  width: 100%;\n}\n\n:global body.resizing {\n  user-select: none !important;\n}\n\n:global body.resizing * {\n  pointer-events: none;\n}\n\n:global body.resizing.col {\n  cursor: col-resize !important;\n}\n"],sourceRoot:""}]);const s=a},3645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(r)for(var o=0;o<this.length;o++){var a=this[o][0];null!=a&&(i[a]=!0)}for(var s=0;s<e.length;s++){var u=[].concat(e[s]);r&&i[u[0]]||(n&&(u[2]?u[2]="".concat(n," and ").concat(u[2]):u[2]=n),t.push(u))}},t}},4015:e=>{"use strict";function t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null==n)return;var r,i,o=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(u){s=!0,i=u}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}e.exports=function(e){var n=t(e,4),r=n[1],i=n[3];if("function"==typeof btoa){var o=btoa(unescape(encodeURIComponent(JSON.stringify(i)))),a="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(o),s="/*# ".concat(a," */"),u=i.sources.map((function(e){return"/*# sourceURL=".concat(i.sourceRoot||"").concat(e," */")}));return[r].concat(u).concat([s]).join("\n")}return[r].join("\n")}},1667:e=>{"use strict";e.exports=function(e,t){return t||(t={}),"string"!=typeof(e=e&&e.__esModule?e.default:e)?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},6755:function(e){e.exports=function(){"use strict";var e=/^(b|B)$/,t={iec:{bits:["b","Kib","Mib","Gib","Tib","Pib","Eib","Zib","Yib"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["b","Kb","Mb","Gb","Tb","Pb","Eb","Zb","Yb"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},n={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]},r={floor:Math.floor,ceil:Math.ceil};function i(i){var o,a,s,u,l,c,h,f,d,p,g,b,v,m,y,C,_,w,x,A,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},M=[],T=0;if(isNaN(i))throw new TypeError("Invalid number");if(s=!0===S.bits,y=!0===S.unix,b=!0===S.pad,a=S.base||2,v=void 0!==S.round?S.round:y?1:2,h=void 0!==S.locale?S.locale:"",f=S.localeOptions||{},C=void 0!==S.separator?S.separator:"",_=void 0!==S.spacer?S.spacer:y?"":" ",x=S.symbols||{},w=2===a&&S.standard||"jedec",g=S.output||"string",l=!0===S.fullform,c=S.fullforms instanceof Array?S.fullforms:[],o=void 0!==S.exponent?S.exponent:-1,A=r[S.roundingMethod]||Math.round,u=a>2?1e3:1024,(d=(p=Number(i))<0)&&(p=-p),(-1===o||isNaN(o))&&(o=Math.floor(Math.log(p)/Math.log(u)))<0&&(o=0),o>8&&(o=8),"exponent"===g)return o;if(0===p)M[0]=0,m=M[1]=y?"":t[w][s?"bits":"bytes"][o];else{T=p/(2===a?Math.pow(2,10*o):Math.pow(1e3,o)),s&&(T*=8)>=u&&o<8&&(T/=u,o++);var k=Math.pow(10,o>0?v:0);M[0]=A(T*k)/k,M[0]===u&&o<8&&void 0===S.exponent&&(M[0]=1,o++),m=M[1]=10===a&&1===o?s?"kb":"kB":t[w][s?"bits":"bytes"][o],y&&(M[1]="jedec"===w?M[1].charAt(0):o>0?M[1].replace(/B$/,""):M[1],e.test(M[1])&&(M[0]=Math.floor(M[0]),M[1]=""))}if(d&&(M[0]=-M[0]),M[1]=x[M[1]]||M[1],!0===h?M[0]=M[0].toLocaleString():h.length>0?M[0]=M[0].toLocaleString(h,f):C.length>0&&(M[0]=M[0].toString().replace(".",C)),b&&!1===Number.isInteger(M[0])&&v>0){var z=C||".",D=M[0].toString().split(z),j=D[1]||"",L=j.length,B=v-L;M[0]="".concat(D[0]).concat(z).concat(j.padEnd(L+B,"0"))}return l&&(M[1]=c[o]?c[o]:n[w][o]+(s?"bit":"byte")+(1===M[0]?"":"s")),"array"===g?M:"object"===g?{value:M[0],symbol:M[1],exponent:o,unit:m}:M.join(_)}return i.partial=function(e){return function(t){return i(t,e)}},i}()},2705:(e,t,n)=>{var r=n(5639).Symbol;e.exports=r},9932:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}},4286:e=>{e.exports=function(e){return e.split("")}},4239:(e,t,n)=>{var r=n(2705),i=n(9607),o=n(2333),a=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?i(e):o(e)}},8674:e=>{e.exports=function(e){return function(t){return null==e?void 0:e[t]}}},4259:e=>{e.exports=function(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var o=Array(i);++r<i;)o[r]=e[r+t];return o}},531:(e,t,n)=>{var r=n(2705),i=n(9932),o=n(1469),a=n(3448),s=r?r.prototype:void 0,u=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(o(t))return i(t,e)+"";if(a(t))return u?u.call(t):"";var n=t+"";return"0"==n&&1/t==-Infinity?"-0":n}},7561:(e,t,n)=>{var r=n(7990),i=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(i,""):e}},180:(e,t,n)=>{var r=n(4259);e.exports=function(e,t,n){var i=e.length;return n=void 0===n?i:n,!t&&n>=i?e:r(e,t,n)}},8805:(e,t,n)=>{var r=n(180),i=n(2689),o=n(3140),a=n(9833);e.exports=function(e){return function(t){t=a(t);var n=i(t)?o(t):void 0,s=n?n[0]:t.charAt(0),u=n?r(n,1).join(""):t.slice(1);return s[e]()+u}}},9464:(e,t,n)=>{var r=n(8674)({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});e.exports=r},1957:e=>{e.exports=!1},9607:(e,t,n)=>{var r=n(2705),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,s=r?r.toStringTag:void 0;e.exports=function(e){var t=o.call(e,s),n=e[s];try{e[s]=void 0;var r=!0}catch(u){}var i=a.call(e);return r&&(t?e[s]=n:delete e[s]),i}},2689:e=>{var t=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},2333:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},5639:(e,t,n)=>{var r=n(1957),i="object"==typeof self&&self&&self.Object===Object&&self,o=r||i||Function("return this")();e.exports=o},3140:(e,t,n)=>{var r=n(4286),i=n(2689),o=n(676);e.exports=function(e){return i(e)?o(e):r(e)}},7990:e=>{var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},676:e=>{var t="[\\ud800-\\udfff]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\\ud83c[\\udffb-\\udfff]",i="[^\\ud800-\\udfff]",o="(?:\\ud83c[\\udde6-\\uddff]){2}",a="[\\ud800-\\udbff][\\udc00-\\udfff]",s="(?:"+n+"|"+r+")"+"?",u="[\\ufe0e\\ufe0f]?",l=u+s+("(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+s+")*"),c="(?:"+[i+n+"?",n,o,a,t].join("|")+")",h=RegExp(r+"(?="+r+")|"+c+l,"g");e.exports=function(e){return e.match(h)||[]}},3279:(e,t,n)=>{var r=n(3218),i=n(7771),o=n(4841),a=Math.max,s=Math.min;e.exports=function(e,t,n){var u,l,c,h,f,d,p=0,g=!1,b=!1,v=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function m(t){var n=u,r=l;return u=l=void 0,p=t,h=e.apply(r,n)}function y(e){return p=e,f=setTimeout(_,t),g?m(e):h}function C(e){var n=e-d;return void 0===d||n>=t||n<0||b&&e-p>=c}function _(){var e=i();if(C(e))return w(e);f=setTimeout(_,function(e){var n=t-(e-d);return b?s(n,c-(e-p)):n}(e))}function w(e){return f=void 0,v&&u?m(e):(u=l=void 0,h)}function x(){var e=i(),n=C(e);if(u=arguments,l=this,d=e,n){if(void 0===f)return y(d);if(b)return clearTimeout(f),f=setTimeout(_,t),m(d)}return void 0===f&&(f=setTimeout(_,t)),h}return t=o(t)||0,r(n)&&(g=!!n.leading,c=(b="maxWait"in n)?a(o(n.maxWait)||0,t):c,v="trailing"in n?!!n.trailing:v),x.cancel=function(){void 0!==f&&clearTimeout(f),p=0,u=d=l=f=void 0},x.flush=function(){return void 0===f?h:w(i())},x}},7187:(e,t,n)=>{var r=n(9464),i=n(9833),o=/[&<>"']/g,a=RegExp(o.source);e.exports=function(e){return(e=i(e))&&a.test(e)?e.replace(o,r):e}},3522:(e,t,n)=>{var r=n(9833),i=/[\\^$.*+?()[\]{}|]/g,o=RegExp(i.source);e.exports=function(e){return(e=r(e))&&o.test(e)?e.replace(i,"\\$&"):e}},1469:e=>{var t=Array.isArray;e.exports=t},3218:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},7005:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},3448:(e,t,n)=>{var r=n(4239),i=n(7005);e.exports=function(e){return"symbol"==typeof e||i(e)&&"[object Symbol]"==r(e)}},7771:(e,t,n)=>{var r=n(5639);e.exports=function(){return r.Date.now()}},4841:(e,t,n)=>{var r=n(7561),i=n(3218),o=n(3448),a=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,l=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(o(e))return NaN;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=s.test(e);return n||u.test(e)?l(e.slice(2),n?2:8):a.test(e)?NaN:+e}},9833:(e,t,n)=>{var r=n(531);e.exports=function(e){return null==e?"":r(e)}},1700:(e,t,n)=>{var r=n(8805)("toUpperCase");e.exports=r},3379:(e,t,n)=>{"use strict";var r,i=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},o=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(r){n=null}e[t]=n}return e[t]}}(),a=[];function s(e){for(var t=-1,n=0;n<a.length;n++)if(a[n].identifier===e){t=n;break}return t}function u(e,t){for(var n={},r=[],i=0;i<e.length;i++){var o=e[i],u=t.base?o[0]+t.base:o[0],l=n[u]||0,c="".concat(u," ").concat(l);n[u]=l+1;var h=s(c),f={css:o[1],media:o[2],sourceMap:o[3]};-1!==h?(a[h].references++,a[h].updater(f)):a.push({identifier:c,updater:b(f,t),references:1}),r.push(c)}return r}function l(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var i=n.nc;i&&(r.nonce=i)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var c,h=(c=[],function(e,t){return c[e]=t,c.filter(Boolean).join("\n")});function f(e,t,n,r){var i=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=h(t,i);else{var o=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(o,a[t]):e.appendChild(o)}}function d(e,t,n){var r=n.css,i=n.media,o=n.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var p=null,g=0;function b(e,t){var n,r,i;if(t.singleton){var o=g++;n=p||(p=l(t)),r=f.bind(null,n,o,!1),i=f.bind(null,n,o,!0)}else n=l(t),r=d.bind(null,n,t),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=i());var n=u(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var i=s(n[r]);a[i].references--}for(var o=u(e,t),l=0;l<n.length;l++){var c=s(n[l]);0===a[c].references&&(a[c].updater(),a.splice(c,1))}n=o}}}},4150:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMCAwdjExLjI1YzAgLjQxNC4zMzYuNzUuNzUuNzVoMTAuNWEuNzUuNzUgMCAwIDAgLjc1LS43NVYwSDB6IiBmaWxsPSIjRkM2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNMCAwcy4xNTYgMyAxLjEyNSAzaDkuNzVDMTEuODQ1IDMgMTIgMCAxMiAwSDB6IiBmaWxsPSIjQ0NBMzUyIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNi43NSAxLjVoLS4zNzVMNiAyLjVsLS4zNzUtMUg1LjI1TDUuODEzIDMgNS4yNSA0LjVoLjM3NUw2IDMuNWwuMzc1IDFoLjM3NUw2LjE4NyAzeiIgZmlsbD0iIzk5N0EzRCIvPjxjaXJjbGUgY3g9Ii43NSIgY3k9Ii43NSIgcj0iMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNS4yNSAzLjc1KSIgZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PGNpcmNsZSBjeD0iLjc1IiBjeT0iLjc1IiByPSIxIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1LjI1IC43NSkiIGZpbGw9IiNGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvZz48L3N2Zz4="},8752:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSI+PHBhdGggZD0iTTExLjcgMS4zMzNINS44NUw0LjU1IDBIMS4zQy41ODUgMCAwIC42IDAgMS4zMzNWNGgxM1YyLjY2N2MwLS43MzMtLjU4NS0xLjMzNC0xLjMtMS4zMzR6IiBmaWxsPSIjRkZBMDAwIi8+PHBhdGggZD0iTTExLjcgMUgxLjNDLjU4NSAxIDAgMS41NzkgMCAyLjI4NnY2LjQyOEMwIDkuNDIxLjU4NSAxMCAxLjMgMTBoMTAuNGMuNzE1IDAgMS4zLS41NzkgMS4zLTEuMjg2VjIuMjg2QzEzIDEuNTc5IDEyLjQxNSAxIDExLjcgMXoiIGZpbGw9IiNGRkNBMjgiLz48L2c+PC9zdmc+"},8868:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjMyNy4wNjRMOC40MzMgMS45NTdhNi4wMjUgNi4wMjUgMCAwIDAtMS45NTItLjM0MkMyLjkxMiAxLjYxNS4wMTkgNS4xOTYuMDE5IDUuMTk2czEuMDk4IDEuMzU4IDIuNzc0IDIuNDAxTC45NiA5LjQzMWwuOTM2LjkzNkwxMS4yNjMgMWwtLjkzNi0uOTM2ek00LjA1IDYuMzRhMi42ODYgMi42ODYgMCAwIDEgMy41NzQtMy41NzRMNC4wNSA2LjM0em02LjQ0OC0zLjMzYTEyLjM0NCAxMi4zNDQgMCAwIDEgMi40NDQgMi4xODZzLTIuODkzIDMuNTgtNi40NjEgMy41OGMtLjUzIDAtMS4wNDQtLjA3OC0xLjUzNy0uMjEzbC43ODgtLjc4OEEyLjY4NCAyLjY4NCAwIDAgMCA5LjA2IDQuNDQ4bDEuNDM4LTEuNDM5eiIgZmlsbD0iIzIzMUYyMCIgZmlsbC1vcGFjaXR5PSIuNTk3Ii8+PC9zdmc+"},4911:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTEiIGhlaWdodD0iMTMiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNjI1IDBBMS42MyAxLjYzIDAgMCAwIDAgMS42MjV2OS43NUExLjYzIDEuNjMgMCAwIDAgMS42MjUgMTNoNy41ODNhMS42MyAxLjYzIDAgMCAwIDEuNjI1LTEuNjI1VjMuNTY3TDcuMjY2IDBIMS42MjV6bTAgMS4wODNINi41djMuMjVoMy4yNXY3LjA0MmEuNTM1LjUzNSAwIDAgMS0uNTQyLjU0MkgxLjYyNWEuNTM1LjUzNSAwIDAgMS0uNTQyLS41NDJ2LTkuNzVjMC0uMzA1LjIzNy0uNTQyLjU0Mi0uNTQyem01Ljk1OC43NjZMOC45ODQgMy4yNWgtMS40di0xLjR6TTMuMjUgNi41djEuMDgzaDQuMzMzVjYuNUgzLjI1em0wIDIuMTY3VjkuNzVINi41VjguNjY3SDMuMjV6IiBmaWxsLW9wYWNpdHk9Ii40MDMiLz48L3N2Zz4="}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={id:r,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e,t,r,i,o,a={},s=[],u=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function l(e,t){for(var n in t)e[n]=t[n];return e}function c(e){var t=e.parentNode;t&&t.removeChild(e)}function h(e,t,n){var r,i,o,a=arguments,s={};for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:s[o]=t[o];if(arguments.length>3)for(n=[n],o=3;o<arguments.length;o++)n.push(a[o]);if(null!=n&&(s.children=n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return f(e,s,r,i,null)}function f(t,n,r,i,o){var a={type:t,props:n,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++e.__v:o};return null!=e.vnode&&e.vnode(a),a}function d(e){return e.children}function p(e,t){this.props=e,this.context=t}function g(e,t){if(null==t)return e.__?g(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?g(e):null}function b(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return b(e)}}function v(n){(!n.__d&&(n.__d=!0)&&t.push(n)&&!m.__r++||i!==e.debounceRendering)&&((i=e.debounceRendering)||r)(m)}function m(){for(var e;m.__r=t.length;)e=t.sort((function(e,t){return e.__v.__b-t.__v.__b})),t=[],e.some((function(e){var t,n,r,i,o,a;e.__d&&(o=(i=(t=e).__v).__e,(a=t.__P)&&(n=[],(r=l({},i)).__v=i.__v+1,T(a,i,r,t.__n,void 0!==a.ownerSVGElement,null!=i.__h?[o]:null,n,null==o?g(i):o,i.__h),k(n,i),i.__e!=o&&b(i)))}))}function y(e,t,n,r,i,o,u,l,c,h){var p,b,v,m,y,_,x,A=r&&r.__k||s,S=A.length;for(n.__k=[],p=0;p<t.length;p++)if(null!=(m=n.__k[p]=null==(m=t[p])||"boolean"==typeof m?null:"string"==typeof m||"number"==typeof m||"bigint"==typeof m?f(null,m,null,null,m):Array.isArray(m)?f(d,{children:m},null,null,null):m.__b>0?f(m.type,m.props,m.key,null,m.__v):m)){if(m.__=n,m.__b=n.__b+1,null===(v=A[p])||v&&m.key==v.key&&m.type===v.type)A[p]=void 0;else for(b=0;b<S;b++){if((v=A[b])&&m.key==v.key&&m.type===v.type){A[b]=void 0;break}v=null}T(e,m,v=v||a,i,o,u,l,c,h),y=m.__e,(b=m.ref)&&v.ref!=b&&(x||(x=[]),v.ref&&x.push(v.ref,null,m),x.push(b,m.__c||y,m)),null!=y?(null==_&&(_=y),"function"==typeof m.type&&null!=m.__k&&m.__k===v.__k?m.__d=c=C(m,c,e):c=w(e,m,v,A,y,c),h||"option"!==n.type?"function"==typeof n.type&&(n.__d=c):e.value=""):c&&v.__e==c&&c.parentNode!=e&&(c=g(v))}for(n.__e=_,p=S;p--;)null!=A[p]&&("function"==typeof n.type&&null!=A[p].__e&&A[p].__e==n.__d&&(n.__d=g(r,p+1)),j(A[p],A[p]));if(x)for(p=0;p<x.length;p++)D(x[p],x[++p],x[++p])}function C(e,t,n){var r,i;for(r=0;r<e.__k.length;r++)(i=e.__k[r])&&(i.__=e,t="function"==typeof i.type?C(i,t,n):w(n,i,i,e.__k,i.__e,t));return t}function _(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some((function(e){_(e,t)})):t.push(e)),t}function w(e,t,n,r,i,o){var a,s,u;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||i!=o||null==i.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(i),a=null;else{for(s=o,u=0;(s=s.nextSibling)&&u<r.length;u+=2)if(s==i)break e;e.insertBefore(i,o),a=o}return void 0!==a?a:i.nextSibling}function x(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||u.test(t)?n:n+"px"}function A(e,t,n,r,i){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||x(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||x(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r||e.addEventListener(t,o?M:S,o):e.removeEventListener(t,o?M:S,o);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function S(t){this.l[t.type+!1](e.event?e.event(t):t)}function M(t){this.l[t.type+!0](e.event?e.event(t):t)}function T(t,n,r,i,o,a,s,u,c){var h,f,g,b,v,m,C,_,w,x,A,S=n.type;if(void 0!==n.constructor)return null;null!=r.__h&&(c=r.__h,u=n.__e=r.__e,n.__h=null,a=[u]),(h=e.__b)&&h(n);try{e:if("function"==typeof S){if(_=n.props,w=(h=S.contextType)&&i[h.__c],x=h?w?w.props.value:h.__:i,r.__c?C=(f=n.__c=r.__c).__=f.__E:("prototype"in S&&S.prototype.render?n.__c=f=new S(_,x):(n.__c=f=new p(_,x),f.constructor=S,f.render=L),w&&w.sub(f),f.props=_,f.state||(f.state={}),f.context=x,f.__n=i,g=f.__d=!0,f.__h=[]),null==f.__s&&(f.__s=f.state),null!=S.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=l({},f.__s)),l(f.__s,S.getDerivedStateFromProps(_,f.__s))),b=f.props,v=f.state,g)null==S.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==S.getDerivedStateFromProps&&_!==b&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(_,x),!f.__e&&null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(_,f.__s,x)||n.__v===r.__v){f.props=_,f.state=f.__s,n.__v!==r.__v&&(f.__d=!1),f.__v=n,n.__e=r.__e,n.__k=r.__k,n.__k.forEach((function(e){e&&(e.__=n)})),f.__h.length&&s.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(_,f.__s,x),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(b,v,m)}))}f.context=x,f.props=_,f.state=f.__s,(h=e.__r)&&h(n),f.__d=!1,f.__v=n,f.__P=t,h=f.render(f.props,f.state,f.context),f.state=f.__s,null!=f.getChildContext&&(i=l(l({},i),f.getChildContext())),g||null==f.getSnapshotBeforeUpdate||(m=f.getSnapshotBeforeUpdate(b,v)),A=null!=h&&h.type===d&&null==h.key?h.props.children:h,y(t,Array.isArray(A)?A:[A],n,r,i,o,a,s,u,c),f.base=n.__e,n.__h=null,f.__h.length&&s.push(f),C&&(f.__E=f.__=null),f.__e=!1}else null==a&&n.__v===r.__v?(n.__k=r.__k,n.__e=r.__e):n.__e=z(r.__e,n,r,i,o,a,s,c);(h=e.diffed)&&h(n)}catch(t){n.__v=null,(c||null!=a)&&(n.__e=u,n.__h=!!c,a[a.indexOf(u)]=null),e.__e(t,n,r)}}function k(t,n){e.__c&&e.__c(n,t),t.some((function(n){try{t=n.__h,n.__h=[],t.some((function(e){e.call(n)}))}catch(t){e.__e(t,n.__v)}}))}function z(e,t,n,r,i,o,u,l){var h,f,d,p,g=n.props,b=t.props,v=t.type,m=0;if("svg"===v&&(i=!0),null!=o)for(;m<o.length;m++)if((h=o[m])&&(h===e||(v?h.localName==v:3==h.nodeType))){e=h,o[m]=null;break}if(null==e){if(null===v)return document.createTextNode(b);e=i?document.createElementNS("http://www.w3.org/2000/svg",v):document.createElement(v,b.is&&b),o=null,l=!1}if(null===v)g===b||l&&e.data===b||(e.data=b);else{if(o=o&&s.slice.call(e.childNodes),f=(g=n.props||a).dangerouslySetInnerHTML,d=b.dangerouslySetInnerHTML,!l){if(null!=o)for(g={},p=0;p<e.attributes.length;p++)g[e.attributes[p].name]=e.attributes[p].value;(d||f)&&(d&&(f&&d.__html==f.__html||d.__html===e.innerHTML)||(e.innerHTML=d&&d.__html||""))}if(function(e,t,n,r,i){var o;for(o in n)"children"===o||"key"===o||o in t||A(e,o,null,n[o],r);for(o in t)i&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||n[o]===t[o]||A(e,o,t[o],n[o],r)}(e,b,g,i,l),d)t.__k=[];else if(m=t.props.children,y(e,Array.isArray(m)?m:[m],t,n,r,i&&"foreignObject"!==v,o,u,e.firstChild,l),null!=o)for(m=o.length;m--;)null!=o[m]&&c(o[m]);l||("value"in b&&void 0!==(m=b.value)&&(m!==e.value||"progress"===v&&!m)&&A(e,"value",m,g.value,!1),"checked"in b&&void 0!==(m=b.checked)&&m!==e.checked&&A(e,"checked",m,g.checked,!1))}return e}function D(t,n,r){try{"function"==typeof t?t(n):t.current=n}catch(t){e.__e(t,r)}}function j(t,n,r){var i,o,a;if(e.unmount&&e.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||D(i,null,n)),r||"function"==typeof t.type||(r=null!=(o=t.__e)),t.__e=t.__d=void 0,null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(t){e.__e(t,n)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&j(i[a],n,r);null!=o&&c(o)}function L(e,t,n){return this.constructor(e,n)}function B(t,n,r){var i,o,u;e.__&&e.__(t,n),o=(i="function"==typeof r)?null:r&&r.__k||n.__k,u=[],T(n,t=(!i&&r||n).__k=h(d,null,[t]),o||a,a,void 0!==n.ownerSVGElement,!i&&r?[r]:o?null:n.firstChild?s.slice.call(n.childNodes):null,u,!i&&r?r:o?o.__e:n.firstChild,i),k(u,t)}function E(e,t){B(e,t,E)}function O(e,t,n){var r,i,o,a=arguments,s=l({},e.props);for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:s[o]=t[o];if(arguments.length>3)for(n=[n],o=3;o<arguments.length;o++)n.push(a[o]);return null!=n&&(s.children=n),f(e.type,s,r||e.key,i||e.ref,null)}function I(e,t,n,r){n&&Object.defineProperty(e,t,{enumerable:n.enumerable,configurable:n.configurable,writable:n.writable,value:n.initializer?n.initializer.call(r):void 0})}function N(e,t,n,r,i){var o={};return Object.keys(r).forEach((function(e){o[e]=r[e]})),o.enumerable=!!o.enumerable,o.configurable=!!o.configurable,("value"in o||o.initializer)&&(o.writable=!0),o=n.slice().reverse().reduce((function(n,r){return r(e,t,n)||n}),o),i&&void 0!==o.initializer&&(o.value=o.initializer?o.initializer.call(i):void 0,o.initializer=void 0),void 0===o.initializer&&(Object.defineProperty(e,t,o),o=null),o}e={__e:function(e,t){for(var n,r,i;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),i=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),i=n.__d),i)return n.__E=n}catch(t){e=t}throw e},__v:0},p.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=l({},this.state),"function"==typeof e&&(e=e(l({},n),this.props)),e&&l(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),v(this))},p.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),v(this))},p.prototype.render=d,t=[],r="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,m.__r=0,o=0;const P=[];Object.freeze(P);const F={};function R(){return++ot.mobxGuid}function G(e){throw U(!1,e),"X"}function U(e,t){if(!e)throw new Error("[mobx] "+(t||"An invariant failed, however the error is obfuscated because this is a production build."))}Object.freeze(F);function H(e){let t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}const V=()=>{};function q(e){return null!==e&&"object"==typeof e}function W(e){if(null===e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return t===Object.prototype||null===t}function Z(e,t,n){Object.defineProperty(e,t,{enumerable:!1,writable:!0,configurable:!0,value:n})}function $(e,t){const n="isMobX"+e;return t.prototype[n]=!0,function(e){return q(e)&&!0===e[n]}}function K(e){return e instanceof Map}function Y(e){return e instanceof Set}function J(e){const t=new Set;for(let n in e)t.add(n);return Object.getOwnPropertySymbols(e).forEach((n=>{Object.getOwnPropertyDescriptor(e,n).enumerable&&t.add(n)})),Array.from(t)}function X(e){return e&&e.toString?e.toString():new String(e).toString()}function Q(e){return null===e?null:"object"==typeof e?""+e:e}const ee="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols?e=>Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e)):Object.getOwnPropertyNames,te=Symbol("mobx administration");class ne{constructor(e="Atom@"+R()){this.name=e,this.isPendingUnobservation=!1,this.isBeingObserved=!1,this.observers=new Set,this.diffValue=0,this.lastAccessedBy=0,this.lowestObserverState=De.NOT_TRACKING}onBecomeObserved(){this.onBecomeObservedListeners&&this.onBecomeObservedListeners.forEach((e=>e()))}onBecomeUnobserved(){this.onBecomeUnobservedListeners&&this.onBecomeUnobservedListeners.forEach((e=>e()))}reportObserved(){return ht(this)}reportChanged(){lt(),function(e){if(e.lowestObserverState===De.STALE)return;e.lowestObserverState=De.STALE,e.observers.forEach((t=>{t.dependenciesState===De.UP_TO_DATE&&(t.isTracing!==je.NONE&&ft(t,e),t.onBecomeStale()),t.dependenciesState=De.STALE}))}(this),ct()}toString(){return this.name}}const re=$("Atom",ne);function ie(e,t=V,n=V){const r=new ne(e);var i;return t!==V&&Dt("onBecomeObserved",r,t,i),n!==V&&zt(r,n),r}const oe={identity:function(e,t){return e===t},structural:function(e,t){return _n(e,t)},default:function(e,t){return Object.is(e,t)},shallow:function(e,t){return _n(e,t,1)}},ae=Symbol("mobx did run lazy initializers"),se=Symbol("mobx pending decorators"),ue={},le={};function ce(e,t){const n=t?ue:le;return n[e]||(n[e]={configurable:!0,enumerable:t,get(){return he(this),this[e]},set(t){he(this),this[e]=t}})}function he(e){if(!0===e[ae])return;const t=e[se];if(t){Z(e,ae,!0);const n=[...Object.getOwnPropertySymbols(t),...Object.keys(t)];for(const r of n){const n=t[r];n.propertyCreator(e,n.prop,n.descriptor,n.decoratorTarget,n.decoratorArguments)}}}function fe(e,t){return function(){let n;const r=function(r,i,o,a){if(!0===a)return t(r,i,o,r,n),null;if(!Object.prototype.hasOwnProperty.call(r,se)){const e=r[se];Z(r,se,Object.assign({},e))}return r[se][i]={prop:i,propertyCreator:t,descriptor:o,decoratorTarget:r,decoratorArguments:n},ce(i,e)};return de(arguments)?(n=P,r.apply(null,arguments)):(n=Array.prototype.slice.call(arguments),r)}}function de(e){return(2===e.length||3===e.length)&&("string"==typeof e[1]||"symbol"==typeof e[1])||4===e.length&&!0===e[3]}function pe(e,t,n){return Ft(e)?e:Array.isArray(e)?Se.array(e,{name:n}):W(e)?Se.object(e,void 0,{name:n}):K(e)?Se.map(e,{name:n}):Y(e)?Se.set(e,{name:n}):e}function ge(e){return e}function be(e){U(e);const t=fe(!0,((t,n,r,i,o)=>{const a=r?r.initializer?r.initializer.call(t):r.value:void 0;fn(t).addObservableProp(n,a,e)})),n=t;return n.enhancer=e,n}const ve={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};function me(e){return null==e?ve:"string"==typeof e?{name:e,deep:!0,proxy:!0}:e}Object.freeze(ve);const ye=be(pe),Ce=be((function(e,t,n){return null==e||vn(e)||tn(e)||an(e)||cn(e)?e:Array.isArray(e)?Se.array(e,{name:n,deep:!1}):W(e)?Se.object(e,void 0,{name:n,deep:!1}):K(e)?Se.map(e,{name:n,deep:!1}):Y(e)?Se.set(e,{name:n,deep:!1}):G(!1)})),_e=be(ge),we=be((function(e,t,n){return _n(e,t)?t:e}));function xe(e){return e.defaultDecorator?e.defaultDecorator.enhancer:!1===e.deep?ge:pe}const Ae={box(e,t){arguments.length>2&&Me("box");const n=me(t);return new Je(e,xe(n),n.name,!0,n.equals)},array(e,t){arguments.length>2&&Me("array");const n=me(t);return function(e,t,n="ObservableArray@"+R(),r=!1){const i=new Xt(n,t,r);o=i.values,a=te,s=i,Object.defineProperty(o,a,{enumerable:!1,writable:!1,configurable:!0,value:s});var o,a,s;const u=new Proxy(i.values,Jt);if(i.proxy=u,e&&e.length){const t=Ke(!0);i.spliceWithArray(0,0,e),Ye(t)}return u}(e,xe(n),n.name)},map(e,t){arguments.length>2&&Me("map");const n=me(t);return new on(e,xe(n),n.name)},set(e,t){arguments.length>2&&Me("set");const n=me(t);return new ln(e,xe(n),n.name)},object(e,t,n){"string"==typeof arguments[1]&&Me("object");const r=me(n);if(!1===r.proxy)return Lt({},e,t,r);{const n=Bt(r),i=function(e){const t=new Proxy(e,Vt);return e[te].proxy=t,t}(Lt({},void 0,void 0,r));return Et(i,e,t,n),i}},ref:_e,shallow:Ce,deep:ye,struct:we},Se=function(e,t,n){if("string"==typeof arguments[1]||"symbol"==typeof arguments[1])return ye.apply(null,arguments);if(Ft(e))return e;const r=W(e)?Se.object(e,t,n):Array.isArray(e)?Se.array(e,t):K(e)?Se.map(e,t):Y(e)?Se.set(e,t):e;if(r!==e)return r;G(!1)};function Me(e){G(`Expected one or two arguments to observable.${e}. Did you accidentally try to use observable.${e} as decorator?`)}Object.keys(Ae).forEach((e=>Se[e]=Ae[e]));const Te=fe(!1,((e,t,n,r,i)=>{const{get:o,set:a}=n,s=i[0]||{};fn(e).addComputedProp(e,t,Object.assign({get:o,set:a,context:e},s))})),ke=Te({equals:oe.structural}),ze=function(e,t,n){if("string"==typeof t)return Te.apply(null,arguments);if(null!==e&&"object"==typeof e&&1===arguments.length)return Te.apply(null,arguments);const r="object"==typeof t?t:{};return r.get=e,r.set="function"==typeof t?t:r.set,r.name=r.name||e.name||"",new Xe(r)};var De,je;ze.struct=ke,function(e){e[e.NOT_TRACKING=-1]="NOT_TRACKING",e[e.UP_TO_DATE=0]="UP_TO_DATE",e[e.POSSIBLY_STALE=1]="POSSIBLY_STALE",e[e.STALE=2]="STALE"}(De||(De={})),function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"}(je||(je={}));class Le{constructor(e){this.cause=e}}function Be(e){return e instanceof Le}function Ee(e){switch(e.dependenciesState){case De.UP_TO_DATE:return!1;case De.NOT_TRACKING:case De.STALE:return!0;case De.POSSIBLY_STALE:{const t=Ge(!0),n=Fe(),r=e.observing,i=r.length;for(let o=0;o<i;o++){const i=r[o];if(Qe(i)){if(ot.disableErrorBoundaries)i.get();else try{i.get()}catch(u){return Re(n),Ue(t),!0}if(e.dependenciesState===De.STALE)return Re(n),Ue(t),!0}}return He(e),Re(n),Ue(t),!1}}}function Oe(e){const t=e.observers.size>0;ot.computationDepth>0&&t&&G(!1),ot.allowStateChanges||!t&&"strict"!==ot.enforceActions||G(!1)}function Ie(e,t,n){const r=Ge(!0);He(e),e.newObserving=new Array(e.observing.length+100),e.unboundDepsCount=0,e.runId=++ot.runId;const i=ot.trackingDerivation;let o;if(ot.trackingDerivation=e,!0===ot.disableErrorBoundaries)o=t.call(n);else try{o=t.call(n)}catch(u){o=new Le(u)}return ot.trackingDerivation=i,function(e){const t=e.observing,n=e.observing=e.newObserving;let r=De.UP_TO_DATE,i=0,o=e.unboundDepsCount;for(let a=0;a<o;a++){const e=n[a];0===e.diffValue&&(e.diffValue=1,i!==a&&(n[i]=e),i++),e.dependenciesState>r&&(r=e.dependenciesState)}n.length=i,e.newObserving=null,o=t.length;for(;o--;){const n=t[o];0===n.diffValue&&st(n,e),n.diffValue=0}for(;i--;){const t=n[i];1===t.diffValue&&(t.diffValue=0,at(t,e))}r!==De.UP_TO_DATE&&(e.dependenciesState=r,e.onBecomeStale())}(e),Ue(r),o}function Ne(e){const t=e.observing;e.observing=[];let n=t.length;for(;n--;)st(t[n],e);e.dependenciesState=De.NOT_TRACKING}function Pe(e){const t=Fe();try{return e()}finally{Re(t)}}function Fe(){const e=ot.trackingDerivation;return ot.trackingDerivation=null,e}function Re(e){ot.trackingDerivation=e}function Ge(e){const t=ot.allowStateReads;return ot.allowStateReads=e,t}function Ue(e){ot.allowStateReads=e}function He(e){if(e.dependenciesState===De.UP_TO_DATE)return;e.dependenciesState=De.UP_TO_DATE;const t=e.observing;let n=t.length;for(;n--;)t[n].lowestObserverState=De.UP_TO_DATE}let Ve=0,qe=1;const We=Object.getOwnPropertyDescriptor((()=>{}),"name");We&&We.configurable;function Ze(e,t,n){const r=function(){return $e(e,t,n||this,arguments)};return r.isMobxAction=!0,r}function $e(e,t,n,r){const i=function(e,t,n){const r=!1;let i=0;0;const o=Fe();lt();const a=Ke(!0),s=Ge(!0),u={prevDerivation:o,prevAllowStateChanges:a,prevAllowStateReads:s,notifySpy:r,startTime:i,actionId:qe++,parentActionId:Ve};return Ve=u.actionId,u}();try{return t.apply(n,r)}catch(o){throw i.error=o,o}finally{!function(e){Ve!==e.actionId&&G("invalid action stack. did you forget to finish an action?");Ve=e.parentActionId,void 0!==e.error&&(ot.suppressReactionErrors=!0);Ye(e.prevAllowStateChanges),Ue(e.prevAllowStateReads),ct(),Re(e.prevDerivation),e.notifySpy&&!1;ot.suppressReactionErrors=!1}(i)}}function Ke(e){const t=ot.allowStateChanges;return ot.allowStateChanges=e,t}function Ye(e){ot.allowStateChanges=e}class Je extends ne{constructor(e,t,n="ObservableValue@"+R(),r=!0,i=oe.default){super(n),this.enhancer=t,this.name=n,this.equals=i,this.hasUnreportedChange=!1,this.value=t(e,void 0,n)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}set(e){this.value;if((e=this.prepareNewValue(e))!==ot.UNCHANGED){0,this.setNewValue(e)}}prepareNewValue(e){if(Oe(this),qt(this)){const t=Zt(this,{object:this,type:"update",newValue:e});if(!t)return ot.UNCHANGED;e=t.newValue}return e=this.enhancer(e,this.value,this.name),this.equals(this.value,e)?ot.UNCHANGED:e}setNewValue(e){const t=this.value;this.value=e,this.reportChanged(),$t(this)&&Yt(this,{type:"update",object:this,newValue:e,oldValue:t})}get(){return this.reportObserved(),this.dehanceValue(this.value)}intercept(e){return Wt(this,e)}observe(e,t){return t&&e({object:this,type:"update",newValue:this.value,oldValue:void 0}),Kt(this,e)}toJSON(){return this.get()}toString(){return`${this.name}[${this.value}]`}valueOf(){return Q(this.get())}[Symbol.toPrimitive](){return this.valueOf()}}$("ObservableValue",Je);class Xe{constructor(e){this.dependenciesState=De.NOT_TRACKING,this.observing=[],this.newObserving=null,this.isBeingObserved=!1,this.isPendingUnobservation=!1,this.observers=new Set,this.diffValue=0,this.runId=0,this.lastAccessedBy=0,this.lowestObserverState=De.UP_TO_DATE,this.unboundDepsCount=0,this.__mapid="#"+R(),this.value=new Le(null),this.isComputing=!1,this.isRunningSetter=!1,this.isTracing=je.NONE,U(e.get,"missing option for computed: get"),this.derivation=e.get,this.name=e.name||"ComputedValue@"+R(),e.set&&(this.setter=Ze(this.name+"-setter",e.set)),this.equals=e.equals||(e.compareStructural||e.struct?oe.structural:oe.default),this.scope=e.context,this.requiresReaction=!!e.requiresReaction,this.keepAlive=!!e.keepAlive}onBecomeStale(){!function(e){if(e.lowestObserverState!==De.UP_TO_DATE)return;e.lowestObserverState=De.POSSIBLY_STALE,e.observers.forEach((t=>{t.dependenciesState===De.UP_TO_DATE&&(t.dependenciesState=De.POSSIBLY_STALE,t.isTracing!==je.NONE&&ft(t,e),t.onBecomeStale())}))}(this)}onBecomeObserved(){this.onBecomeObservedListeners&&this.onBecomeObservedListeners.forEach((e=>e()))}onBecomeUnobserved(){this.onBecomeUnobservedListeners&&this.onBecomeUnobservedListeners.forEach((e=>e()))}get(){this.isComputing&&G(`Cycle detected in computation ${this.name}: ${this.derivation}`),0!==ot.inBatch||0!==this.observers.size||this.keepAlive?(ht(this),Ee(this)&&this.trackAndCompute()&&function(e){if(e.lowestObserverState===De.STALE)return;e.lowestObserverState=De.STALE,e.observers.forEach((t=>{t.dependenciesState===De.POSSIBLY_STALE?t.dependenciesState=De.STALE:t.dependenciesState===De.UP_TO_DATE&&(e.lowestObserverState=De.UP_TO_DATE)}))}(this)):Ee(this)&&(this.warnAboutUntrackedRead(),lt(),this.value=this.computeValue(!1),ct());const e=this.value;if(Be(e))throw e.cause;return e}peek(){const e=this.computeValue(!1);if(Be(e))throw e.cause;return e}set(e){if(this.setter){U(!this.isRunningSetter,`The setter of computed value '${this.name}' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?`),this.isRunningSetter=!0;try{this.setter.call(this.scope,e)}finally{this.isRunningSetter=!1}}else U(!1,!1)}trackAndCompute(){const e=this.value,t=this.dependenciesState===De.NOT_TRACKING,n=this.computeValue(!0),r=t||Be(e)||Be(n)||!this.equals(e,n);return r&&(this.value=n),r}computeValue(e){let t;if(this.isComputing=!0,ot.computationDepth++,e)t=Ie(this,this.derivation,this.scope);else if(!0===ot.disableErrorBoundaries)t=this.derivation.call(this.scope);else try{t=this.derivation.call(this.scope)}catch(u){t=new Le(u)}return ot.computationDepth--,this.isComputing=!1,t}suspend(){this.keepAlive||(Ne(this),this.value=void 0)}observe(e,t){let n,r=!0;return Mt((()=>{let i=this.get();if(!r||t){const t=Fe();e({type:"update",object:this,newValue:i,oldValue:n}),Re(t)}r=!1,n=i}))}warnAboutUntrackedRead(){}toJSON(){return this.get()}toString(){return`${this.name}[${this.derivation.toString()}]`}valueOf(){return Q(this.get())}[Symbol.toPrimitive](){return this.valueOf()}}const Qe=$("ComputedValue",Xe);class et{constructor(){this.version=5,this.UNCHANGED={},this.trackingDerivation=null,this.computationDepth=0,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!0,this.allowStateReads=!0,this.enforceActions=!1,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.computedConfigurable=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1}}const tt={};function nt(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:tt}let rt=!0,it=!1,ot=function(){const e=nt();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(rt=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==(new et).version&&(rt=!1),rt?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new et):(setTimeout((()=>{it||G("There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`")}),1),new et)}();function at(e,t){e.observers.add(t),e.lowestObserverState>t.dependenciesState&&(e.lowestObserverState=t.dependenciesState)}function st(e,t){e.observers.delete(t),0===e.observers.size&&ut(e)}function ut(e){!1===e.isPendingUnobservation&&(e.isPendingUnobservation=!0,ot.pendingUnobservations.push(e))}function lt(){ot.inBatch++}function ct(){if(0==--ot.inBatch){bt();const e=ot.pendingUnobservations;for(let t=0;t<e.length;t++){const n=e[t];n.isPendingUnobservation=!1,0===n.observers.size&&(n.isBeingObserved&&(n.isBeingObserved=!1,n.onBecomeUnobserved()),n instanceof Xe&&n.suspend())}ot.pendingUnobservations=[]}}function ht(e){const t=ot.trackingDerivation;return null!==t?(t.runId!==e.lastAccessedBy&&(e.lastAccessedBy=t.runId,t.newObserving[t.unboundDepsCount++]=e,e.isBeingObserved||(e.isBeingObserved=!0,e.onBecomeObserved())),!0):(0===e.observers.size&&ot.inBatch>0&&ut(e),!1)}function ft(e,t){if(console.log(`[mobx.trace] '${e.name}' is invalidated due to a change in: '${t.name}'`),e.isTracing===je.BREAK){const n=[];dt(Ot(e),n,1),new Function(`debugger;\n/*\nTracing '${e.name}'\n\nYou are entering this break point because derivation '${e.name}' is being traced and '${t.name}' is now forcing it to update.\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\n\n${e instanceof Xe?e.derivation.toString().replace(/[*]\//g,"/"):""}\n\nThe dependencies for this derivation are:\n\n${n.join("\n")}\n*/\n    `)()}}function dt(e,t,n){t.length>=1e3?t.push("(and many more)"):(t.push(`${new Array(n).join("\t")}${e.name}`),e.dependencies&&e.dependencies.forEach((e=>dt(e,t,n+1))))}class pt{constructor(e="Reaction@"+R(),t,n,r=!1){this.name=e,this.onInvalidate=t,this.errorHandler=n,this.requiresObservable=r,this.observing=[],this.newObserving=[],this.dependenciesState=De.NOT_TRACKING,this.diffValue=0,this.runId=0,this.unboundDepsCount=0,this.__mapid="#"+R(),this.isDisposed=!1,this._isScheduled=!1,this._isTrackPending=!1,this._isRunning=!1,this.isTracing=je.NONE}onBecomeStale(){this.schedule()}schedule(){this._isScheduled||(this._isScheduled=!0,ot.pendingReactions.push(this),bt())}isScheduled(){return this._isScheduled}runReaction(){if(!this.isDisposed){if(lt(),this._isScheduled=!1,Ee(this)){this._isTrackPending=!0;try{this.onInvalidate(),this._isTrackPending}catch(u){this.reportExceptionInDerivation(u)}}ct()}}track(e){if(this.isDisposed)return;lt();this._isRunning=!0;const t=Ie(this,e,void 0);this._isRunning=!1,this._isTrackPending=!1,this.isDisposed&&Ne(this),Be(t)&&this.reportExceptionInDerivation(t.cause),ct()}reportExceptionInDerivation(e){if(this.errorHandler)return void this.errorHandler(e,this);if(ot.disableErrorBoundaries)throw e;const t=`[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '${this}'`;ot.suppressReactionErrors?console.warn(`[mobx] (error in reaction '${this.name}' suppressed, fix error of causing action below)`):console.error(t,e),ot.globalReactionErrorHandlers.forEach((t=>t(e,this)))}dispose(){this.isDisposed||(this.isDisposed=!0,this._isRunning||(lt(),Ne(this),ct()))}getDisposer(){const e=this.dispose.bind(this);return e[te]=this,e}toString(){return`Reaction[${this.name}]`}trace(e=!1){!function(...e){let t=!1;"boolean"==typeof e[e.length-1]&&(t=e.pop());const n=function(e){switch(e.length){case 0:return ot.trackingDerivation;case 1:return mn(e[0]);case 2:return mn(e[0],e[1])}}(e);if(!n)return G(!1);n.isTracing===je.NONE&&console.log(`[mobx.trace] '${n.name}' tracing enabled`);n.isTracing=t?je.BREAK:je.LOG}(this,e)}}let gt=e=>e();function bt(){ot.inBatch>0||ot.isRunningReactions||gt(vt)}function vt(){ot.isRunningReactions=!0;const e=ot.pendingReactions;let t=0;for(;e.length>0;){100==++t&&(console.error(`Reaction doesn't converge to a stable state after 100 iterations. Probably there is a cycle in the reactive function: ${e[0]}`),e.splice(0));let n=e.splice(0);for(let e=0,t=n.length;e<t;e++)n[e].runReaction()}ot.isRunningReactions=!1}const mt=$("Reaction",pt);function yt(e){const t=gt;gt=n=>e((()=>t(n)))}function Ct(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}}function _t(){G(!1)}function wt(e){return function(t,n,r){if(r){if(r.value)return{value:Ze(e,r.value),enumerable:!1,configurable:!0,writable:!0};const{initializer:t}=r;return{enumerable:!1,configurable:!0,writable:!0,initializer(){return Ze(e,t.call(this))}}}return xt(e).apply(this,arguments)}}function xt(e){return function(t,n,r){Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get(){},set(t){Z(this,n,At(e,t))}})}}const At=function(e,t,n,r){return 1===arguments.length&&"function"==typeof e?Ze(e.name||"<unnamed action>",e):2===arguments.length&&"function"==typeof t?Ze(e,t):1===arguments.length&&"string"==typeof e?wt(e):!0!==r?wt(t).apply(null,arguments):void Z(e,t,Ze(e.name||t,n.value,this))};function St(e,t,n){Z(e,t,Ze(t,n.bind(e)))}function Mt(e,t=F){const n=t&&t.name||e.name||"Autorun@"+R();let r;if(!t.scheduler&&!t.delay)r=new pt(n,(function(){this.track(i)}),t.onError,t.requiresObservable);else{const e=kt(t);let o=!1;r=new pt(n,(()=>{o||(o=!0,e((()=>{o=!1,r.isDisposed||r.track(i)})))}),t.onError,t.requiresObservable)}function i(){e(r)}return r.schedule(),r.getDisposer()}At.bound=function(e,t,n,r){return!0===r?(St(e,t,n.value),null):n?{configurable:!0,enumerable:!1,get(){return St(this,t,n.value||n.initializer.call(this)),this[t]},set:_t}:{enumerable:!1,configurable:!0,set(e){St(this,t,e)},get(){}}};const Tt=e=>e();function kt(e){return e.scheduler?e.scheduler:e.delay?t=>setTimeout(t,e.delay):Tt}function zt(e,t,n){return Dt("onBecomeUnobserved",e,t,n)}function Dt(e,t,n,r){const i="function"==typeof r?mn(t,n):mn(t),o="function"==typeof r?r:n,a=`${e}Listeners`;i[a]?i[a].add(o):i[a]=new Set([o]);return"function"!=typeof i[e]?G(!1):function(){const e=i[a];e&&(e.delete(o),0===e.size&&delete i[a])}}function jt(e){const{enforceActions:t,computedRequiresReaction:n,computedConfigurable:r,disableErrorBoundaries:i,reactionScheduler:o,reactionRequiresObservable:a,observableRequiresReaction:s}=e;if(!0===e.isolateGlobalState&&((ot.pendingReactions.length||ot.inBatch||ot.isRunningReactions)&&G("isolateGlobalState should be called before MobX is running any reactions"),it=!0,rt&&(0==--nt().__mobxInstanceCount&&(nt().__mobxGlobals=void 0),ot=new et)),void 0!==t){let e;switch(t){case!0:case"observed":e=!0;break;case!1:case"never":e=!1;break;case"strict":case"always":e="strict";break;default:G(`Invalid value for 'enforceActions': '${t}', expected 'never', 'always' or 'observed'`)}ot.enforceActions=e,ot.allowStateChanges=!0!==e&&"strict"!==e}void 0!==n&&(ot.computedRequiresReaction=!!n),void 0!==a&&(ot.reactionRequiresObservable=!!a),void 0!==s&&(ot.observableRequiresReaction=!!s,ot.allowStateReads=!ot.observableRequiresReaction),void 0!==r&&(ot.computedConfigurable=!!r),void 0!==i&&(!0===i&&console.warn("WARNING: Debug feature only. MobX will NOT recover from errors when `disableErrorBoundaries` is enabled."),ot.disableErrorBoundaries=!!i),o&&yt(o)}function Lt(e,t,n,r){const i=Bt(r=me(r));return he(e),fn(e,r.name,i.enhancer),t&&Et(e,t,n,i),e}function Bt(e){return e.defaultDecorator||(!1===e.deep?_e:ye)}function Et(e,t,n,r){lt();try{const i=ee(t);for(const o of i){const i=Object.getOwnPropertyDescriptor(t,o);0;0;const a=(n&&o in n?n[o]:i.get?Te:r)(e,o,i,!0);a&&Object.defineProperty(e,o,a)}}finally{ct()}}function Ot(e,t){return It(mn(e,t))}function It(e){const t={name:e.name};return e.observing&&e.observing.length>0&&(t.dependencies=function(e){const t=[];return e.forEach((e=>{-1===t.indexOf(e)&&t.push(e)})),t}(e.observing).map(It)),t}function Nt(){this.message="FLOW_CANCELLED"}function Pt(e,t){return null!=e&&(void 0!==t?!!vn(e)&&e[te].values.has(t):vn(e)||!!e[te]||re(e)||mt(e)||Qe(e))}function Ft(e){return 1!==arguments.length&&G(!1),Pt(e)}function Rt(e,t,n){if(2!==arguments.length||cn(e))if(vn(e)){const r=e[te];r.values.get(t)?r.write(t,n):r.addObservableProp(t,n,r.defaultEnhancer)}else if(an(e))e.set(t,n);else if(cn(e))e.add(t);else{if(!tn(e))return G(!1);"number"!=typeof t&&(t=parseInt(t,10)),U(t>=0,`Not a valid index: '${t}'`),lt(),t>=e.length&&(e.length=t+1),e[t]=n,ct()}else{lt();const n=t;try{for(let t in n)Rt(e,t,n[t])}finally{ct()}}}Nt.prototype=Object.create(Error.prototype);function Gt(e,t){lt();try{return e.apply(t)}finally{ct()}}function Ut(e){return e[te]}function Ht(e){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e}const Vt={has(e,t){if(t===te||"constructor"===t||t===ae)return!0;const n=Ut(e);return Ht(t)?n.has(t):t in e},get(e,t){if(t===te||"constructor"===t||t===ae)return e[t];const n=Ut(e),r=n.values.get(t);if(r instanceof ne){const e=r.get();return void 0===e&&n.has(t),e}return Ht(t)&&n.has(t),e[t]},set:(e,t,n)=>!!Ht(t)&&(Rt(e,t,n),!0),deleteProperty(e,t){if(!Ht(t))return!1;return Ut(e).remove(t),!0},ownKeys:e=>(Ut(e).keysAtom.reportObserved(),Reflect.ownKeys(e)),preventExtensions:e=>(G("Dynamic observable objects cannot be frozen"),!1)};function qt(e){return void 0!==e.interceptors&&e.interceptors.length>0}function Wt(e,t){const n=e.interceptors||(e.interceptors=[]);return n.push(t),H((()=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function Zt(e,t){const n=Fe();try{const r=[...e.interceptors||[]];for(let e=0,n=r.length;e<n&&(U(!(t=r[e](t))||t.type,"Intercept handlers should return nothing or a change object"),t);e++);return t}finally{Re(n)}}function $t(e){return void 0!==e.changeListeners&&e.changeListeners.length>0}function Kt(e,t){const n=e.changeListeners||(e.changeListeners=[]);return n.push(t),H((()=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function Yt(e,t){const n=Fe();let r=e.changeListeners;if(r){r=r.slice();for(let e=0,n=r.length;e<n;e++)r[e](t);Re(n)}}const Jt={get:(e,t)=>t===te?e[te]:"length"===t?e[te].getArrayLength():"number"==typeof t?Qt.get.call(e,t):"string"!=typeof t||isNaN(t)?Qt.hasOwnProperty(t)?Qt[t]:e[t]:Qt.get.call(e,parseInt(t)),set:(e,t,n)=>("length"===t&&e[te].setArrayLength(n),"number"==typeof t&&Qt.set.call(e,t,n),"symbol"==typeof t||isNaN(t)?e[t]=n:Qt.set.call(e,parseInt(t),n),!0),preventExtensions:e=>(G("Observable arrays cannot be frozen"),!1)};class Xt{constructor(e,t,n){this.owned=n,this.values=[],this.proxy=void 0,this.lastKnownLength=0,this.atom=new ne(e||"ObservableArray@"+R()),this.enhancer=(n,r)=>t(n,r,e+"[..]")}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}dehanceValues(e){return void 0!==this.dehancer&&e.length>0?e.map(this.dehancer):e}intercept(e){return Wt(this,e)}observe(e,t=!1){return t&&e({object:this.proxy,type:"splice",index:0,added:this.values.slice(),addedCount:this.values.length,removed:[],removedCount:0}),Kt(this,e)}getArrayLength(){return this.atom.reportObserved(),this.values.length}setArrayLength(e){if("number"!=typeof e||e<0)throw new Error("[mobx.array] Out of range: "+e);let t=this.values.length;if(e!==t)if(e>t){const n=new Array(e-t);for(let r=0;r<e-t;r++)n[r]=void 0;this.spliceWithArray(t,0,n)}else this.spliceWithArray(e,t-e)}updateArrayLength(e,t){if(e!==this.lastKnownLength)throw new Error("[mobx] Modification exception: the internal structure of an observable array was changed.");this.lastKnownLength+=t}spliceWithArray(e,t,n){Oe(this.atom);const r=this.values.length;if(void 0===e?e=0:e>r?e=r:e<0&&(e=Math.max(0,r+e)),t=1===arguments.length?r-e:null==t?0:Math.max(0,Math.min(t,r-e)),void 0===n&&(n=P),qt(this)){const r=Zt(this,{object:this.proxy,type:"splice",index:e,removedCount:t,added:n});if(!r)return P;t=r.removedCount,n=r.added}n=0===n.length?n:n.map((e=>this.enhancer(e,void 0)));const i=this.spliceItemsIntoValues(e,t,n);return 0===t&&0===n.length||this.notifyArraySplice(e,n,i),this.dehanceValues(i)}spliceItemsIntoValues(e,t,n){if(n.length<1e4)return this.values.splice(e,t,...n);{const r=this.values.slice(e,e+t);return this.values=this.values.slice(0,e).concat(n,this.values.slice(e+t)),r}}notifyArrayChildUpdate(e,t,n){const r=!this.owned&&!1,i=$t(this),o=i||r?{object:this.proxy,type:"update",index:e,newValue:t,oldValue:n}:null;this.atom.reportChanged(),i&&Yt(this,o)}notifyArraySplice(e,t,n){const r=!this.owned&&!1,i=$t(this),o=i||r?{object:this.proxy,type:"splice",index:e,removed:n,added:t,removedCount:n.length,addedCount:t.length}:null;this.atom.reportChanged(),i&&Yt(this,o)}}const Qt={intercept(e){return this[te].intercept(e)},observe(e,t=!1){return this[te].observe(e,t)},clear(){return this.splice(0)},replace(e){const t=this[te];return t.spliceWithArray(0,t.values.length,e)},toJS(){return this.slice()},toJSON(){return this.toJS()},splice(e,t,...n){const r=this[te];switch(arguments.length){case 0:return[];case 1:return r.spliceWithArray(e);case 2:return r.spliceWithArray(e,t)}return r.spliceWithArray(e,t,n)},spliceWithArray(e,t,n){return this[te].spliceWithArray(e,t,n)},push(...e){const t=this[te];return t.spliceWithArray(t.values.length,0,e),t.values.length},pop(){return this.splice(Math.max(this[te].values.length-1,0),1)[0]},shift(){return this.splice(0,1)[0]},unshift(...e){const t=this[te];return t.spliceWithArray(0,0,e),t.values.length},reverse(){const e=this.slice();return e.reverse.apply(e,arguments)},sort(e){const t=this.slice();return t.sort.apply(t,arguments)},remove(e){const t=this[te],n=t.dehanceValues(t.values).indexOf(e);return n>-1&&(this.splice(n,1),!0)},get(e){const t=this[te];if(t){if(e<t.values.length)return t.atom.reportObserved(),t.dehanceValue(t.values[e]);console.warn(`[mobx.array] Attempt to read an array index (${e}) that is out of bounds (${t.values.length}). Please check length first. Out of bound indices will not be tracked by MobX`)}},set(e,t){const n=this[te],r=n.values;if(e<r.length){Oe(n.atom);const i=r[e];if(qt(n)){const r=Zt(n,{type:"update",object:n.proxy,index:e,newValue:t});if(!r)return;t=r.newValue}(t=n.enhancer(t,i))!==i&&(r[e]=t,n.notifyArrayChildUpdate(e,t,i))}else{if(e!==r.length)throw new Error(`[mobx.array] Index out of bounds, ${e} is larger than ${r.length}`);n.spliceWithArray(e,0,[t])}}};["concat","flat","includes","indexOf","join","lastIndexOf","slice","toString","toLocaleString"].forEach((e=>{"function"==typeof Array.prototype[e]&&(Qt[e]=function(){const t=this[te];t.atom.reportObserved();const n=t.dehanceValues(t.values);return n[e].apply(n,arguments)})})),["every","filter","find","findIndex","flatMap","forEach","map","some"].forEach((e=>{"function"==typeof Array.prototype[e]&&(Qt[e]=function(t,n){const r=this[te];r.atom.reportObserved();return r.dehanceValues(r.values)[e](((e,r)=>t.call(n,e,r,this)),n)})})),["reduce","reduceRight"].forEach((e=>{Qt[e]=function(){const t=this[te];t.atom.reportObserved();const n=arguments[0];return arguments[0]=(e,r,i)=>(r=t.dehanceValue(r),n(e,r,i,this)),t.values[e].apply(t.values,arguments)}}));const en=$("ObservableArrayAdministration",Xt);function tn(e){return q(e)&&en(e[te])}var nn;const rn={};class on{constructor(e,t=pe,n="ObservableMap@"+R()){if(this.enhancer=t,this.name=n,this[nn]=rn,this._keysAtom=ie(`${this.name}.keys()`),this[Symbol.toStringTag]="Map","function"!=typeof Map)throw new Error("mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js");this._data=new Map,this._hasMap=new Map,this.merge(e)}_has(e){return this._data.has(e)}has(e){if(!ot.trackingDerivation)return this._has(e);let t=this._hasMap.get(e);if(!t){const n=t=new Je(this._has(e),ge,`${this.name}.${X(e)}?`,!1);this._hasMap.set(e,n),zt(n,(()=>this._hasMap.delete(e)))}return t.get()}set(e,t){const n=this._has(e);if(qt(this)){const r=Zt(this,{type:n?"update":"add",object:this,newValue:t,name:e});if(!r)return this;t=r.newValue}return n?this._updateValue(e,t):this._addValue(e,t),this}delete(e){if(Oe(this._keysAtom),qt(this)){if(!Zt(this,{type:"delete",object:this,name:e}))return!1}if(this._has(e)){const t=!1,n=$t(this),r=n||t?{type:"delete",object:this,oldValue:this._data.get(e).value,name:e}:null;return Gt((()=>{this._keysAtom.reportChanged(),this._updateHasMapEntry(e,!1);this._data.get(e).setNewValue(void 0),this._data.delete(e)})),n&&Yt(this,r),!0}return!1}_updateHasMapEntry(e,t){let n=this._hasMap.get(e);n&&n.setNewValue(t)}_updateValue(e,t){const n=this._data.get(e);if((t=n.prepareNewValue(t))!==ot.UNCHANGED){const r=!1,i=$t(this),o=i||r?{type:"update",object:this,oldValue:n.value,name:e,newValue:t}:null;0,n.setNewValue(t),i&&Yt(this,o)}}_addValue(e,t){Oe(this._keysAtom),Gt((()=>{const n=new Je(t,this.enhancer,`${this.name}.${X(e)}`,!1);this._data.set(e,n),t=n.value,this._updateHasMapEntry(e,!0),this._keysAtom.reportChanged()}));const n=!1,r=$t(this),i=r?{type:"add",object:this,name:e,newValue:t}:null;r&&Yt(this,i)}get(e){return this.has(e)?this.dehanceValue(this._data.get(e).get()):this.dehanceValue(void 0)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}keys(){return this._keysAtom.reportObserved(),this._data.keys()}values(){const e=this,t=this.keys();return Sn({next(){const{done:n,value:r}=t.next();return{done:n,value:n?void 0:e.get(r)}}})}entries(){const e=this,t=this.keys();return Sn({next(){const{done:n,value:r}=t.next();return{done:n,value:n?void 0:[r,e.get(r)]}}})}[(nn=te,Symbol.iterator)](){return this.entries()}forEach(e,t){for(const[n,r]of this)e.call(t,r,n,this)}merge(e){return an(e)&&(e=e.toJS()),Gt((()=>{const t=Ke(!0);try{W(e)?J(e).forEach((t=>this.set(t,e[t]))):Array.isArray(e)?e.forEach((([e,t])=>this.set(e,t))):K(e)?(e.constructor!==Map&&G("Cannot initialize from classes that inherit from Map: "+e.constructor.name),e.forEach(((e,t)=>this.set(t,e)))):null!=e&&G("Cannot initialize map from "+e)}finally{Ye(t)}})),this}clear(){Gt((()=>{Pe((()=>{for(const e of this.keys())this.delete(e)}))}))}replace(e){return Gt((()=>{const t=function(e){if(K(e)||an(e))return e;if(Array.isArray(e))return new Map(e);if(W(e)){const t=new Map;for(const n in e)t.set(n,e[n]);return t}return G(`Cannot convert to map from '${e}'`)}(e),n=new Map;let r=!1;for(const e of this._data.keys())if(!t.has(e)){if(this.delete(e))r=!0;else{const t=this._data.get(e);n.set(e,t)}}for(const[e,i]of t.entries()){const t=this._data.has(e);if(this.set(e,i),this._data.has(e)){const i=this._data.get(e);n.set(e,i),t||(r=!0)}}if(!r)if(this._data.size!==n.size)this._keysAtom.reportChanged();else{const e=this._data.keys(),t=n.keys();let r=e.next(),i=t.next();for(;!r.done;){if(r.value!==i.value){this._keysAtom.reportChanged();break}r=e.next(),i=t.next()}}this._data=n})),this}get size(){return this._keysAtom.reportObserved(),this._data.size}toPOJO(){const e={};for(const[t,n]of this)e["symbol"==typeof t?t:X(t)]=n;return e}toJS(){return new Map(this)}toJSON(){return this.toPOJO()}toString(){return this.name+"[{ "+Array.from(this.keys()).map((e=>`${X(e)}: ${""+this.get(e)}`)).join(", ")+" }]"}observe(e,t){return Kt(this,e)}intercept(e){return Wt(this,e)}}const an=$("ObservableMap",on);var sn;const un={};class ln{constructor(e,t=pe,n="ObservableSet@"+R()){if(this.name=n,this[sn]=un,this._data=new Set,this._atom=ie(this.name),this[Symbol.toStringTag]="Set","function"!=typeof Set)throw new Error("mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js");this.enhancer=(e,r)=>t(e,r,n),e&&this.replace(e)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}clear(){Gt((()=>{Pe((()=>{for(const e of this._data.values())this.delete(e)}))}))}forEach(e,t){for(const n of this)e.call(t,n,n,this)}get size(){return this._atom.reportObserved(),this._data.size}add(e){if(Oe(this._atom),qt(this)){if(!Zt(this,{type:"add",object:this,newValue:e}))return this}if(!this.has(e)){Gt((()=>{this._data.add(this.enhancer(e,void 0)),this._atom.reportChanged()}));const t=!1,n=$t(this),r=n||t?{type:"add",object:this,newValue:e}:null;0,n&&Yt(this,r)}return this}delete(e){if(qt(this)){if(!Zt(this,{type:"delete",object:this,oldValue:e}))return!1}if(this.has(e)){const t=!1,n=$t(this),r=n||t?{type:"delete",object:this,oldValue:e}:null;return Gt((()=>{this._atom.reportChanged(),this._data.delete(e)})),n&&Yt(this,r),!0}return!1}has(e){return this._atom.reportObserved(),this._data.has(this.dehanceValue(e))}entries(){let e=0;const t=Array.from(this.keys()),n=Array.from(this.values());return Sn({next(){const r=e;return e+=1,r<n.length?{value:[t[r],n[r]],done:!1}:{done:!0}}})}keys(){return this.values()}values(){this._atom.reportObserved();const e=this;let t=0;const n=Array.from(this._data.values());return Sn({next:()=>t<n.length?{value:e.dehanceValue(n[t++]),done:!1}:{done:!0}})}replace(e){return cn(e)&&(e=e.toJS()),Gt((()=>{const t=Ke(!0);try{Array.isArray(e)||Y(e)?(this.clear(),e.forEach((e=>this.add(e)))):null!=e&&G("Cannot initialize set from "+e)}finally{Ye(t)}})),this}observe(e,t){return Kt(this,e)}intercept(e){return Wt(this,e)}toJS(){return new Set(this)}toString(){return this.name+"[ "+Array.from(this).join(", ")+" ]"}[(sn=te,Symbol.iterator)](){return this.values()}}const cn=$("ObservableSet",ln);class hn{constructor(e,t=new Map,n,r){this.target=e,this.values=t,this.name=n,this.defaultEnhancer=r,this.keysAtom=new ne(n+".keys")}read(e){return this.values.get(e).get()}write(e,t){const n=this.target,r=this.values.get(e);if(r instanceof Xe)r.set(t);else{if(qt(this)){const r=Zt(this,{type:"update",object:this.proxy||n,name:e,newValue:t});if(!r)return;t=r.newValue}if((t=r.prepareNewValue(t))!==ot.UNCHANGED){const i=$t(this),o=!1,a=i||o?{type:"update",object:this.proxy||n,oldValue:r.value,name:e,newValue:t}:null;0,r.setNewValue(t),i&&Yt(this,a)}}}has(e){const t=this.pendingKeys||(this.pendingKeys=new Map);let n=t.get(e);if(n)return n.get();{const r=!!this.values.get(e);return n=new Je(r,ge,`${this.name}.${X(e)}?`,!1),t.set(e,n),n.get()}}addObservableProp(e,t,n=this.defaultEnhancer){const{target:r}=this;if(qt(this)){const n=Zt(this,{object:this.proxy||r,name:e,type:"add",newValue:t});if(!n)return;t=n.newValue}const i=new Je(t,n,`${this.name}.${X(e)}`,!1);this.values.set(e,i),t=i.value,Object.defineProperty(r,e,function(e){return dn[e]||(dn[e]={configurable:!0,enumerable:!0,get(){return this[te].read(e)},set(t){this[te].write(e,t)}})}(e)),this.notifyPropertyAddition(e,t)}addComputedProp(e,t,n){const{target:r}=this;n.name=n.name||`${this.name}.${X(t)}`,this.values.set(t,new Xe(n)),(e===r||function(e,t){const n=Object.getOwnPropertyDescriptor(e,t);return!n||!1!==n.configurable&&!1!==n.writable}(e,t))&&Object.defineProperty(e,t,function(e){return pn[e]||(pn[e]={configurable:ot.computedConfigurable,enumerable:!1,get(){return gn(this).read(e)},set(t){gn(this).write(e,t)}})}(t))}remove(e){if(!this.values.has(e))return;const{target:t}=this;if(qt(this)){if(!Zt(this,{object:this.proxy||t,name:e,type:"remove"}))return}try{lt();const n=$t(this),r=!1,i=this.values.get(e),o=i&&i.get();if(i&&i.set(void 0),this.keysAtom.reportChanged(),this.values.delete(e),this.pendingKeys){const t=this.pendingKeys.get(e);t&&t.set(!1)}delete this.target[e];const a=n||r?{type:"remove",object:this.proxy||t,oldValue:o,name:e}:null;0,n&&Yt(this,a)}finally{ct()}}illegalAccess(e,t){console.warn(`Property '${t}' of '${e}' was accessed through the prototype chain. Use 'decorate' instead to declare the prop or access it statically through it's owner`)}observe(e,t){return Kt(this,e)}intercept(e){return Wt(this,e)}notifyPropertyAddition(e,t){const n=$t(this),r=n?{type:"add",object:this.proxy||this.target,name:e,newValue:t}:null;if(n&&Yt(this,r),this.pendingKeys){const t=this.pendingKeys.get(e);t&&t.set(!0)}this.keysAtom.reportChanged()}getKeys(){this.keysAtom.reportObserved();const e=[];for(const[t,n]of this.values)n instanceof Je&&e.push(t);return e}}function fn(e,t="",n=pe){if(Object.prototype.hasOwnProperty.call(e,te))return e[te];W(e)||(t=(e.constructor.name||"ObservableObject")+"@"+R()),t||(t="ObservableObject@"+R());const r=new hn(e,new Map,X(t),n);return Z(e,te,r),r}const dn=Object.create(null),pn=Object.create(null);function gn(e){const t=e[te];return t||(he(e),e[te])}const bn=$("ObservableObjectAdministration",hn);function vn(e){return!!q(e)&&(he(e),bn(e[te]))}function mn(e,t){if("object"==typeof e&&null!==e){if(tn(e))return void 0!==t&&G(!1),e[te].atom;if(cn(e))return e[te];if(an(e)){const n=e;if(void 0===t)return n._keysAtom;const r=n._data.get(t)||n._hasMap.get(t);return r||G(!1),r}if(he(e),t&&!e[te]&&e[t],vn(e)){if(!t)return G(!1);const n=e[te].values.get(t);return n||G(!1),n}if(re(e)||Qe(e)||mt(e))return e}else if("function"==typeof e&&mt(e[te]))return e[te];return G(!1)}function yn(e,t){return e||G("Expecting some object"),void 0!==t?yn(mn(e,t)):re(e)||Qe(e)||mt(e)||an(e)||cn(e)?e:(he(e),e[te]?e[te]:void G(!1))}const Cn=Object.prototype.toString;function _n(e,t,n=-1){return wn(e,t,n)}function wn(e,t,n,r,i){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return!1;if(e!=e)return t!=t;const o=typeof e;if("function"!==o&&"object"!==o&&"object"!=typeof t)return!1;const a=Cn.call(e);if(a!==Cn.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return"undefined"!=typeof Symbol&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":n>=0&&n++}e=xn(e),t=xn(t);const s="[object Array]"===a;if(!s){if("object"!=typeof e||"object"!=typeof t)return!1;const n=e.constructor,r=t.constructor;if(n!==r&&!("function"==typeof n&&n instanceof n&&"function"==typeof r&&r instanceof r)&&"constructor"in e&&"constructor"in t)return!1}if(0===n)return!1;n<0&&(n=-1),i=i||[];let u=(r=r||[]).length;for(;u--;)if(r[u]===e)return i[u]===t;if(r.push(e),i.push(t),s){if(u=e.length,u!==t.length)return!1;for(;u--;)if(!wn(e[u],t[u],n-1,r,i))return!1}else{const o=Object.keys(e);let a;if(u=o.length,Object.keys(t).length!==u)return!1;for(;u--;)if(a=o[u],!An(t,a)||!wn(e[a],t[a],n-1,r,i))return!1}return r.pop(),i.pop(),!0}function xn(e){return tn(e)?e.slice():K(e)||an(e)||Y(e)||cn(e)?Array.from(e.entries()):e}function An(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Sn(e){return e[Symbol.iterator]=Mn,e}function Mn(){return this}if("undefined"==typeof Proxy||"undefined"==typeof Symbol)throw new Error("[mobx] MobX 5+ requires Proxy and Symbol objects. If your environment doesn't support Symbol or Proxy objects, please downgrade to MobX 4. For React Native Android, consider upgrading JSCore.");function Tn(e){return"number"==typeof e.parsedSize}function kn(e,t){for(const n of e){if(!1===t(n))return!1;if(n.groups&&!1===kn(n.groups,t))return!1}}"object"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:Ct,extras:{getDebugName:function(e,t){let n;return n=void 0!==t?mn(e,t):vn(e)||an(e)||cn(e)?yn(e):mn(e),n.name}},$mobx:te});const zn={getItem(e){try{return JSON.parse(window.localStorage.getItem(`wba.${e}`))}catch(t){return null}},setItem(e,t){try{window.localStorage.setItem(`wba.${e}`,JSON.stringify(t))}catch(n){}},removeItem(e){try{window.localStorage.removeItem(`wba.${e}`)}catch(t){}}};var Dn,jn,Ln,Bn,En,On,In,Nn,Pn;const Fn=new(Dn=Se.ref,jn=Se.shallow,Bn=N((Ln=class{constructor(){this.cid=0,this.sizes=new Set(["statSize","parsedSize","gzipSize"]),I(this,"allChunks",Bn,this),I(this,"selectedChunks",En,this),I(this,"searchQuery",On,this),I(this,"defaultSize",In,this),I(this,"selectedSize",Nn,this),I(this,"showConcatenatedModulesContent",Pn,this)}setModules(e){kn(e,(e=>{e.cid=this.cid++})),this.allChunks=e,this.selectedChunks=this.allChunks}get hasParsedSizes(){return this.allChunks.some(Tn)}get activeSize(){const e=this.selectedSize||this.defaultSize;return this.hasParsedSizes&&this.sizes.has(e)?e:"statSize"}get visibleChunks(){const e=this.allChunks.filter((e=>this.selectedChunks.includes(e)));return this.filterModulesForSize(e,this.activeSize)}get allChunksSelected(){return this.visibleChunks.length===this.allChunks.length}get totalChunksSize(){return this.allChunks.reduce(((e,t)=>e+(t[this.activeSize]||0)),0)}get searchQueryRegexp(){const e=this.searchQuery.trim();if(!e)return null;try{return new RegExp(e,"iu")}catch(t){return null}}get isSearching(){return!!this.searchQueryRegexp}get foundModulesByChunk(){if(!this.isSearching)return[];const e=this.searchQueryRegexp;return this.visibleChunks.map((t=>{let n=[];kn(t.groups,(t=>{let r=0;if(e.test(t.label)?r+=3:t.path&&e.test(t.path)&&r++,!r)return;t.groups||(r+=1);(n[r-1]=n[r-1]||[]).push(t)}));const{activeSize:r}=this;return n=n.filter(Boolean).reverse(),n.forEach((e=>e.sort(((e,t)=>t[r]-e[r])))),{chunk:t,modules:[].concat(...n)}})).filter((e=>e.modules.length>0)).sort(((e,t)=>e.modules.length-t.modules.length))}get foundModules(){return this.foundModulesByChunk.reduce(((e,t)=>e.concat(t.modules)),[])}get hasFoundModules(){return this.foundModules.length>0}get hasConcatenatedModules(){let e=!1;return kn(this.visibleChunks,(t=>{if(t.concatenated)return e=!0,!1})),e}get foundModulesSize(){return this.foundModules.reduce(((e,t)=>e+t[this.activeSize]),0)}filterModulesForSize(e,t){return e.reduce(((e,n)=>{if(n[t]){if(n.groups){const e=!n.concatenated||this.showConcatenatedModulesContent;n={...n,groups:e?this.filterModulesForSize(n.groups,t):null}}n.weight=n[t],e.push(n)}return e}),[])}}).prototype,"allChunks",[Dn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),En=N(Ln.prototype,"selectedChunks",[jn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),On=N(Ln.prototype,"searchQuery",[Se],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return""}}),In=N(Ln.prototype,"defaultSize",[Se],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Nn=N(Ln.prototype,"selectedSize",[Se],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Pn=N(Ln.prototype,"showConcatenatedModulesContent",[Se],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0===zn.getItem("showConcatenatedModulesContent")}}),N(Ln.prototype,"hasParsedSizes",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"hasParsedSizes"),Ln.prototype),N(Ln.prototype,"activeSize",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"activeSize"),Ln.prototype),N(Ln.prototype,"visibleChunks",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"visibleChunks"),Ln.prototype),N(Ln.prototype,"allChunksSelected",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"allChunksSelected"),Ln.prototype),N(Ln.prototype,"totalChunksSize",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"totalChunksSize"),Ln.prototype),N(Ln.prototype,"searchQueryRegexp",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"searchQueryRegexp"),Ln.prototype),N(Ln.prototype,"isSearching",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"isSearching"),Ln.prototype),N(Ln.prototype,"foundModulesByChunk",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"foundModulesByChunk"),Ln.prototype),N(Ln.prototype,"foundModules",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"foundModules"),Ln.prototype),N(Ln.prototype,"hasFoundModules",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"hasFoundModules"),Ln.prototype),N(Ln.prototype,"hasConcatenatedModules",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"hasConcatenatedModules"),Ln.prototype),N(Ln.prototype,"foundModulesSize",[ze],Object.getOwnPropertyDescriptor(Ln.prototype,"foundModulesSize"),Ln.prototype),Ln);var Rn,Gn,Un,Hn=n(6755),Vn=n.n(Hn),qn=0,Wn=[],Zn=e.__b,$n=e.__r,Kn=e.diffed,Yn=e.__c,Jn=e.unmount;function Xn(t,n){e.__h&&e.__h(Gn,t,qn||n),qn=0;var r=Gn.__H||(Gn.__H={__:[],__h:[]});return t>=r.__.length&&r.__.push({}),r.__[t]}function Qn(e){return qn=1,er(lr,e)}function er(e,t,n){var r=Xn(Rn++,2);return r.t=e,r.__c||(r.__=[n?n(t):lr(void 0,t),function(e){var t=r.t(r.__[0],e);r.__[0]!==t&&(r.__=[t,r.__[1]],r.__c.setState({}))}],r.__c=Gn),r.__}function tr(t,n){var r=Xn(Rn++,4);!e.__s&&ur(r.__H,n)&&(r.__=t,r.__H=n,Gn.__h.push(r))}function nr(e,t){var n=Xn(Rn++,7);return ur(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function rr(e,t){return qn=8,nr((function(){return e}),t)}function ir(){Wn.forEach((function(n){if(n.__P)try{n.__H.__h.forEach(ar),n.__H.__h.forEach(sr),n.__H.__h=[]}catch(t){n.__H.__h=[],e.__e(t,n.__v)}})),Wn=[]}e.__b=function(e){Gn=null,Zn&&Zn(e)},e.__r=function(e){$n&&$n(e),Rn=0;var t=(Gn=e.__c).__H;t&&(t.__h.forEach(ar),t.__h.forEach(sr),t.__h=[])},e.diffed=function(t){Kn&&Kn(t);var n=t.__c;n&&n.__H&&n.__H.__h.length&&(1!==Wn.push(n)&&Un===e.requestAnimationFrame||((Un=e.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(r),or&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);or&&(t=requestAnimationFrame(n))})(ir)),Gn=void 0},e.__c=function(t,n){n.some((function(t){try{t.__h.forEach(ar),t.__h=t.__h.filter((function(e){return!e.__||sr(e)}))}catch(a){n.some((function(e){e.__h&&(e.__h=[])})),n=[],e.__e(a,t.__v)}})),Yn&&Yn(t,n)},e.unmount=function(t){Jn&&Jn(t);var n=t.__c;if(n&&n.__H)try{n.__H.__.forEach(ar)}catch(t){e.__e(t,n.__v)}};var or="function"==typeof requestAnimationFrame;function ar(e){var t=Gn;"function"==typeof e.__c&&e.__c(),Gn=t}function sr(e){var t=Gn;e.__c=e.__(),Gn=t}function ur(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function lr(e,t){return"function"==typeof t?t(e):t}function cr(e,t){for(var n in t)e[n]=t[n];return e}function hr(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function fr(e){this.props=e}function dr(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:hr(this.props,e)}function r(t){return this.shouldComponentUpdate=n,h(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(fr.prototype=new p).isPureReactComponent=!0,fr.prototype.shouldComponentUpdate=function(e,t){return hr(this.props,e)||hr(this.state,t)};var pr=e.__b;e.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),pr&&pr(e)};var gr="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function br(e){function t(t,n){var r=cr({},t);return delete r.ref,e(r,(n=t.ref||n)&&("object"!=typeof n||"current"in n)?n:null)}return t.$$typeof=gr,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var vr=function(e,t){return null==e?null:_(_(e).map(t))},mr={map:vr,forEach:vr,count:function(e){return e?_(e).length:0},only:function(e){var t=_(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:_},yr=e.__e;e.__e=function(e,t,n){if(e.then)for(var r,i=t;i=i.__;)if((r=i.__c)&&r.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t);yr(e,t,n)};var Cr=e.unmount;function _r(){this.__u=0,this.t=null,this.__b=null}function wr(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function xr(){this.u=null,this.o=null}e.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),Cr&&Cr(e)},(_r.prototype=new p).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var i=wr(r.__v),o=!1,a=function(){o||(o=!0,n.__R=null,i?i(s):s())};n.__R=a;var s=function(){if(!--r.__u){if(r.state.__e){var e=r.state.__e;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)})),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__e:r.__b=null});t=r.t.pop();)t.forceUpdate()}},u=!0===t.__h;r.__u++||u||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(a,a)},_r.prototype.componentWillUnmount=function(){this.t=[]},_r.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),t.__c.__H=null),null!=(t=cr({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)}))),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__e&&h(d,null,e.fallback);return i&&(i.__h=null),[h(d,null,t.__e?null:e.children),i]};var Ar=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function Sr(e){return this.getChildContext=function(){return e.context},e.children}function Mr(e){var t=this,n=e.i;t.componentWillUnmount=function(){B(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),B(h(Sr,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}(xr.prototype=new p).__e=function(e){var t=this,n=wr(t.__v),r=t.o.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),Ar(t,e,r)):i()};n?n(o):o()}},xr.prototype.render=function(e){this.u=null,this.o=new Map;var t=_(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},xr.prototype.componentDidUpdate=xr.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){Ar(e,n,t)}))};var Tr="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,kr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,zr=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(e)};p.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(p.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Dr=e.event;function jr(){}function Lr(){return this.cancelBubble}function Br(){return this.defaultPrevented}e.event=function(e){return Dr&&(e=Dr(e)),e.persist=jr,e.isPropagationStopped=Lr,e.isDefaultPrevented=Br,e.nativeEvent=e};var Er,Or={configurable:!0,get:function(){return this.class}},Ir=e.vnode;e.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){for(var i in r={},n){var o=n[i];"value"===i&&"defaultValue"in n&&null==o||("defaultValue"===i&&"value"in n&&null==n.value?i="value":"download"===i&&!0===o?o="":/ondoubleclick/i.test(i)?i="ondblclick":/^onchange(textarea|input)/i.test(i+t)&&!zr(n.type)?i="oninput":/^on(Ani|Tra|Tou|BeforeInp)/.test(i)?i=i.toLowerCase():kr.test(i)?i=i.replace(/[A-Z0-9]/,"-$&").toLowerCase():null===o&&(o=void 0),r[i]=o)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=_(n.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==t&&null!=r.defaultValue&&(r.value=_(n.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),e.props=r}t&&n.class!=n.className&&(Or.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",Or)),e.$$typeof=Tr,Ir&&Ir(e)};var Nr=e.__r;e.__r=function(e){Nr&&Nr(e),Er=e.__c};var Pr={ReactCurrentDispatcher:{current:{readContext:function(e){return Er.__n[e.__c].props.value}}}};"object"==typeof performance&&"function"==typeof performance.now&&performance.now.bind(performance);function Fr(e){return!!e&&e.$$typeof===Tr}var Rr=function(e,t){return e(t)};const Gr={useState:Qn,useReducer:er,useEffect:function(t,n){var r=Xn(Rn++,3);!e.__s&&ur(r.__H,n)&&(r.__=t,r.__H=n,Gn.__H.__h.push(r))},useLayoutEffect:tr,useRef:function(e){return qn=5,nr((function(){return{current:e}}),[])},useImperativeHandle:function(e,t,n){qn=6,tr((function(){"function"==typeof e?e(t()):e&&(e.current=t())}),null==n?n:n.concat(e))},useMemo:nr,useCallback:rr,useContext:function(e){var t=Gn.context[e.__c],n=Xn(Rn++,9);return n.__c=e,t?(null==n.__&&(n.__=!0,t.sub(Gn)),t.props.value):e.__},useDebugValue:function(t,n){e.useDebugValue&&e.useDebugValue(n?n(t):t)},version:"16.8.0",Children:mr,render:function(e,t,n){return null==t.__k&&(t.textContent=""),B(e,t),"function"==typeof n&&n(),e?e.__c:null},hydrate:function(e,t,n){return E(e,t),"function"==typeof n&&n(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(B(null,e),!0)},createPortal:function(e,t){return h(Mr,{__v:e,i:t})},createElement:h,createContext:function(e,t){var n={__c:t="__cC"+o++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some(v)},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n},createFactory:function(e){return h.bind(null,e)},cloneElement:function(e){return Fr(e)?O.apply(null,arguments):e},createRef:function(){return{current:null}},Fragment:d,isValidElement:Fr,findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:p,PureComponent:fr,memo:dr,forwardRef:br,unstable_batchedUpdates:Rr,StrictMode:d,Suspense:_r,SuspenseList:xr,lazy:function(e){var t,n,r;function i(i){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){r=e})),r)throw r;if(!n)throw t;return h(n,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Pr};if(!Qn)throw new Error("mobx-react-lite requires React with Hooks support");if(!Ct)throw new Error("mobx-react-lite requires mobx at least version 4 to be available");var Ur=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a};function Hr(){var e=Ur(Qn(0),2)[1];return rr((function(){e((function(e){return e+1}))}),[])}var Vr={};var qr,Wr=(qr="observerBatching","function"==typeof Symbol?Symbol.for(qr):"__$mobx-react "+qr+"__");function Zr(e){e()}var $r=!1;function Kr(){return $r}function Yr(e){return Ot(e)}var Jr,Xr=1e4,Qr=new Set;function ei(){void 0===Jr&&(Jr=setTimeout(ti,1e4))}function ti(){Jr=void 0;var e=Date.now();Qr.forEach((function(t){var n=t.current;n&&e>=n.cleanAt&&(n.reaction.dispose(),t.current=null,Qr.delete(t))})),Qr.size>0&&ei()}var ni=!1,ri=[];var ii={};function oi(e){return"observer"+e}function ai(e,t,n){if(void 0===t&&(t="observed"),void 0===n&&(n=ii),Kr())return e();var r,i=function(e){return function(){ni?ri.push(e):e()}}((n.useForceUpdate||Hr)()),o=Gr.useRef(null);if(!o.current){var a=new pt(oi(t),(function(){s.mounted?i():(a.dispose(),o.current=null)})),s=function(e){return{cleanAt:Date.now()+Xr,reaction:e}}(a);o.current=s,r=o,Qr.add(r),ei()}var l=o.current.reaction;return Gr.useDebugValue(l,Yr),Gr.useEffect((function(){var e;return e=o,Qr.delete(e),o.current?o.current.mounted=!0:(o.current={reaction:new pt(oi(t),(function(){i()})),cleanAt:1/0},i()),function(){o.current.reaction.dispose(),o.current=null}}),[]),function(e){ni=!0,ri=[];try{var t=e();ni=!1;var n=ri.length>0?ri:void 0;return Gr.useLayoutEffect((function(){n&&n.forEach((function(e){return e()}))}),[n]),t}finally{ni=!1}}((function(){var t,n;if(l.track((function(){try{t=e()}catch(u){n=u}})),n)throw n;return t}))}var si=function(){return(si=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function ui(e,t){if(Kr())return e;var n,r,i,o=si({forwardRef:!1},t),a=e.displayName||e.name,s=function(t,n){return ai((function(){return e(t,n)}),a)};return s.displayName=a,n=o.forwardRef?dr(br(s)):dr(s),r=e,i=n,Object.keys(r).forEach((function(e){li[e]||Object.defineProperty(i,e,Object.getOwnPropertyDescriptor(r,e))})),n.displayName=a,n}var li={$$typeof:!0,render:!0,compare:!0,type:!0};function ci(e){var t=e.children,n=e.render,r=t||n;return"function"!=typeof r?null:ai(r)}function hi(e,t,n,r,i){var o="children"===t?"render":"children",a="function"==typeof e[t],s="function"==typeof e[o];return a&&s?new Error("MobX Observer: Do not use children and render in the same time in`"+n):a||s?null:new Error("Invalid prop `"+i+"` of type `"+typeof e[t]+"` supplied to `"+n+"`, expected `function`.")}ci.propTypes={children:hi,render:hi},ci.displayName="Observer";!function(e){e||(e=Zr),jt({reactionScheduler:e}),("undefined"!=typeof window?window:"undefined"!=typeof self?self:Vr)[Wr]=!0}(Rr);var fi=0;var di={};function pi(e){return di[e]||(di[e]=function(e){if("function"==typeof Symbol)return Symbol(e);var t="__$mobx-react "+e+" ("+fi+")";return fi++,t}(e)),di[e]}function gi(e,t){if(bi(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var i=0;i<n.length;i++)if(!Object.hasOwnProperty.call(t,n[i])||!bi(e[n[i]],t[n[i]]))return!1;return!0}function bi(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function vi(e,t,n){Object.hasOwnProperty.call(e,t)?e[t]=n:Object.defineProperty(e,t,{enumerable:!1,configurable:!0,writable:!0,value:n})}var mi=pi("patchMixins"),yi=pi("patchedDefinition");function Ci(e,t){for(var n=this,r=arguments.length,i=new Array(r>2?r-2:0),o=2;o<r;o++)i[o-2]=arguments[o];t.locks++;try{var a;return null!=e&&(a=e.apply(this,i)),a}finally{t.locks--,0===t.locks&&t.methods.forEach((function(e){e.apply(n,i)}))}}function _i(e,t){return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];Ci.call.apply(Ci,[this,e,t].concat(r))}}function wi(e,t,n){var r=function(e,t){var n=e[mi]=e[mi]||{},r=n[t]=n[t]||{};return r.locks=r.locks||0,r.methods=r.methods||[],r}(e,t);r.methods.indexOf(n)<0&&r.methods.push(n);var i=Object.getOwnPropertyDescriptor(e,t);if(!i||!i[yi]){var o=e[t],a=xi(e,t,i?i.enumerable:void 0,r,o);Object.defineProperty(e,t,a)}}function xi(e,t,n,r,i){var o,a=_i(i,r);return(o={})[yi]=!0,o.get=function(){return a},o.set=function(i){if(this===e)a=_i(i,r);else{var o=xi(this,t,n,r,i);Object.defineProperty(this,t,o)}},o.configurable=!0,o.enumerable=n,o}var Ai=te||"$mobx",Si=pi("isMobXReactObserver"),Mi=pi("isUnmounted"),Ti=pi("skipRender"),ki=pi("isForcingUpdate");function zi(e){var t=e.prototype;if(e[Si]){var n=Di(t);console.warn("The provided component class ("+n+") \n                has already been declared as an observer component.")}else e[Si]=!0;if(t.componentWillReact)throw new Error("The componentWillReact life-cycle event is no longer supported");if(e.__proto__!==fr)if(t.shouldComponentUpdate){if(t.shouldComponentUpdate!==Li)throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.")}else t.shouldComponentUpdate=Li;Bi(t,"props"),Bi(t,"state");var r=t.render;return t.render=function(){return ji.call(this,r)},wi(t,"componentWillUnmount",(function(){var e;if(!0!==Kr()&&(null===(e=this.render[Ai])||void 0===e||e.dispose(),this[Mi]=!0,!this.render[Ai])){var t=Di(this);console.warn("The reactive render of an observer class component ("+t+") \n                was overriden after MobX attached. This may result in a memory leak if the \n                overriden reactive render was not properly disposed.")}})),e}function Di(e){return e.displayName||e.name||e.constructor&&(e.constructor.displayName||e.constructor.name)||"<component>"}function ji(e){var t=this;if(!0===Kr())return e.call(this);vi(this,Ti,!1),vi(this,ki,!1);var n=Di(this),r=e.bind(this),i=!1,o=new pt(n+".render()",(function(){if(!i&&(i=!0,!0!==t[Mi])){var e=!0;try{vi(t,ki,!0),t[Ti]||p.prototype.forceUpdate.call(t),e=!1}finally{vi(t,ki,!1),e&&o.dispose()}}}));function a(){i=!1;var e=void 0,t=void 0;if(o.track((function(){try{t=function(e,t){const n=Ke(e);let r;try{r=t()}finally{Ye(n)}return r}(!1,r)}catch(u){e=u}})),e)throw e;return t}return o.reactComponent=this,a[Ai]=o,this.render=a,a.call(this)}function Li(e,t){return Kr()&&console.warn("[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side."),this.state!==t||!gi(this.props,e)}function Bi(e,t){var n=pi("reactProp_"+t+"_valueHolder"),r=pi("reactProp_"+t+"_atomHolder");function i(){return this[r]||vi(this,r,ie("reactive "+t)),this[r]}Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){var e=!1;return Ge&&Ue&&(e=Ge(!0)),i.call(this).reportObserved(),Ge&&Ue&&Ue(e),this[n]},set:function(e){this[ki]||gi(this[n],e)?vi(this,n,e):(vi(this,n,e),vi(this,Ti,!0),i.call(this).reportChanged(),vi(this,Ti,!1))}})}var Ei="function"==typeof Symbol&&Symbol.for,Oi=Ei?Symbol.for("react.forward_ref"):br((function(e){return null})).$$typeof,Ii=Ei?Symbol.for("react.memo"):dr((function(e){return null})).$$typeof;function Ni(e){if(!0===e.isMobxInjector&&console.warn("Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'"),Ii&&e.$$typeof===Ii)throw new Error("Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.");if(Oi&&e.$$typeof===Oi){var t=e.render;if("function"!=typeof t)throw new Error("render property of ForwardRef was not a function");return br((function(){var e=arguments;return h(ci,null,(function(){return t.apply(void 0,e)}))}))}return"function"!=typeof e||e.prototype&&e.prototype.render||e.isReactClass||Object.prototype.isPrototypeOf.call(p,e)?zi(e):ui(e)}if(!p)throw new Error("mobx-react requires React to be available");if(!Se)throw new Error("mobx-react requires mobx to be available");
/**
 * Carrot Search FoamTree HTML5 (demo variant)
 * v3.5.0, bugfix/3.5.x/e3b91c8e, build FOAMTREE-SOFTWARE5-DIST-3, Jan 18, 2021
 * 
 * Carrot Search confidential.
 * Copyright 2002-2021, Carrot Search s.c, All Rights Reserved.
 */
!function(){var e,t=function(){var e=window.navigator.userAgent;try{window.localStorage.setItem("ftap5caavc","ftap5caavc"),window.localStorage.removeItem("ftap5caavc");var n=!0}catch(r){n=!1}return{Se:function(){return/webkit/i.test(e)},Lh:function(){return/Mac/.test(e)},Qe:function(){return/iPad|iPod|iPhone/.test(e)},Kh:function(){return/Android/.test(e)},Gh:function(){return"ontouchstart"in window||!!window.DocumentTouch&&document instanceof window.DocumentTouch},Fh:function(){return n},Eh:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},md:function(e,n){return[].forEach&&t.Eh()?e&&e():n&&n()}}}(),n=function(){function e(){return window.performance&&(window.performance.now||window.performance.mozNow||window.performance.msNow||window.performance.oNow||window.performance.webkitNow)||Date.now}var t=e();return{create:function(){return{now:function(){var t=e();return function(){return t.call(window.performance)}}()}},now:function(){return t.call(window.performance)}}}();function r(){function r(){if(!u)throw"AF0";var e=n.now();0!==l&&(o.sd=e-l),l=e,s=s.filter((function(e){return null!==e})),o.frames++;for(var t=0;t<s.length;t++){var r=s[t];null!==r&&(!0===r.ee.call(r.context)?s[t]=null:y.zc(r.repeat)&&(r.repeat=r.repeat-1,0>=r.repeat&&(s[t]=null)))}s=s.filter((function(e){return null!==e})),u=!1,i(),0!==(e=n.now()-e)&&(o.rd=e),o.totalTime+=e,o.ue=1e3*o.frames/o.totalTime,l=0===s.length?0:n.now()}function i(){0<s.length&&!u&&(u=!0,a(r))}var o=this.Wf={frames:0,totalTime:0,rd:0,sd:0,ue:0};e=o;var a=t.Qe()?function(e){window.setTimeout(e,0)}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(){var e=n.create();return function(t){var n=0;window.setTimeout((function(){var r=e.now();t(),n=e.now()-r}),16>n?16-n:0)}}(),s=[],u=!1,l=0;this.repeat=function(e,t,n){this.cancel(e),s.push({ee:e,context:n,repeat:t}),i()},this.once=function(e,t){this.repeat(e,1,t)},this.cancel=function(e){for(var t=0;t<s.length;t++){var n=s[t];null!==n&&n.ee===e&&(s[t]=null)}},this.i=function(){s=[]}}var i=t.md((function(){function e(){this.buffer=[],this.ma=0,this.current=y.extend({},s)}function t(e){return function(){var t,n=this.buffer,r=this.ma;for(n[r++]="call",n[r++]=e,n[r++]=arguments.length,t=0;t<arguments.length;t++)n[r++]=arguments[t];this.ma=r}}function n(e){return function(){return o[e].apply(o,arguments)}}var r=document.createElement("canvas");r.width=1,r.height=1;var o=r.getContext("2d");r=["font"];var a="fillStyle globalAlpha globalCompositeOperation lineCap lineDashOffset lineJoin lineWidth miterLimit shadowBlur shadowColor shadowOffsetX shadowOffsetY strokeStyle textAlign textBaseline".split(" "),s={};return a.concat(r).forEach((function(e){s[e]=o[e]})),e.prototype.clear=function(){this.ma=0},e.prototype.Ga=function(){return 0===this.ma},e.prototype.Na=function(e){e instanceof i?function(e,t,n){for(var r=0,i=e.ma,o=e.buffer;r<n;)o[i++]=t[r++];e.ma=i}(e,this.buffer,this.ma):function(e,t,n,r){for(var i=0;i<n;)switch(t[i++]){case"set":e[t[i++]]=t[i++];break;case"setGlobalAlpha":e[t[i++]]=t[i++]*r;break;case"call":var o=t[i++];switch(t[i++]){case 0:e[o]();break;case 1:e[o](t[i++]);break;case 2:e[o](t[i++],t[i++]);break;case 3:e[o](t[i++],t[i++],t[i++]);break;case 4:e[o](t[i++],t[i++],t[i++],t[i++]);break;case 5:e[o](t[i++],t[i++],t[i++],t[i++],t[i++]);break;case 6:e[o](t[i++],t[i++],t[i++],t[i++],t[i++],t[i++]);break;case 7:e[o](t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++]);break;case 8:e[o](t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++]);break;case 9:e[o](t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++],t[i++]);break;default:throw"CB0"}}}(e,this.buffer,this.ma,y.I(e.globalAlpha,1))},e.prototype.replay=e.prototype.Na,e.prototype.i=function(){return new e},e.prototype.scratch=e.prototype.i,"arc arcTo beginPath bezierCurveTo clearRect clip closePath drawImage fill fillRect fillText lineTo moveTo putImageData quadraticCurveTo rect rotate scale setLineDash setTransform stroke strokeRect strokeText transform translate".split(" ").forEach((function(n){e.prototype[n]=t(n)})),["measureText","createLinearGradient","createRadialGradient","createPattern","getLineDash"].forEach((function(t){e.prototype[t]=n(t)})),["save","restore"].forEach((function(r){var i=n(r),o=t(r);e.prototype[r]=function(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}(o,i)})),r.forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){o[t]=e,this.current[t]=e;var n=this.buffer;n[this.ma++]="set",n[this.ma++]=t,n[this.ma++]=e},get:function(){return this.current[t]}})})),a.forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.current[t]=e;var n=this.buffer;n[this.ma++]="globalAlpha"===t?"setGlobalAlpha":"set",n[this.ma++]=t,n[this.ma++]=e},get:function(){return this.current[t]}})})),e.prototype.roundRect=function(e,t,n,r,i){this.beginPath(),this.moveTo(e+i,t),this.lineTo(e+n-i,t),this.quadraticCurveTo(e+n,t,e+n,t+i),this.lineTo(e+n,t+r-i),this.quadraticCurveTo(e+n,t+r,e+n-i,t+r),this.lineTo(e+i,t+r),this.quadraticCurveTo(e,t+r,e,t+r-i),this.lineTo(e,t+i),this.quadraticCurveTo(e,t,e+i,t),this.closePath()},e.prototype.fillPolygonWithText=function(e,t,n,r,o){o||(o={});var a={hb:y.I(o.maxFontSize,j.ya.hb),Gc:y.I(o.minFontSize,j.ya.Gc),lineHeight:y.I(o.lineHeight,j.ya.lineHeight),cb:y.I(o.horizontalPadding,j.ya.cb),Ua:y.I(o.verticalPadding,j.ya.Ua),ib:y.I(o.maxTotalTextHeight,j.ya.ib),fontFamily:y.I(o.fontFamily,j.ya.fontFamily),fontStyle:y.I(o.fontStyle,j.ya.fontStyle),fontVariant:y.I(o.fontVariant,j.ya.fontVariant),fontWeight:y.I(o.fontWeight,j.ya.fontWeight),verticalAlign:y.I(o.verticalAlign,j.ya.verticalAlign)},s=o.cache;if(s&&y.has(o,"area")){s.Qc||(s.Qc=new i);var u=o.area,l=y.I(o.cacheInvalidationThreshold,.05);e=j.de(a,this,r,e,M.F(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1,s,u,l,o.invalidateCache)}else e=j.re(a,this,r,e,M.F(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1);return e.ka?{fit:!0,lineCount:e.bc,fontSize:e.fontSize,box:{x:e.box.x,y:e.box.y,w:e.box.w,h:e.box.o},ellipsis:e.Ub}:{fit:!1}},e})),o=t.md((function(){function e(e){this.S=e,this.canvas=e.canvas,this.i=[],this.zb=[void 0],this.vc=["#SIZE#px sans-serif"],this.td=[0],this.ud=[1],this.Rd=[0],this.Sd=[0],this.Td=[0],this.yd=[10],this.Xb=[10],this.Hb=[this.zb,this.vc,this.Xb,this.td,this.ud,this.Rd,this.yd,this.Sd,this.Td],this.da=[1,0,0,1,0,0]}function t(e){var t=e.S,n=e.Hb[0].length-1;e.zb[n]&&(t.setLineDash(e.zb[n]),t.lineDashOffset=e.td[n]),t.miterLimit=e.yd[n],t.lineWidth=e.ud[n],t.shadowBlur=e.Rd[n],t.shadowOffsetX=e.Sd[n],t.shadowOffsetY=e.Td[n],t.font=e.vc[n].replace("#SIZE#",e.Xb[n].toString())}function n(e,t,n){return e*n[0]+t*n[2]+n[4]}function r(e,t,n){return e*n[1]+t*n[3]+n[5]}function i(e,t){for(var n=0;n<e.length;n++)e[n]*=t[0];return e}e.prototype.save=function(){this.i.push(this.da.slice(0));for(var e=0;e<this.Hb.length;e++){var t=this.Hb[e];t.push(t[t.length-1])}this.S.save()},e.prototype.restore=function(){this.da=this.i.pop();for(var e=0;e<this.Hb.length;e++)this.Hb[e].pop();this.S.restore(),t(this)},e.prototype.scale=function(e,n){var r=this.da;r[0]*=e,r[1]*=e,r[2]*=n,r[3]*=n,e=this.da,r=(n=this.Hb)[0].length-1;var o=this.zb[r];for(o&&i(o,e),o=2;o<n.length;o++){n[o][r]*=e[0]}t(this)},e.prototype.translate=function(e,t){var n=this.da;n[4]+=n[0]*e+n[2]*t,n[5]+=n[1]*e+n[3]*t},["moveTo","lineTo"].forEach((function(t){e.prototype[t]=function(e){return function(t,i){var o=this.da;return this.S[e].call(this.S,n(t,i,o),r(t,i,o))}}(t)})),["clearRect","fillRect","strokeRect","rect"].forEach((function(t){e.prototype[t]=function(e){return function(t,i,o,a){var s=this.da;return this.S[e].call(this.S,n(t,i,s),r(t,i,s),o*s[0],a*s[3])}}(t)})),"fill stroke beginPath closePath clip createImageData createPattern getImageData putImageData getLineDash setLineDash".split(" ").forEach((function(t){e.prototype[t]=function(e){return function(){return this.S[e].apply(this.S,arguments)}}(t)})),[{p:"lineDashOffset",a:function(e){return e.td}},{p:"lineWidth",a:function(e){return e.ud}},{p:"miterLimit",a:function(e){return e.yd}},{p:"shadowBlur",a:function(e){return e.Rd}},{p:"shadowOffsetX",a:function(e){return e.Sd}},{p:"shadowOffsetY",a:function(e){return e.Td}}].forEach((function(t){Object.defineProperty(e.prototype,t.p,{set:function(e){var n=t.a(this);e*=this.da[0],n[n.length-1]=e,this.S[t.p]=e}})}));var o=/(\d+(?:\.\d+)?)px/;return Object.defineProperty(e.prototype,"font",{set:function(e){var t=o.exec(e);if(1<t.length){var n=this.Xb.length-1;this.Xb[n]=parseFloat(t[1]),this.vc[n]=e.replace(o,"#SIZE#px"),e=this.S,n=this.vc[n].replace("#SIZE#",(this.Xb[n]*this.da[0]).toString()),e.font=n}}}),"fillStyle globalAlpha globalCompositeOperation lineCap lineJoin shadowColor strokeStyle textAlign textBaseline".split(" ").forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.S[t]=e}})})),e.prototype.arc=function(e,t,i,o,a,s){var u=this.da;this.S.arc(n(e,t,u),r(e,t,u),i*u[0],o,a,s)},e.prototype.arcTo=function(e,t,i,o,a){var s=this.da;this.S.arc(n(e,t,s),r(e,t,s),n(i,o,s),r(i,o,s),a*s[0])},e.prototype.bezierCurveTo=function(e,t,i,o,a,s){var u=this.da;this.S.bezierCurveTo(n(e,t,u),r(e,t,u),n(i,o,u),r(i,o,u),n(a,s,u),r(a,s,u))},e.prototype.drawImage=function(e,t,i,o,a,s,u,l,c){function h(t,i,o,a){d.push(n(t,i,f)),d.push(r(t,i,f)),o=y.V(o)?e.width:o,a=y.V(a)?e.height:a,d.push(o*f[0]),d.push(a*f[3])}var f=this.da,d=[e];y.V(s)?h(t,i,o,a):h(s,u,l,c),this.S.drawImage.apply(this.S,d)},e.prototype.quadraticCurveTo=function(e,t,i,o){var a=this.da;this.S.quadraticCurveTo(n(e,t,a),r(e,t,a),n(i,o,a),r(i,o,a))},e.prototype.fillText=function(e,t,i,o){var a=this.da;this.S.fillText(e,n(t,i,a),r(t,i,a),y.zc(o)?o*a[0]:1e20)},e.prototype.setLineDash=function(e){e=i(e.slice(0),this.da),this.zb[this.zb.length-1]=e,this.S.setLineDash(e)},e})),a=function(){var e=!t.Se()||t.Qe()||t.Kh()?1:7;return{estimate:function(){function t(e){e.beginPath(),s.Ud(e,l)}var r=document.createElement("canvas");r.width=800,r.height=600;var i=r.getContext("2d"),o=r.width;r=r.height;var a,u=0,l=[{x:0,y:100}];for(a=1;6>=a;a++)u=2*a*Math.PI/6,l.push({x:100*Math.sin(u),y:100*Math.cos(u)});a={polygonPlainFill:[t,function(e){e.fillStyle="rgb(255, 0, 0)",e.fill()}],polygonPlainStroke:[t,function(e){e.strokeStyle="rgb(128, 0, 0)",e.lineWidth=2,e.closePath(),e.stroke()}],polygonGradientFill:[t,function(e){var t=e.createRadialGradient(0,0,10,0,0,60);t.addColorStop(0,"rgb(255, 0, 0)"),t.addColorStop(1,"rgb(255, 255, 0)"),e.fillStyle=t,e.fill()}],polygonGradientStroke:[t,function(e){var t=e.createLinearGradient(-100,-100,100,100);t.addColorStop(0,"rgb(224, 0, 0)"),t.addColorStop(1,"rgb(32, 0, 0)"),e.strokeStyle=t,e.lineWidth=2,e.closePath(),e.stroke()}],polygonExposureShadow:[t,function(e){e.shadowBlur=50,e.shadowColor="rgba(0, 0, 0, 1)",e.fillStyle="rgba(0, 0, 0, 1)",e.globalCompositeOperation="source-over",e.fill(),e.shadowBlur=0,e.shadowColor="transparent",e.globalCompositeOperation="destination-out",e.fill()}],labelPlainFill:[function(e){e.fillStyle="#000",e.font="24px sans-serif",e.textAlign="center"},function(e){e.fillText("Some text",0,-16),e.fillText("for testing purposes",0,16)}]},u=100/Object.keys(a).length;var c,h=n.now(),f={};for(c in a){var d=a[c],p=n.now(),g=0;do{i.save(),i.translate(Math.random()*o,Math.random()*r);var b=3*Math.random()+.5;for(i.scale(b,b),b=0;b<d.length;b++)d[b](i);i.restore(),g++,b=n.now()}while(b-p<u);f[c]=e*(b-p)/g}return f.total=n.now()-h,f}}}(),s={Ud:function(e,t){var n=t[0];e.moveTo(n.x,n.y);for(var r=t.length-1;0<r;r--)n=t[r],e.lineTo(n.x,n.y)},Ti:function(e,t,n,r){var i,o=[],a=0,s=t.length;for(i=0;i<s;i++){var u=t[i],l=t[(i+1)%s];u=M.i(u,l),u=Math.sqrt(u),o.push(u),a+=u}n=r*(n+.5*r*a/s),a={};var c={},h={};for(i=0;i<s;i++){u=t[i],l=t[(i+1)%s],r=t[(i+2)%s];var f=o[(i+1)%s];f=Math.min(.5,n/f),M.ga(1-f,l,r,c),M.ga(f,l,r,h),0==i&&(M.ga(Math.min(.5,n/o[0]),u,l,a),e.moveTo(a.x,a.y)),e.quadraticCurveTo(l.x,l.y,c.x,c.y),e.lineTo(h.x,h.y)}return!0}};function u(e){function t(){return"embedded"===i.getAttribute("data-foamtree")}function n(e){h[e]&&(h[e].style.opacity=d*f[e])}function r(e){e.width=Math.round(a*e.B),e.height=Math.round(s*e.B)}var i,o,a,s,u,l,c=[],h={},f={},d=0;this.M=function(n){0!==(i=n).clientWidth&&0!==i.clientHeight||E.i("element has zero dimensions: "+i.clientWidth+" x "+i.clientHeight+"."),i.innerHTML="",a=i.clientWidth,s=i.clientHeight,u=0!==a?a:void 0,l=0!==s?s:void 0,t()&&E.i("visualization already embedded in the element."),i.setAttribute("data-foamtree","embedded"),(o=document.createElement("div")).style.width="100%",o.style.height="100%",o.style.position="relative",i.appendChild(o),e.j.D("stage:initialized",this,o,a,s)},this.Za=function(){t()&&(i.removeAttribute("data-foamtree"),c=[],h={},i.removeChild(o),e.j.D("stage:disposed",this,o))},this.u=function(){if(a=i.clientWidth,s=i.clientHeight,0!==a&&0!==s&&(a!==u||s!==l)){for(var t=c.length-1;0<=t;t--)r(c[t]);e.j.D("stage:resized",u,l,a,s),u=a,l=s}},this.Hi=function(e,t){e.B=t,r(e)},this.dc=function(t,i,a){var s=document.createElement("canvas");return s.setAttribute("style","position: absolute; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"),s.B=i,r(s),c.push(s),h[t]=s,f[t]=1,n(t),a||o.appendChild(s),e.j.D("stage:newLayer",t,s),s},this.$b=function(e,t){return y.V(t)||(f[e]=t,n(e)),f[e]},this.i=function(e){return y.V(e)||(d=e,y.Aa(h,(function(e,t){n(t)}))),d}}function l(e){function t(e,t,n){return m=!0,p.x=0,p.y=0,g.x=0,g.y=0,a=f,s.x=d.x,s.y=d.y,t(),u*=e,l=n?u/a:e,l=Math.max(.25/a,l),!0}function n(e,t){return t.x=e.x/f+d.x,t.y=e.y/f+d.y,t}function r(e,t,n,r,i,o,a,s,u){var l=(e-n)*(o-s)-(t-r)*(i-a);return!(1e-5>Math.abs(l))&&(u.x=((e*r-t*n)*(i-a)-(e-n)*(i*s-o*a))/l,u.y=((e*r-t*n)*(o-s)-(t-r)*(i*s-o*a))/l,!0)}var i,o,a=1,s={x:0,y:0},u=1,l=1,c=1,h={x:0,y:0},f=1,d={x:0,y:0},p={x:0,y:0},g={x:0,y:0},b={x:0,y:0,w:0,o:0},v={x:0,y:0,w:0,o:0,scale:1},m=!0;e.j.subscribe("stage:initialized",(function(e,t,n,r){i=n,o=r,b.x=0,b.y=0,b.w=n,b.o=r,v.x=0,v.y=0,v.w=n,v.o=r,v.scale=1})),e.j.subscribe("stage:resized",(function(e,t,n,r){function a(e){e.x*=l,e.y*=c}function u(e){a(e),e.w*=l,e.o*=c}i=n,o=r;var l=n/e,c=r/t;a(s),a(d),a(h),a(p),a(g),u(b),u(v)})),this.Nb=function(e,r){return t(r,(function(){n(e,h)}),!0)},this.ga=function(e,n){if(1==Math.round(1e4*n)/1e4){n=b.x-d.x;var i=b.y-d.y;return t(1,(function(){}),!0),this.i(-n,-i)}return t(n,(function(){for(var t=!1;!t;){t=Math.random();var n=Math.random(),i=Math.random(),o=Math.random();t=r(e.x+t*e.w,e.y+n*e.o,b.x+t*b.w,b.y+n*b.o,e.x+i*e.w,e.y+o*e.o,b.x+i*b.w,b.y+o*b.o,h)}}),!0)},this.ic=function(e,n){var a=e.w/e.o,s=i/o;if(a<s){var u=e.o*s,l=e.o;a=e.x-.5*(u-e.w),s=e.y}else a>s?(u=e.w,l=e.w*o/i,a=e.x,s=e.y-.5*(l-e.o)):(a=e.x,s=e.y,u=e.w,l=e.o);return a-=u*n,u*=1+2*n,r(a,s-=l*n,d.x,d.y,a+u,s,d.x+i/f,d.y,h)?t(i/f/u,y.qa,!1):(m=!1,this.i(f*(d.x-a),f*(d.y-s)))},this.i=function(e,t){return e=Math.round(1e4*e)/1e4,t=Math.round(1e4*t)/1e4,g.x+=e/f,g.y+=t/f,0!==e||0!==t},this.reset=function(e){return e&&this.content(0,0,i,o),this.ga({x:b.x+d.x,y:b.y+d.y,w:b.w/f,o:b.o/f},c/u)},this.Fb=function(e){c=Math.min(1,Math.round(1e4*(e||u))/1e4)},this.u=function(){return d.x<b.x?(b.x-d.x)*f:d.x+i/f>b.x+b.w?-(d.x+i/f-b.x-b.w)*f:0},this.H=function(){return d.y<b.y?(b.y-d.y)*f:d.y+o/f>b.y+b.o?-(d.y+o/f-b.y-b.o)*f:0},this.update=function(e){var t=Math.abs(Math.log(l));6>t?t=2:(t/=4,t+=3*t*(1<l?e:1-e)),t=1<l?Math.pow(e,t):1-Math.pow(1-e,t),f=a*(t=(m?t:1)*(l-1)+1),d.x=h.x-(h.x-s.x)/t,d.y=h.y-(h.y-s.y)/t,d.x-=p.x*(1-e)+g.x*e,d.y-=p.y*(1-e)+g.y*e,1===e&&(p.x=g.x,p.y=g.y),v.x=d.x,v.y=d.y,v.w=i/f,v.o=o/f,v.scale=f},this.T=function(e){return e.x=v.x,e.y=v.y,e.scale=v.scale,e},this.absolute=function(e,t){return n(e,t||{})},this.Uc=function(e,t){return(t=t||{}).x=(e.x-d.x)*f,t.y=(e.y-d.y)*f,t},this.pc=function(e){return this.scale()<c/e},this.zd=function(){return y.od(f,1)},this.scale=function(){return Math.round(1e4*f)/1e4},this.content=function(e,t,n,r){b.x=e,b.y=t,b.w=n,b.o=r},this.rc=function(e,t){var n;for(n=e.length-1;0<=n;n--){var r=e[n];r.save(),r.scale(f,f),r.translate(-d.x,-d.y)}for(t(v),n=e.length-1;0<=n;n--)(r=e[n]).restore()}}var c=new function(){function e(e){if("hsl"==e.model||"hsla"==e.model)return e;var t=e.r/=255,n=e.g/=255,r=e.b/=255,i=Math.max(t,n,r),o=Math.min(t,n,r),a=(i+o)/2;if(i==o)var s=o=0;else{var u=i-o;switch(o=.5<a?u/(2-i-o):u/(i+o),i){case t:s=(n-r)/u+(n<r?6:0);break;case n:s=(r-t)/u+2;break;case r:s=(t-n)/u+4}s/=6}return e.h=360*s,e.s=100*o,e.l=100*a,e.model="hsl",e}var t={h:0,s:0,l:0,a:1,model:"hsla"};this.u=function(n){return y.Ac(n)?e(c.ga(n)):y.wb(n)?e(n):t},this.ga=function(e){var n;return(n=/rgba\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:parseFloat(n[4]),model:"rgba"}:(n=/hsla\(\s*([^,\s]+)\s*,\s*([^,%\s]+)%\s*,\s*([^,\s%]+)%\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:parseFloat(n[4]),model:"hsla"}:(n=/rgb\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&4==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:1,model:"rgb"}:(n=/hsl\(\s*([^,\s]+)\s*,\s*([^,\s%]+)%\s*,\s*([^,\s%]+)%\s*\)/.exec(e))&&4==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:1,model:"hsl"}:(n=/#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})/.exec(e))&&4==n.length?{r:parseInt(n[1],16),g:parseInt(n[2],16),b:parseInt(n[3],16),a:1,model:"rgb"}:(n=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/.exec(e))&&4==n.length?{r:17*parseInt(n[1],16),g:17*parseInt(n[2],16),b:17*parseInt(n[3],16),a:1,model:"rgb"}:t},this.T=function(e){function t(e,t,n){return 0>n&&(n+=1),1<n&&--n,n<1/6?e+6*(t-e)*n:.5>n?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function n(e,t,n){return Math.sqrt(e*e*.241+t*t*.691+n*n*.068)/255}if("rgb"==e.model||"rgba"==e.model)return n(e.r,e.g,e.b);var r=e.l/100,i=e.s/100,o=e.h/360;if(0==e.zj)r=e=o=r;else{var a=2*r-(i=.5>r?r*(1+i):r+i-r*i);r=t(a,i,o+1/3),e=t(a,i,o),o=t(a,i,o-1/3)}return n(255*r,255*e,255*o)},this.wa=function(e){if(y.Ac(e))return e;if(!y.wb(e))return"#000";switch(e.model){case"hsla":return c.sa(e);case"hsl":return c.H(e);case"rgba":return c.ua(e);case"rgb":return c.ta(e);default:return"#000"}},this.ua=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+","+e.a+")"},this.ta=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+")"},this.sa=function(e){return"hsla("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%,"+e.a+")"},this.H=function(e){return"hsl("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%)"},this.i=function(e,t,n){return"hsl("+(.5+e|0)+","+(.5+t|0)+"%,"+(.5+n|0)+"%)"}};function h(){var e,t=!1,n=[],r=this,i=new function(){this.then=function(i){return i&&(t?i.apply(r,e):n.push(i)),this},this.Fg=function(e){return r=e,{then:this.then}}};this.resolve=function(){e=arguments;for(var i=0;i<n.length;i++)n[i].apply(r,e);return t=!0,this},this.promise=function(){return i}}function f(e){var t=new h,n=e.length;if(0<e.length)for(var r=e.length-1;0<=r;r--)e[r].then((function(){0==--n&&t.resolve()}));else t.resolve();return t.promise()}function d(e){var t=0;this.i=function(){t++},this.u=function(){0===--t&&e()},this.clear=function(){t=0},this.initial=function(){return 0===t}}var g={oe:function(e,t,n,r){r=r||{};try{var i=e.getBoundingClientRect()}catch(p){if(!g.Ih){g.Ih=!0,window.console.log("getBoundingClientRect() failed."),window.console.log("Element",e);for(var o=(i=window.console).log;null!==e.parentElement;)e=e.parentElement;o.call(i,"Attached to DOM",e===document.body.parentElement)}i={left:0,top:0}}return r.x=t-i.left,r.y=n-i.top,r}};function b(){var e=document,t={};this.addEventListener=function(n,r,i){var o=t[n];o||(o=[],t[n]=o),o.push(r),e.addEventListener(n,r,i)},this.i=function(){y.Aa(t,(function(t,n){for(var r=t.length-1;0<=r;r--)e.removeEventListener(n,t[r])}))}}function v(e){function t(e){return function(t){n(t)&&e.apply(this,arguments)}}function n(t){for(t=t.target;t;){if(t===e)return!0;t=t.parentElement}return!1}function r(e,t,n){i(e,n=n||{});for(var r=0;r<t.length;r++)t[r].call(e.target,n);return i(e,n),(void 0===n.Db&&n.$h||"prevent"===n.Db)&&e.preventDefault(),n}function i(t,n){return g.oe(e,t.clientX,t.clientY,n),n.altKey=t.altKey,n.metaKey=t.metaKey,n.ctrlKey=t.ctrlKey,n.shiftKey=t.shiftKey,n.lb=3===t.which,n}var o=new b,a=[],s=[],u=[],l=[],c=[],h=[],f=[],d=[],p=[],v=[],m=[];this.i=function(e){a.push(e)},this.u=function(e){c.push(e)},this.sa=function(e){s.push(e)},this.wa=function(e){u.push(e)},this.Ka=function(e){l.push(e)},this.ua=function(e){m.push(e)},this.ta=function(e){h.push(e)},this.Ja=function(e){f.push(e)},this.ga=function(e){d.push(e)},this.H=function(e){p.push(e)},this.T=function(e){v.push(e)},this.Za=function(){o.i()};var y,C,_,w,x={x:0,y:0},A={x:0,y:0},S=!1,T=!1;o.addEventListener("mousedown",t((function(t){if(t.target!==e){var n=r(t,u);A.x=n.x,A.y=n.y,x.x=n.x,x.y=n.y,S=!0,r(t,d),C=!1,y=window.setTimeout((function(){100>M.i(x,n)&&(window.clearTimeout(w),r(t,s),C=!0)}),400)}}))),o.addEventListener("mouseup",(function(e){var t=r(e,l);S&&(T&&r(e,v),window.clearTimeout(y),C||T||!n(e)||(t={x:t.x,y:t.y},_&&100>M.i(t,_)?r(e,c):r(e,a),_=t,w=window.setTimeout((function(){_=null}),350)),T=S=!1)})),o.addEventListener("mousemove",(function(e){var t=i(e,{});n(e)&&r(e,h,{type:"move"}),x.x=t.x,x.y=t.y,S&&!T&&100<M.i(A,x)&&(T=!0),T&&r(e,p,t)})),o.addEventListener("mouseout",t((function(e){r(e,f,{type:"out"})}))),o.addEventListener("wheel",t((function(e){if("deltaY"in e)var t=e.deltaY;else t=0,"detail"in e&&(t=e.detail),"wheelDelta"in e&&(t=-e.wheelDelta/120),"wheelDeltaY"in e&&(t=-e.wheelDeltaY/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=0),t*=10;t&&e.deltaMode&&(t=1===e.deltaMode?67*t:800*t),r(e,m,{ed:-t/200,$h:!0})})),{passive:!1}),o.addEventListener("contextmenu",t((function(e){e.preventDefault()})))}var m=function(){function e(e){return function(t){return Math.pow(t,e)}}function t(e){return function(t){return 1-Math.pow(1-t,e)}}function n(e){return function(t){return 1>(t*=2)?.5*Math.pow(t,e):1-.5*Math.abs(Math.pow(2-t,e))}}function r(e){return function(t){for(var n=0;n<e.length;n++)t=(0,e[n])(t);return t}}return{ia:function(e){switch(e){case"linear":return m.Ab;case"bounce":return m.tg;case"squareIn":return m.Tf;case"squareOut":return m.Gb;case"squareInOut":return m.Uf;case"cubicIn":return m.wg;case"cubicOut":return m.fe;case"cubicInOut":return m.xg;case"quadIn":return m.si;case"quadOut":return m.ui;case"quadInOut":return m.ti;default:return m.Ab}},Ab:function(e){return e},tg:r([n(2),function(e){return 0===e?0:1===e?1:e*(e*(e*(e*(25.9425*e-85.88)+105.78)-58.69)+13.8475)}]),Tf:e(2),Gb:t(2),Uf:n(2),wg:e(3),fe:t(3),xg:n(3),si:e(2),ui:t(2),ti:n(2),oj:r}}(),y={V:function(e){return void 0===e},Re:function(e){return null===e},zc:function(e){return"[object Number]"===Object.prototype.toString.call(e)},Ac:function(e){return"[object String]"===Object.prototype.toString.call(e)},Pe:function(e){return"function"==typeof e},wb:function(e){return e===Object(e)},od:function(e,t){return 1e-6>e-t&&-1e-6<e-t},Ne:function(e){return y.V(e)||y.Re(e)||y.Ac(e)&&!/\S/.test(e)},has:function(e,t){return e&&e.hasOwnProperty(t)},bb:function(e,t){if(e)for(var n=t.length-1;0<=n;n--)if(e.hasOwnProperty(t[n]))return!0;return!1},extend:function(e){return y.Bg(Array.prototype.slice.call(arguments,1),(function(t){if(t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})),e},xj:function(e,t){return e.map((function(e){return e[t]}),[])},Bg:function(e,t,n){null!=e&&(e.forEach?e.forEach(t,n):y.Aa(e,t,n))},Aa:function(e,t,n){for(var r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))break},I:function(){for(var e=0;e<arguments.length;e++){var t=arguments[e];if(!(y.V(t)||y.zc(t)&&isNaN(t)||y.Ac(t)&&y.Ne(t)))return t}},If:function(e,t){0<=(t=e.indexOf(t))&&e.splice(t,1)},yg:function(e,t,n){var r;return function(){var i=this,o=arguments,a=n&&!r;clearTimeout(r),r=setTimeout((function(){r=null,n||e.apply(i,o)}),t),a&&e.apply(i,o)}},defer:function(e){setTimeout(e,1)},vj:function(e){return e},qa:function(){}},C=function(e,n,r){return t.Fh()?function(){var t=n+":"+JSON.stringify(arguments),i=window.localStorage.getItem(t);return i&&(i=JSON.parse(i)),i&&Date.now()-i.t<r?i.v:(i=e.apply(this,arguments),window.localStorage.setItem(t,JSON.stringify({v:i,t:Date.now()})),i)}:e},_=function(e,t){function n(){var n=[];if(Array.isArray(e))for(var r=0;r<e.length;r++){var i=e[r];i&&n.push(i.apply(t,arguments))}else e&&n.push(e.apply(t,arguments));return n}return n.empty=function(){return 0===e.length&&!y.Pe(e)},n};function w(){var e={};this.subscribe=function(t,n){var r=e[t];r||(r=[],e[t]=r),r.push(n)},this.D=function(t,n){var r=e[t];if(r)for(var i=Array.prototype.slice.call(arguments,1),o=0;o<r.length;o++)r[o].apply(this,i)}}var x=function(e){for(var t="",n=0;n<e.length;n++)t+=String.fromCharCode(1^e.charCodeAt(n));return t};function A(e){function t(t,n,u){var l,c=this,h=0;this.id=o++,this.name=u||"{unnamed on "+t+"}",this.target=function(){return t},this.xb=function(){return-1!=s.indexOf(c)},this.start=function(){if(!c.xb()){if(-1==s.indexOf(c)){var t=a.now();!0===c.af(t)&&(s=s.slice()).push(c)}0<s.length&&e.repeat(r)}return this},this.stop=function(){for(i(c);l<n.length;l++){var e=n[l];e.Xa&&e.gb.call()}return this},this.af=function(e){if(h++,0!==n.length){if(y.V(l)){var t=n[l=0];t.before&&t.before.call(t,e,h,c)}else t=n[l];for(;l<n.length;){if(t.gb&&t.gb.call(t,e,h,c))return!0;t.after&&t.after.call(t,e,h,c),y.V(l)&&(l=-1),++l<n.length&&((t=n[l]).before&&t.before.call(t,e,h,c))}}return!1}}function r(){!function(){var e=a.now();s.forEach((function(t){!0!==t.af(e)&&i(t)}))}(),0==s.length&&e.cancel(r)}function i(e){s=s.filter((function(t){return t!==e}))}var o=0,a=n.create(),s=[];this.i=function(){for(var e=s.length-1;0<=e;e--)s[e].stop();s=[]},this.K=function(){function e(){}function n(e){function t(e){return y.Pe(e)?e.call(void 0):e}var n,r,i=e.target,o=e.duration,s=e.ba;this.before=function(){for(var o in n={},e.P)i.hasOwnProperty(o)&&(n[o]={start:y.V(e.P[o].start)?i[o]:t(e.P[o].start),end:y.V(e.P[o].end)?i[o]:t(e.P[o].end),easing:y.V(e.P[o].easing)?m.Ab:e.P[o].easing});r=a.now()},this.gb=function(){var e=a.now()-r;for(var t in e=0===o?1:Math.min(o,e)/o,n){var u=n[t];i[t]=u.start+(u.end-u.start)*u.easing(e)}return s&&s.call(i,e),1>e}}function r(e,t,n){this.Xa=n,this.gb=function(){return e.call(t),!1}}function i(e){var t;this.before=function(n,r){t=r+e},this.gb=function(e,n){return n<t}}function o(e){var t;this.before=function(n){t=n+e},this.gb=function(e){return e<t}}function u(e){this.before=function(){e.forEach((function(e){e.start()}))},this.gb=function(){for(var t=0;t<e.length;t++)if(e[t].xb())return!0;return!1}}return e.A=function(e,a){return new function(){function s(t,n,i,o){return n?(y.V(i)&&(i=e),t.Mb(new r(n,i,o))):t}var l=[];this.Mb=function(e){return l.push(e),this},this.wait=function(e){return this.Mb(new o(e))},this.Xd=function(e){return this.Mb(new i(e||1))},this.call=function(e,t){return s(this,e,t,!1)},this.Xa=function(e,t){return s(this,e,t,!0)},this.fa=function(t){return y.V(t.target)&&(t.target=e),this.Mb(new n(t))},this.Qa=function(e){return this.Mb(new u(e))},this.done=function(){return new t(e,l,a)},this.start=function(){return this.done().start()},this.i=function(){var e=new h;return this.Xd().call(e.resolve).done(),e.promise()},this.Ta=function(){var e=this.i();return this.start(),e}}},e.jc=function(t){return function(e){return y.V(e)?s.slice():s.filter((function(t){return t.target()===e}))}(t).forEach((function(e){e.stop()})),e.A(t,void 0)},e}()}var S=function(){var e={ne:function(e,t){if(e.m){e=e.m;for(var n=0;n<e.length;n++)t(e[n],n)}},sc:function(t,n){if(t.m){t=t.m;for(var r=0;r<t.length;r++)if(!1===e.sc(t[r],n)||!1===n(t[r],r))return!1}}};return e.L=e.sc,e.tc=function(t,n){if(t.m){t=t.m;for(var r=0;r<t.length;r++)if(!1===n(t[r],r)||!1===e.tc(t[r],n))return!1}},e.za=function(t,n){if(t.m)for(var r=t.m,i=0;i<r.length;i++)if(!1===e.za(r[i],n))return!1;return n(t)},e.pj=e.za,e.fd=function(t,n){!1!==n(t)&&e.tc(t,n)},e.uc=function(t,n){var r=[];return e.tc(t,(function(e){r.push(e)})),n?r.filter(n):r},e.me=function(e,t){for(e=e.parent;e&&!1!==t(e);)e=e.parent},e.Jh=function(e,t){for(e=e.parent;e&&e!==t;)e=e.parent;return!!e},e}(),M=new function(){function e(e,t){var n=e.x-t.x;return n*n+(e=e.y-t.y)*e}function t(e,t,n){for(var r=0;r<e.length;r++){var i=M.T(e[r],e[r+1]||e[0],t,n,!0);if(i)return i}}return this.T=function(e,t,n,r,i){var o=e.x;e=e.y;var a=t.x-o;t=t.y-e;var s=n.x,u=n.y;n=r.x-s;var l=r.y-u;if(!(1e-12>=(r=a*l-n*t)&&-1e-12<=r)&&(n=((s-=o)*l-n*(u-=e))/r,0<=(r=(s*t-a*u)/r)&&(i||1>=r)&&0<=n&&1>=n))return{x:o+a*n,y:e+t*n}},this.Lb=function(e,t,n,r){var i=e.x;e=e.y;var o=t.x-i;t=t.y-e;var a=n.x;n=n.y;var s=r.x-a,u=o*(r=r.y-n)-s*t;if(!(1e-12>=u&&-1e-12<=u)&&(0<=(r=((a-i)*r-s*(n-e))/u)&&1>=r))return{x:i+o*r,y:e+t*r}},this.wa=function(e,n,r){var i=M.u(n,{}),o=M.u(r,{}),a=o.x-i.x,s=o.y-i.y,u=[];for(o=0;o<r.length;o++){var l=r[o];u.push({x:l.x-a,y:l.y-s})}for(r=[],l=[],o=0;o<e.length;o++){var c=e[o],h=t(n,i,c);h?(r.push(h),l.push(t(u,i,c))):(r.push(null),l.push(null))}for(o=0;o<e.length;o++)if(h=r[o],c=l[o],h&&c){n=e[o],u=i;var f=h.x-i.x;if(h=h.y-i.y,1e-12<(h=Math.sqrt(f*f+h*h))){f=n.x-i.x;var d=n.y-i.y;h=Math.sqrt(f*f+d*d)/h,n.x=u.x+h*(c.x-u.x),n.y=u.y+h*(c.y-u.y)}else n.x=u.x,n.y=u.y}for(o=0;o<e.length;o++)(l=e[o]).x+=a,l.y+=s},this.F=function(e,t){if(0!==e.length){for(var n,r,i=n=e[0].x,o=r=e[0].y,a=e.length;0<--a;)i=Math.min(i,e[a].x),n=Math.max(n,e[a].x),o=Math.min(o,e[a].y),r=Math.max(r,e[a].y);return t.x=i,t.y=o,t.w=n-i,t.o=r-o,t}},this.H=function(e){return[{x:e.x,y:e.y},{x:e.x+e.w,y:e.y},{x:e.x+e.w,y:e.y+e.o},{x:e.x,y:e.y+e.o}]},this.u=function(e,t){for(var n=0,r=0,i=e.length,o=e[0],a=0,s=1;s<i-1;s++){var u=e[s],l=e[s+1],c=o.y+u.y+l.y,h=(u.x-o.x)*(l.y-o.y)-(l.x-o.x)*(u.y-o.y);n+=h*(o.x+u.x+l.x),r+=h*c,a+=h}return t.x=n/(3*a),t.y=r/(3*a),t.ha=a/2,t},this.Ja=function(e,t){this.u(e,t),t.r=Math.sqrt(t.ha/Math.PI)},this.sa=function(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=e[n+1]||e[0];if(0>(t.y-r.y)*(i.x-r.x)-(t.x-r.x)*(i.y-r.y))return!1}return!0},this.Vc=function(e,t,n){var r=e.x,i=t.x;if(e.x>t.x&&(r=t.x,i=e.x),i>n.x+n.w&&(i=n.x+n.w),r<n.x&&(r=n.x),r>i)return!1;var o=e.y,a=t.y,s=t.x-e.x;return 1e-7<Math.abs(s)&&(o=(a=(t.y-e.y)/s)*r+(e=e.y-a*e.x),a=a*i+e),o>a&&(r=a,a=o,o=r),a>n.y+n.o&&(a=n.y+n.o),o<n.y&&(o=n.y),o<=a},this.Ka=function(n,r,i,o,a){var s;function u(i,o,a){if(r.x===h.x&&r.y===h.y)return a;var u=t(n,r,h),f=Math.sqrt(e(u,r)/(i*i+o*o));return f<l?(l=f,s=u.x,c=u.y,0!==o?Math.abs(c-r.y)/Math.abs(o):Math.abs(s-r.x)/Math.abs(i)):a}o=y.I(o,.5),a=y.I(a,.5),i=y.I(i,1);var l=Number.MAX_VALUE,c=s=0,h={x:0,y:0},f=o*i;i*=1-o,o=1-a,h.x=r.x-f,h.y=r.y-a;var d=u(f,a,d);return h.x=r.x+i,h.y=r.y-a,d=u(i,a,d),h.x=r.x-f,h.y=r.y+o,d=u(f,o,d),h.x=r.x+i,h.y=r.y+o,u(i,o,d)},this.pb=function(e,t){function n(e,t,n){var r=t.x,i=n.x;t=t.y;var o=i-r,a=(n=n.y)-t;return Math.abs(a*e.x-o*e.y-r*n+i*t)/Math.sqrt(o*o+a*a)}for(var r=e.length,i=n(t,e[r-1],e[0]),o=0;o<r-1;o++){var a=n(t,e[o],e[o+1]);a<i&&(i=a)}return i},this.ua=function(e,t,n){var r;n={x:t.x+Math.cos(n),y:t.y-Math.sin(n)};var i=[],o=[],a=e.length;for(r=0;r<a;r++){var s=M.Lb(e[r],e[(r+1)%a],t,n);if(s&&(i.push(s),2==o.push(r)))break}if(2==i.length){s=i[0],i=i[1];var u=o[0];o=o[1];var l=[i,s];for(r=u+1;r<=o;r++)l.push(e[r]);for(r=[s,i];o!=u;)o=(o+1)%a,r.push(e[o]);return e=[l,r],a=n.x-t.x,r=i.x-s.x,0===a&&(a=n.y-t.y,r=i.y-s.y),(0>a?-1:0<a?1:0)!=(0>r?-1:0<r?1:0)&&e.reverse(),e}},this.ga=function(e,t,n,r){return r.x=e*(t.x-n.x)+n.x,r.y=e*(t.y-n.y)+n.y,r},this.i=e,this.ta=function(e,n,r){if(y.zc(n))var i=2*Math.PI*n/360;else switch(i=M.F(e,{}),n){case"random":i=Math.random()*Math.PI*2;break;case"top":i=Math.atan2(-i.o,0);break;case"bottom":i=Math.atan2(i.o,0);break;case"left":i=Math.atan2(0,-i.w);break;case"right":i=Math.atan2(0,i.w);break;case"topleft":i=Math.atan2(-i.o,-i.w);break;case"topright":i=Math.atan2(-i.o,i.w);break;case"bottomleft":i=Math.atan2(i.o,-i.w);break;default:i=Math.atan2(i.o,i.w)}return e=t(e,n=M.u(e,{}),{x:n.x+Math.cos(i),y:n.y+Math.sin(i)}),M.ga(r,e,n,{})},this},T=new function(){function e(e,t){this.face=e,this.Rc=t,this.ec=this.Lc=null}function t(e,t,n){this.la=[e,t,n],this.J=Array(3);var r=t.y-e.y,i=n.z-e.z,o=t.x-e.x;t=t.z-e.z;var a=n.x-e.x;e=n.y-e.y,this.Ha={x:r*i-t*e,y:t*a-o*i,z:o*e-r*a},this.Ya=[],this.ad=this.visible=!1}this.i=function(r){function o(t,n,r){var i=t.la[0],o=t.Ha,a=o.x,l=o.y;o=o.z;var c=Array(u),h=(n=n.Ya).length;for(s=0;s<h;s++){var f=n[s].Rc;c[f.index]=!0,0>a*(f.x-i.x)+l*(f.y-i.y)+o*(f.z-i.z)&&e.add(t,f)}for(h=(n=r.Ya).length,s=0;s<h;s++)!0!==c[(f=n[s].Rc).index]&&0>a*(f.x-i.x)+l*(f.y-i.y)+o*(f.z-i.z)&&e.add(t,f)}var a,s,u=r.length;for(a=0;a<u;a++)r[a].index=a,r[a].Pb=null;var l,c=[];if(0<(l=function(){function n(e,n,r,i){var o={x:n.x-e.x,y:n.y-e.y,z:n.z-e.z},a=r.x-e.x,s=r.y-e.y,u=r.z-e.z,l=o.y*u-o.z*s,c=o.z*a-o.x*u;return o=o.x*s-o.y*a,l*i.x+c*i.y+o*i.z>l*e.x+c*e.y+o*e.z?new t(e,n,r):new t(r,n,e)}function i(e,t,n,r){function i(e,t,n){return(e=e.la)[((t=e[0]==t?0:e[1]==t?1:2)+1)%3]!=n?(t+2)%3:t}t.J[i(t,n,r)]=e,e.J[i(e,r,n)]=t}if(4>u)return 0;var o=r[0],a=r[1],s=r[2],l=r[3],h=n(o,a,s,l),f=n(o,s,l,a),d=n(o,a,l,s),p=n(a,s,l,o);for(i(h,f,s,o),i(h,d,o,a),i(h,p,a,s),i(f,d,l,o),i(f,p,s,l),i(d,p,l,a),c.push(h,f,d,p),o=4;o<u;o++)for(a=r[o],s=0;4>s;s++)h=(l=c[s]).la[0],0>(f=l.Ha).x*(a.x-h.x)+f.y*(a.y-h.y)+f.z*(a.z-h.z)&&e.add(l,a);return 4}())){for(;l<u;){var h=r[l];if(h.Pb){for(a=h.Pb;null!==a;)a.face.visible=!0,a=a.ec;a=0;e:for(;a<c.length;a++){var f=c[a];if(!1===f.visible){var d=f.J;for(s=0;3>s;s++)if(!0===d[s].visible){var p=f,g=s;break e}}}f=[],d=[];var b=p,v=g;do{if(f.push(b),d.push(v),v=(v+1)%3,!1===b.J[v].visible)do{for(a=b.la[v],b=b.J[v],s=0;3>s;s++)b.la[s]==a&&(v=s)}while(!1===b.J[v].visible&&(b!==p||v!==g))}while(b!==p||v!==g);var m=null,y=null;for(a=0;a<f.length;a++){b=f[a],v=d[a];var C=b.J[v],_=b.la[(v+1)%3],w=b.la[v],x=_.y-h.y,A=w.z-h.z,S=_.x-h.x,M=_.z-h.z,T=w.x-h.x,k=w.y-h.y;if(0<i.length){var z=i.pop();z.la[0]=h,z.la[1]=_,z.la[2]=w,z.Ha.x=x*A-M*k,z.Ha.y=M*T-S*A,z.Ha.z=S*k-x*T,z.Ya.length=0,z.visible=!1,z.ad=!0}else z={la:[h,_,w],J:Array(3),Ha:{x:x*A-M*k,y:M*T-S*A,z:S*k-x*T},Ya:[],visible:!1};c.push(z),b.J[v]=z,z.J[1]=b,null!==y&&(y.J[0]=z,z.J[2]=y),y=z,null===m&&(m=z),o(z,b,C)}for(y.J[0]=m,m.J[2]=y,a=[],s=0;s<c.length;s++)if(!0===(f=c[s]).visible){for(b=(d=f.Ya).length,h=0;h<b;h++)m=(v=d[h]).Lc,y=v.ec,null!==m&&(m.ec=y),null!==y&&(y.Lc=m),null===m&&(v.Rc.Pb=y),n.push(v);f.ad&&i.push(f)}else a.push(f);c=a}l++}for(a=0;a<c.length;a++)(f=c[a]).ad&&i.push(f)}return{pe:c}},e.add=function(t,r){if(0<n.length){var i=n.pop();i.face=t,i.Rc=r,i.ec=null,i.Lc=null}else i=new e(t,r);t.Ya.push(i),null!==(t=r.Pb)&&(t.Lc=i),i.ec=t,r.Pb=i};for(var n=Array(2e3),r=0;r<n.length;r++)n[r]=new e(null,null);var i=Array(1e3);for(r=0;r<i.length;r++)i[r]={la:Array(3),J:Array(3),Ha:{x:0,y:0,z:0},Ya:[],visible:!1}},k=new function(){function e(e,t,n,r,i,o,a,s){var u=(e-n)*(o-s)-(t-r)*(i-a);if(!(1e-12>Math.abs(u)))return{x:((e*r-t*n)*(i-a)-(e-n)*(i*s-o*a))/u,y:((e*r-t*n)*(o-s)-(t-r)*(i*s-o*a))/u}}return this.i=function(t,n){for(var r=t[0],i=r.x,o=r.y,a=r.x,s=r.y,u=t.length-1;0<u;u--)r=t[u],i=Math.min(i,r.x),o=Math.min(o,r.y),a=Math.max(a,r.x),s=Math.max(s,r.y);if(!(a-i<3*n||s-o<3*n)){e:{for(null==(r=!0)&&(r=!1),i=[],o=t.length,a=0;a<=o;a++){s=t[a%o],u=t[(a+1)%o];var l=t[(a+2)%o],c=u.x-s.x,h=u.y-s.y,f=Math.sqrt(c*c+h*h),d=n*c/f,p=n*h/f;if(c=l.x-u.x,h=l.y-u.y,c=n*c/(f=Math.sqrt(c*c+h*h)),h=n*h/f,(s=e(s.x-p,s.y+d,u.x-p,u.y+d,u.x-h,u.y+c,l.x-h,l.y+c))&&(i.push(s),l=i.length,r&&3<=l&&(s=i[l-3],u=i[l-2],l=i[l-1],0>(u.x-s.x)*(l.y-s.y)-(l.x-s.x)*(u.y-s.y)))){r=void 0;break e}}i.shift(),r=3>i.length?void 0:i}if(!r)e:{for(i=t.slice(0),r=0;r<t.length;r++){if(a=t[r%t.length],l=(u=t[(r+1)%t.length]).x-a.x,o=u.y-a.y,l=n*l/(s=Math.sqrt(l*l+o*o)),s=n*o/s,o=a.x-s,a=a.y+l,s=u.x-s,l=u.y+l,0!=i.length){for(p=o-s,h=a-l,d=[],c=f=!0,u=0;u<i.length;u++){var g=p*(a-i[u].y)-(o-i[u].x)*h;1e-12>=g&&-1e-12<=g&&(g=0),d.push(g),0<g&&(f=!1),0>g&&(c=!1)}if(f)i=[];else if(!c){for(p=[],u=0;u<i.length;u++)h=(u+1)%i.length,f=d[u],c=d[h],0<=f&&p.push(i[u]),(0<f&&0>c||0>f&&0<c)&&p.push(e(i[u].x,i[u].y,i[h].x,i[h].y,o,a,s,l));i=p}}if(3>i.length){r=void 0;break e}}r=i}return r}},this},z=new function(){function e(e){for(var t=e[0].x,n=e[0].y,r=t,i=n,o=1;o<e.length;o++){var a=e[o];t=Math.min(t,a.x),n=Math.min(n,a.y),r=Math.max(r,a.x),i=Math.max(i,a.y)}return[{x:t+2*(e=r-t),y:n+2*(i-=n),w:0},{x:t+2*e,y:n-2*i,w:0},{x:t-2*e,y:n+2*i,w:0}]}this.i=function(t,n){function r(e){var t=[e[0]],n=e[0][0],r=e[0][1],i=e.length,o=1;e:for(;o<i;o++)for(var a=1;a<i;a++){var s=e[a];if(null!==s){if(s[1]===n){if(t.unshift(s),n=s[0],e[a]=null,t.length===i)break e;continue}if(s[0]===r&&(t.push(s),r=s[1],e[a]=null,t.length===i))break e}}return t[0][0]!=t[i-1][1]&&t.push([t[i-1][1],t[0][0]]),t}function i(e,t,n,r){var i,o,a=[],s=[],u=n.length,l=t.length,c=0,h=-1,f=-1,d=r;for(r=0;r<u;r++){var p=(d+1)%u,g=n[d][0],b=n[p][0];if(1e-12<M.i(g.ea,b.ea))if(g.jb&&b.jb){var v=[],m=[];for(i=0;i<l;i++){var y=(c+1)%l;if((o=M.T(t[c],t[y],g.ea,b.ea,!1))&&(m.push(c),2===v.push(o)))break;c=y}if(2===v.length){if(i=v[1],g=(o=M.i(g.ea,v[0]))<(i=M.i(g.ea,i))?0:1,o=o<i?1:0,i=m[g],-1===h&&(h=i),-1!==f)for(;i!=f;)f=(f+1)%l,a.push(t[f]),s.push(null);a.push(v[g],v[o]),s.push(n[d][2],null),f=m[o]}}else if(g.jb&&!b.jb)for(i=0;i<l;i++){if(y=(c+1)%l,o=M.T(t[c],t[y],g.ea,b.ea,!1)){if(-1!==f)for(v=f;c!=v;)v=(v+1)%l,a.push(t[v]),s.push(null);a.push(o),s.push(n[d][2]),-1===h&&(h=c);break}c=y}else if(!g.jb&&b.jb)for(i=0;i<l;i++){if(y=(c+1)%l,o=M.T(t[c],t[y],g.ea,b.ea,!1)){a.push(g.ea,o),s.push(n[d][2],null),f=c;break}c=y}else a.push(g.ea),s.push(n[d][2]);d=p}if(0==a.length)s=a=null;else if(-1!==f)for(;h!=f;)f=(f+1)%l,a.push(t[f]),s.push(null);e.C=a,e.J=s}if(1===t.length)t[0].C=n.slice(0),t[0].J=[];else{var o,a=e(n),s=[];for(o=0;o<a.length;o++){var u=a[o];s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y-u.w})}for(o=0;o<t.length;o++)(u=t[o]).C=null,s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y-u.w});var l=T.i(s).pe;for(function(){for(o=0;o<l.length;o++){var e=l[o],t=e.la,n=t[0],r=t[1],i=t[2];t=n.x;var a=n.y;n=n.z;var s=r.x,u=r.y;r=r.z;var c=i.x,h=i.y;i=i.z;var f=t*(u-h)+s*(h-a)+c*(a-u);e.ea={x:-(a*(r-i)+u*(i-n)+h*(n-r))/f/2,y:-(n*(s-c)+r*(c-t)+i*(t-s))/f/2}}}(),function(e){for(o=0;o<l.length;o++){var t=l[o];t.jb=!M.sa(e,t.ea)}}(n),s=function(e,t){var n,r=Array(t.length);for(n=0;n<r.length;n++)r[n]=[];for(n=0;n<e.length;n++){var i=e[n];if(!(0>i.Ha.z))for(var o=i.J,a=0;a<o.length;a++){var s=o[a];if(!(0>s.Ha.z)){var u=i.la,l=u[(a+1)%3].index;u=u[a].index,2<l&&r[l-3].push([i,s,2<u?t[u-3]:null])}}}return r}(l,t),o=0;o<t.length;o++)if(0!==(u=s[o]).length){var c=t[o],h=(u=r(u)).length,f=-1;for(a=0;a<h;a++)u[a][0].jb&&(f=a);if(0<=f)i(c,n,u,f);else{f=[];var d=[];for(a=0;a<h;a++)1e-12<M.i(u[a][0].ea,u[(a+1)%h][0].ea)&&(f.push(u[a][0].ea),d.push(u[a][2]));c.C=f,c.J=d}c.C&&3>c.C.length&&(c.C=null,c.J=null)}}},this.u=function(t,n){var r,i=!1,o=t.length;for(r=0;r<o;r++){var a=t[r];null===a.C&&(i=!0),a.Yd=a.w}if(i){i=e(n);var s=[];for(r=t.length,a=0;a<i.length;a++){var u=i[a];s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y})}for(a=0;a<r;a++)u=t[a],s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y});for(u=T.i(s).pe,i=Array(r),a=0;a<r;a++)i[a]={};for(s=u.length,a=0;a<s;a++){var l=u[a];if(0<l.Ha.z){var c=l.la,h=c.length;for(l=0;l<h-1;l++){var f=c[l].index-3,d=c[l+1].index-3;0<=f&&0<=d&&(i[f][d]=!0,i[d][f]=!0)}l=c[0].index-3,0<=d&&0<=l&&(i[d][l]=!0,i[l][d]=!0)}}for(a=0;a<r;a++){for(var p in l=i[a],u=t[a],d=Number.MAX_VALUE,s=null,l)l=t[p],d>(c=M.i(u,l))&&(d=c,s=l);u.wj=s,u.Ze=Math.sqrt(d)}for(r=0;r<o;r++)a=t[r],p=Math.min(Math.sqrt(a.w),.95*a.Ze),a.w=p*p;for(this.i(t,n),r=0;r<o;r++)(a=t[r]).Yd!==a.w&&0<a.kc&&(n=Math.min(a.kc,a.Yd-a.w),a.w+=n,a.kc-=n)}}},D=new function(){this.H=function(e){for(var t=0,n=(e=e.m).length,r=0;r<n;r++){var i=e[r];if(i.C){var o=i.x,a=i.y;M.u(i.C,i),t<(i=(0<(o-=i.x)?o:-o)+(0<(i=a-i.y)?i:-i))&&(t=i)}}return t},this.i=function(e,t){var n=e.m;switch(t){case"random":return e.m[Math.floor(n.length*Math.random())];case"topleft":var r=(e=n[0]).x+e.y;for(t=1;t<n.length;t++){var i=n[t],o=i.x+i.y;o<r&&(r=o,e=i)}return e;case"bottomright":for(r=(e=n[0]).x+e.y,t=1;t<n.length;t++)(o=(i=n[t]).x+i.y)>r&&(r=o,e=i);return e;default:for(i=n[0],r=o=M.i(e,i),t=n.length-1;1<=t;t--){var a=n[t];(o=M.i(e,a))<r&&(r=o,i=a)}return i}},this.u=function(e,t,n){var r=e.m;if(r[0].J){var i,o=r.length;for(e=0;e<o;e++)r[e].Sc=!1,r[e].Zb=0;var a=i=0;for((o=[])[i++]=t||r[0],t=t.Zb=0;a<i;)if(!(r=o[a++]).Sc&&r.J){n(r,t++,r.Zb),r.Sc=!0;var s=r.J,u=s.length;for(e=0;e<u;e++){var l=s[e];l&&!0!==l.Sc&&(0===l.Zb&&(l.Zb=r.Zb+1),o[i++]=l)}}}else for(e=0;e<r.length;e++)n(r[e],e,1)}},j=function(){function e(e,i,u,l,c,d,p,g){var b=y.extend({},a,e);1>e.lineHeight&&(e.lineHeight=1),e=b.fontFamily;var v=b.fontStyle+" "+b.fontVariant+" "+b.fontWeight,m=b.hb,C=b.Gc,_=v+" "+e;b.te=_;var w={ka:!1,bc:0,fontSize:0};if(i.save(),i.font=v+" 100px "+e,i.textBaseline="middle",i.textAlign="center",function(e,t){t=t.te;var n=s[t];void 0===n&&(n={},s[t]=n),n[" "]=e.measureText(" ").width,n["…"]=e.measureText("…").width}(i,b),u=u.trim(),h.text=u,function(e,t,n,r){for(var i,o,a=0;a<e.length;a++)e[a].y===t.y&&(void 0===i?i=a:o=a);void 0===o&&(o=i),i!==o&&e[o].x<e[i].x&&(a=i,i=o,o=a),r.C=e,r.F=t,r.cd=n,r.Xe=i,r.Ye=o}(l,c,d,f),/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/.test(u)?(n(h),t(i,h,_),r(b,h,f,C,m,!0,w)):(t(i,h,_),r(b,h,f,C,m,!1,w),!w.ka&&(p&&(n(h),t(i,h,_)),g||p)&&(g&&(w.Ub=!0),r(b,h,f,C,C,!0,w))),w.ka){var x="",A=0,S=Number.MAX_VALUE,M=Number.MIN_VALUE;o(b,h,w.bc,w.fontSize,f,w.Ub,(function(e,t){0<x.length&&" "===t&&(x+=" "),x+=e}),(function(e,t,n,r,o){"­"===r&&(x+="‐"),i.save(),i.translate(d.x,t),e=w.fontSize/100,i.scale(e,e),i.fillText(x,0,0),i.restore(),x=n,A<o&&(A=o),S>t&&(S=t),M<t&&(M=t)})),w.box={x:d.x-A/2,y:S-w.fontSize/2,w:A,o:M-S+w.fontSize},i.restore()}else i.clear&&i.clear();return w}function t(e,t,n){var r,i=t.text.split(/(\n|[ \f\r\t\v\u2028\u2029]+|\u00ad+|\u200b+)/),o=[],a=[],u=i.length>>>1;for(r=0;r<u;r++)o.push(i[2*r]),a.push(i[2*r+1]);for(2*r<i.length&&(o.push(i[2*r]),a.push(void 0)),n=s[n],r=0;r<o.length;r++)void 0===(u=n[i=o[r]])&&(u=e.measureText(i).width,n[i]=u);t.Tc=o,t.Qf=a}function n(e){for(var t=e.text.split(/\s+/),n=[],r={".":!0,",":!0,";":!0,"?":!0,"!":!0,":":!0,"。":!0},i=0;i<t.length;i++){var o=t[i];if(3<o.length){var a="";a+=o.charAt(0),a+=o.charAt(1);for(var s=2;s<o.length-2;s++){var u=o.charAt(s);r[u]||(a+="​"),a+=u}a+="​",a+=o.charAt(o.length-2),a+=o.charAt(o.length-1),n.push(a)}else n.push(o)}e.text=n.join(" ")}function r(e,t,n,r,i,a,s){var u=e.lineHeight,l=Math.max(e.Ua,.001),c=e.ib,h=t.Tc,f=n.cd,d=n.F,p=void 0,g=void 0;switch(e.verticalAlign){case"top":f=d.y+d.o-f.y;break;case"bottom":f=f.y-d.y;break;default:f=2*Math.min(f.y-d.y,d.y+d.o-f.y)}if(!(0>=(c=Math.min(f,c*n.F.o)))){f=r,i=Math.min(i,c),d=Math.min(1,c/Math.max(20,t.Tc.length));do{var b=(f+i)/2,v=Math.min(h.length,Math.floor((c+b*(u-1-2*l))/(b*u))),m=void 0;if(0<v)for(var y=1,C=v;;){var _=Math.floor((y+C)/2);if(o(e,t,_,b,n,a&&b===r&&_===v,null,null)){if(y===(C=p=m=_))break}else if((y=_+1)>C)break}void 0!==m?f=g=b:i=b}while(i-f>d);return void 0===g?(s.ka=!1,s.fontSize=0):(s.ka=!0,s.fontSize=g,s.bc=p,s.Ub=a&&b===f),s}s.ka=!1}function o(e,t,n,r,i,o,a,h){var f=e.cb,d=r*(e.lineHeight-1),p=Math.max(e.Ua,.001),g=s[e.te],b=t.Tc;t=t.Qf;var v=i.C,m=i.cd,y=i.Xe,C=i.Ye;switch(e.verticalAlign){case"top":i=m.y+r/2+r*p;var _=1;break;case"bottom":i=m.y-(r*n+d*(n-1))+r/2-r*p,_=-1;break;default:i=m.y-(r*(n-1)/2+d*(n-1)/2),_=1}for(e=i,p=0;p<n;p++)u[2*p]=i-r/2,u[2*p+1]=i+r/2,i+=_*r,i+=_*d;for(;l.length<u.length;)l.push(Array(2));p=u,i=2*n,_=l;var w=v.length,x=y;y=(y-1+w)%w;var A=C;C=(C+1)%w;for(var S=0;S<i;){for(var M=p[S],T=v[y];T.y<M;)x=y,T=v[y=(y-1+w)%w];for(var k=v[C];k.y<M;)A=C,k=v[C=(C+1)%w];var z=v[x],D=v[A];k=D.x+(k.x-D.x)*(M-D.y)/(k.y-D.y),_[S][0]=z.x+(T.x-z.x)*(M-z.y)/(T.y-z.y),_[S][1]=k,S++}for(p=0;p<n;p++)v=2*p,_=(_=(i=m.x)-l[v][0])<(w=l[v][1]-i)?_:w,v=(w=i-l[v+1][0])<(v=l[v+1][1]-i)?w:v,c[p]=2*(_<v?_:v)-f*r;for(x=g[" "]*r/100,_=g["…"]*r/100,y=c[f=0],m=0,v=void 0,p=0;p<b.length;p++){if(i=b[p],A=t[p],m+(w=g[i]*r/100)<y&&b.length-p>=n-f&&"\n"!=v)m+=w," "===A&&(m+=x),a&&a(i,v);else{if(w>y&&(f!==n-1||!o))return!1;if(f+1>=n)return!!o&&(((n=y-m-_)>_||w>_)&&(0<(n=Math.floor(i.length*n/w))&&a&&a(i.substring(0,n),v)),a&&a("…",void 0),h&&h(f,e,i,v,m),!0);if(f++,h&&h(f,e,i,v,m),e+=r,e+=d,m=w," "===A&&(m+=x),w>(y=c[f])&&(f!==n||!o))return!1}v=A}return h&&h(f,e,void 0,void 0,m),!0}var a={hb:72,Gc:0,lineHeight:1.05,cb:1,Ua:.5,ib:.9,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",verticalAlign:"center"},s={},u=[],l=[],c=[],h={text:"",Tc:void 0,Qf:void 0},f={C:void 0,F:void 0,cd:void 0,Xe:0,Ye:0};return{re:e,de:function(t,n,r,i,o,a,s,u,l,c,h,f){var d=0,p=0;if(r=r.toString().trim(),!f&&l.result&&r===l.Xf&&Math.abs(c-l.Zd)/c<=h){var g=l.result;g.ka&&(d=a.x-l.eg,p=a.y-l.fg,h=l.Qc,n.save(),n.translate(d,p),h.Na(n),n.restore())}return g||((h=l.Qc).clear(),(g=e(t,h,r,i,o,a,s,u)).ka&&h.Na(n),l.Zd=c,l.eg=a.x,l.fg=a.y,l.result=g,l.Xf=r),g.ka?{ka:!0,bc:g.bc,fontSize:g.fontSize,box:{x:g.box.x+d,y:g.box.y+p,w:g.box.w,o:g.box.o},Ub:g.Ub}:{ka:!1}},Zh:function(){return{Zd:0,eg:0,fg:0,result:void 0,Qc:new i,Xf:void 0}},ya:a}}(),L=new function(){function e(e,t){return function(r,i,o,a){function s(e,t,n,r,i){e.C=[{x:t,y:n},{x:t+r,y:n},{x:t+r,y:n+i},{x:t,y:n+i}]}var u=i.x,l=i.y,c=i.w;if(i=i.o,0!=r.length)if(1==r.length)r[0].x=u+c/2,r[0].y=l+i/2,r[0].nd=0,o&&s(r[0],u,l,c,i);else{r=r.slice(0);for(var h=0,f=0;f<r.length;f++)h+=r[f].weight;for(h=c*i/h,f=0;f<r.length;f++)r[f].lc=r[f].weight*h;(function e(r,i,a,u,l){if(0!=r.length){var c=r.shift(),h=n(c);if(t(u,l)){var f=i,d=h/u;do{var p=(h=c.shift()).lc,g=p/d,b=a,v=d;(p=h).x=f+g/2,p.y=b+v/2,o&&s(h,f,a,g,d),f+=g}while(0<c.length);return e(r,i,a+d,u,l-d)}f=a,g=h/l;do{b=f,v=d=(p=(h=c.shift()).lc)/g,(p=h).x=i+g/2,p.y=b+v/2,o&&s(h,i,f,g,d),f+=d}while(0<c.length);return e(r,i+g,a,u-g,l)}})(a=e(r,c,i,[[r.shift()]],a),u,l,c,i)}}}function t(e,t,r,i){function o(e){return Math.max(Math.pow(u*e/s,r),Math.pow(s/(u*e),i))}var a=n(e),s=a*a,u=t*t;for(t=o(e[0].lc),a=1;a<e.length;a++)t=Math.max(t,o(e[a].lc));return t}function n(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].lc;return t}this.u=e((function(e,r,i,o,a){for(var s=1/(a=Math.pow(2,a)),u=r<i;0<e.length;){var l=o[o.length-1],c=e.shift(),h=u?r:i,f=u?a:s,d=u?s:a,p=t(l,h,f,d);l.push(c),p<(h=t(l,h,f,d))&&(l.pop(),o.push([c]),u?i-=n(l)/r:r-=n(l)/i,u=r<i)}return o}),(function(e,t){return e<t})),this.i=e((function(e,n,r,i,o){function a(e){if(1<i.length){for(var r=i[i.length-1],o=i[i.length-2].slice(0),a=0;a<r.length;a++)o.push(r[a]);t(o,n,s,u)<e&&i.splice(-2,2,o)}}for(var s=Math.pow(2,o),u=1/s;0<e.length;){if(o=t(r=i[i.length-1],n,s,u),0==e.length)return;var l=e.shift();r.push(l),o<t(r,n,s,u)&&(r.pop(),a(o),i.push([l]))}return a(t(i[i.length-1],n,s,u)),i}),(function(){return!0}))};function B(e){var t,n={},r=e.Cd;e.j.subscribe("model:loaded",(function(e){t=e})),this.M=function(){e.j.D("api:initialized",this)},this.nc=function(e,t,i,o){this.Xc(n,t),this.Yc(n,t),this.Wc(n,t,!1),o&&o(n),e(r,n,i)},this.bd=function(e,n,r,i,o,a,s){if(e){for(e=n.length-1;0<=e;e--){var u=n[e],l=y.extend({group:u.group},o);l[r]=i(u),a(l)}0<n.length&&s(y.extend({groups:S.uc(t,i).map((function(e){return e.group}))},o))}},this.Yc=function(e,t){return e.selected=t.selected,e.hovered=t.ub,e.open=t.open,e.openness=t.Cb,e.exposed=t.U,e.exposure=t.ja,e.transitionProgress=t.ra,e.revealed=!t.aa.Ga(),e.browseable=t.Ia?t.R:void 0,e.visible=t.Y,e.labelDrawn=t.oa&&t.oa.ka,e},this.Xc=function(e,t){var n=t.parent;return e.group=t.group,e.parent=n&&n.group,e.weightNormalized=t.cg,e.level=t.level-1,e.siblingCount=n&&n.m.length,e.hasChildren=!t.empty(),e.index=t.index,e.indexByWeight=t.nd,e.description=t.description,e.attribution=t.attribution,e},this.Wc=function(e,t,n){if(e.polygonCenterX=t.O.x,e.polygonCenterY=t.O.y,e.polygonArea=t.O.ha,e.boxLeft=t.F.x,e.boxTop=t.F.y,e.boxWidth=t.F.w,e.boxHeight=t.F.o,t.oa&&t.oa.ka){var r=t.oa.box;e.labelBoxLeft=r.x,e.labelBoxTop=r.y,e.labelBoxWidth=r.w,e.labelBoxHeight=r.o,e.labelFontSize=t.oa.fontSize}return n&&t.$&&(e.polygon=t.$.map((function(e){return{x:e.x,y:e.y}})),e.neighbors=t.J&&t.J.map((function(e){return e&&e.group}))),e}}var E=new function(){var e=window.console;this.i=function(e){throw"FoamTree: "+e},this.info=function(t){e.info("FoamTree: "+t)},this.warn=function(t){e.warn("FoamTree: "+t)}};function O(e){function t(t,r){t.m=[],t.Ea=!0;var o=i(r),a=0;if(("flattened"===e.mb||"always"===e.zg&&t.group&&t.group.description)&&0<r.length&&0<t.level){var s=r.reduce((function(e,t){return e+y.I(t.weight,1)}),0),u=n(t.group,!1);u.description=!0,u.weight=s*e.Sb,u.index=a++,u.parent=t,u.level=t.level+1,u.id=u.id+"_d",t.m.push(u)}for(s=0;s<r.length;s++){var l=r[s];if(0>=(u=y.I(l.weight,1))){if(!e.Wi)continue;u=.9*o}(l=n(l,!0)).weight=u,l.index=a,l.parent=t,l.level=t.level+1,t.m.push(l),a++}}function n(e,t){var n=new ee;return r(e),n.id=e.__id,n.group=e,t&&(l[e.__id]=n),n}function r(e){y.has(e,"__id")||(Object.defineProperty(e,"__id",{enumerable:!1,configurable:!1,writable:!1,value:u}),u++)}function i(e){for(var t=Number.MAX_VALUE,n=0;n<e.length;n++){var r=e[n].weight;0<r&&t>r&&(t=r)}return t===Number.MAX_VALUE&&(t=1),t}function o(e){if(!e.empty()){var t,n=0;for(t=(e=e.m).length-1;0<=t;t--){var r=e[t].weight;n<r&&(n=r)}for(t=e.length-1;0<=t;t--)(r=e[t]).cg=r.weight/n}}function a(e){if(!e.empty()){e=e.m.slice(0).sort((function(e,t){return e.weight<t.weight?1:e.weight>t.weight?-1:e.index-t.index}));for(var t=0;t<e.length;t++)e[t].nd=t}}function s(){for(var t=p.m.reduce((function(e,t){return e+t.weight}),0),n=0;n<p.m.length;n++){var r=p.m[n];r.attribution&&(r.weight=Math.max(.025,e.sg)*t)}}var u,l,c,h,f,d=this,p=new ee;this.M=function(){return p},this.T=function(n){var r=n.group.groups,i=e.Rh;return!!(!n.m&&!n.description&&r&&0<r.length&&f+r.length<=i)&&(f+=r.length,t(n,r),o(n),a(n),!0)},this.load=function(e){p.group=e,p.xa=!1,p.R=!1,p.Ia=!1,p.open=!0,p.Cb=1,u=function e(t,n){if(!t)return n;if(n=Math.max(n,t.__id||0),(t=t.groups)&&0<t.length)for(var r=t.length-1;0<=r;r--)n=e(t[r],n);return n}(e,0)+1,l={},c={},h={},f=0,e&&(r(e),l[e.__id]=p,y.V(e.id)||(c[e.id]=e),function e(t){var n=t.groups;if(n)for(var i=0;i<n.length;i++){var o=n[i];r(o);var a=o.__id;l[a]=null,h[a]=t,a=o.id,y.V(a)||(c[a]=o),e(o)}}(e)),t(p,e&&e.groups||[]),function(e){if(!e.empty()){var t=n({attribution:!0});t.index=e.m.length,t.parent=e,t.level=e.level+1,t.attribution=!0,e.m.push(t)}}(p),o(p),s(),a(p)},this.update=function(e){e.forEach((function(e){S.za(e,(function(e){if(!e.empty())for(var t=i((e=e.m).map((function(e){return e.group}))),n=0;n<e.length;n++){var r=e[n];r.weight=0<r.group.weight?r.group.weight:.9*t}})),o(e),e===p&&s(),a(e)}))},this.u=function(e){return function(){if(y.V(e)||y.Re(e))return[];if(Array.isArray(e))return e.map(d.i,d);if(y.wb(e)){if(y.has(e,"__id"))return[d.i(e)];if(y.has(e,"all")){var t=[];return S.L(p,(function(e){t.push(e)})),t}if(y.has(e,"groups"))return d.u(e.groups)}return[d.i(e)]}().filter((function(e){return void 0!==e}))},this.i=function(e){if(y.wb(e)&&y.has(e,"__id")){if(e=e.__id,y.has(l,e)){if(null===l[e]){for(var t=h[e],n=[];t&&(t=t.__id,n.push(t),!l[t]);)t=h[t];for(t=n.length-1;0<=t;t--)this.T(l[n[t]])}return l[e]}}else if(y.has(c,e))return this.i(c[e])},this.H=function(e,t,n){return{m:d.u(e),Ca:y.I(e&&e[t],!0),Ba:y.I(e&&e.keepPrevious,n)}}}function I(e,t,n){var r={};t.Ba&&S.L(e,(function(e){n(e)&&(r[e.id]=e)})),e=t.m,t=t.Ca;for(var i=e.length-1;0<=i;i--){var o=e[i];r[o.id]=t?o:void 0}var a=[];return y.Aa(r,(function(e){void 0!==e&&a.push(e)})),a}function N(e){function t(e,t){e=e.ja,t.opacity=1,t.Da=1,t.va=0>e?1-A.Ch/100*e:1,t.saturation=0>e?1-A.Dh/100*e:1,t.ca=0>e?1+.5*e:1}function n(e){return e=e.ja,Math.max(.001,0===e?1:1+e*(A.Pa-1))}function r(t,n,r,u){var l=o();if(0===t.length&&!l)return(new h).resolve().promise();var v=t.reduce((function(e,t){return e[t.id]=!0,e}),{}),y=[];if(t=[],C.reduce((function(e,t){return e||v[t.id]&&(!t.U||1!==t.ja)||!v[t.id]&&!t.parent.U&&(t.U||-1!==t.ja)}),!1)){var M=[],T={};C.forEach((function(e){v[e.id]&&(e.U||y.push(e),e.U=!0,S.za(e,(function(e){M.push(a(e,1)),T[e.id]=!0})))})),0<M.length?(S.L(c,(function(e){v[e.id]||(e.U&&y.push(e),e.U=!1),T[e.id]||M.push(a(e,-1))})),t.push(b.K.A({}).Qa(M).call(s).Ta()),i(v),t.push(function(t){return t||!g.zd()?b.K.A(d).fa({duration:.7*A.Oa,P:{x:{end:_.x+_.w/2,easing:m.ia(A.Wb)},y:{end:_.y+_.o/2,easing:m.ia(A.Wb)}},ba:function(){e.j.D("foamtree:dirty",!0)}}).Ta():(d.x=_.x+_.w/2,d.y=_.y+_.o/2,(new h).resolve().promise())}(l)),r&&(g.ic(_,A.Yb,A.Oa,m.ia(A.Wb)),g.Fb())):(t.push(function(e){var t=[],n=[];return S.L(c,(function(e){0!==e.ja&&n.push(a(e,0,(function(){this.U=!1})))})),t.push(b.K.A({}).Qa(n).Ta()),g.content(0,0,w,x),e&&(t.push(g.reset(A.Oa,m.ia(A.Wb))),g.Fb()),f(t)}(r)),n&&S.L(c,(function(e){e.U&&y.push(e)})))}return f(t).then((function(){p.bd(n,y,"exposed",(function(e){return e.U}),{indirect:u},e.options.hf,e.options.gf)}))}function i(e){C.reduce(u(!0,void 0,(function(t){return t.U||e[t.id]})),l(_)),_.x-=_.w*(A.Pa-1)/2,_.y-=_.o*(A.Pa-1)/2,_.w*=A.Pa,_.o*=A.Pa}function o(){return!!C&&C.reduce((function(e,t){return e||0!==t.ja}),!1)}function a(n,r,i){var o=b.K.A(n);return 0===n.ja&&0!==r&&o.call((function(){this.mc(M),this.qb(t)})),o.fa({duration:A.Oa,P:{ja:{end:r,easing:m.ia(A.Wb)}},ba:function(){c.N=!0,c.Fa=!0,e.j.D("foamtree:dirty",!0)}}),0===r&&o.call((function(){this.vd(),this.cc(),this.Nc(M),this.Mc(t)})),o.call(i).done()}function s(){var e=c.m.reduce(u(!1,M.transformPoint,void 0),l({})).box,t=A.Yb,n=Math.min(e.x,_.x-_.w*t),r=Math.min(e.y,_.y-_.o*t);g.content(n,r,Math.max(e.x+e.w,_.x+_.w*(1+t))-n,Math.max(e.y+e.o,_.y+_.o*(1+t))-r)}function u(e,t,n){var r={};return function(i,o){if(!n||n(o)){for(var a,s=e&&o.$||o.C,u=s.length-1;0<=u;u--)a=void 0!==t?t(o,s[u],r):s[u],i.Hc=Math.min(i.Hc,a.x),i.wd=Math.max(i.wd,a.x),i.Ic=Math.min(i.Ic,a.y),i.xd=Math.max(i.xd,a.y);i.box.x=i.Hc,i.box.y=i.Ic,i.box.w=i.wd-i.Hc,i.box.o=i.xd-i.Ic}return i}}function l(e){return{Hc:Number.MAX_VALUE,wd:Number.MIN_VALUE,Ic:Number.MAX_VALUE,xd:Number.MIN_VALUE,box:e}}var c,d,p,g,b,v,C,_,w,x,A=e.options,M={Ve:function(e,t){return t.scale=n(e),!1},Ib:function(e,t){e=n(e);var r=d.x,i=d.y;t.translate(r,i),t.scale(e,e),t.translate(-r,-i)},Jb:function(e,t,r){e=n(e);var i=d.x,o=d.y;r.x=(t.x-i)/e+i,r.y=(t.y-o)/e+o},transformPoint:function(e,t,r){e=n(e);var i=d.x,o=d.y;return r.x=(t.x-i)*e+i,r.y=(t.y-o)*e+o,r}};e.j.subscribe("stage:initialized",(function(e,t,n,r){d={x:n/2,y:r/2},_={x:0,y:0,w:w=n,o:x=r}})),e.j.subscribe("stage:resized",(function(e,t,n,r){d.x*=n/e,d.y*=r/t,w=n,x=r})),e.j.subscribe("api:initialized",(function(e){p=e})),e.j.subscribe("zoom:initialized",(function(e){g=e})),e.j.subscribe("model:loaded",(function(e,t){c=e,C=t})),e.j.subscribe("model:childrenAttached",(function(e){C=e})),e.j.subscribe("timeline:initialized",(function(e){b=e})),e.j.subscribe("openclose:initialized",(function(e){v=e}));var T=["groupExposureScale","groupUnexposureScale","groupExposureZoomMargin"];e.j.subscribe("options:changed",(function(e){y.bb(e,T)&&o()&&(i({}),g.cj(_,A.Yb),g.Fb())})),this.M=function(){e.j.D("expose:initialized",this)},this.Vb=function(e,t,n,i){var o=e.m.reduce((function(e,t){for(;t=t.parent;)e[t.id]=!0;return e}),{}),a=I(c,e,(function(e){return e.U&&!e.open&&!o[e.id]})),s=new h;return function(e,t){for(var n=e.reduce((function(e,t){return e[t.id]=t,e}),{}),r=e.length-1;0<=r;r--)S.L(e[r],(function(e){n[e.id]=void 0}));var i=[];y.Aa(n,(function(e){e&&S.me(e,(function(e){e.open||i.push(e)}))}));var o=[];return y.Aa(n,(function(e){e&&e.open&&o.push(e)})),e=[],0!==i.length&&e.push(v.Bb({m:i,Ca:!0,Ba:!0},t,!0)),f(e)}(a,t).then((function(){r(a.filter((function(e){return e.C&&e.$})),t,n,i).then(s.resolve)})),s.promise()}}function P(e){var t,n,r=[],i=new d(y.qa);e.j.subscribe("stage:initialized",(function(){})),e.j.subscribe("stage:resized",(function(){})),e.j.subscribe("stage:newLayer",(function(e,t){r.push(t)})),e.j.subscribe("model:loaded",(function(e){t=e,i.clear()})),e.j.subscribe("zoom:initialized",(function(){})),e.j.subscribe("timeline:initialized",(function(e){n=e}));var o=!1;e.j.subscribe("render:renderers:resolved",(function(e){o=e.labelPlainFill||!1}));var a=new function(){var e=0,t=0,n=0,r=0,i=0,o=0;this.i=function(a,s,u,l,c){t=1-(e=1+s),n=u,r=l,i=c,o=a},this.Ve=function(o,a){return a.scale=e+t*o.ra,0!==i||0!==n||0!==r},this.Ib=function(a,s){var u=e+t*a.ra,l=a.parent,c=o*a.x+(1-o)*l.x,h=o*a.y+(1-o)*l.y;s.translate(c,h),s.scale(u,u),a=1-a.ra,s.rotate(i*Math.PI*a),s.translate(-c,-h),s.translate(l.F.w*n*a,l.F.o*r*a)},this.Jb=function(i,a,s){var u=e+t*i.ra,l=o*i.x+(1-o)*i.parent.x,c=o*i.y+(1-o)*i.parent.y,h=1-i.ra;i=i.parent,s.x=(a.x-l)/u+l-i.F.w*n*h,s.y=(a.y-c)/u+c-i.F.o*r*h},this.transformPoint=function(i,a,s){var u=e+t*i.ra,l=o*i.x+(1-o)*i.parent.x,c=o*i.y+(1-o)*i.parent.y,h=1-i.ra;i=i.parent,s.x=(a.x-l)*u+l-i.F.w*n*h,s.y=(a.y-c)*u+c-i.F.o*r*h}};this.M=function(){},this.u=function(){function r(e,t){var n=Math.min(1,Math.max(0,e.ra));t.opacity=n,t.va=1,t.saturation=n,t.Da=n,t.ca=e.yb}function s(e,t){var n=Math.min(1,Math.max(0,e.Hd));t.opacity=n,t.Da=n,t.va=1,t.saturation=1,t.ca=e.yb}var u=e.options,l=u.Gd,c=u.ii,h=u.ji,f=u.ki,d=u.ei,p=u.fi,g=u.gi,b=u.ai,v=u.bi,y=u.ci,C=d+p+g+b+v+y+c+h+f,_=0<C?l/C:0,w=[];return i.initial()?a.i(u.oi,u.mi,u.pi,u.ri,u.li):a.i(u.Mf,u.Lf,u.Nf,u.Of,u.Kf),D.u(t,D.i(t,e.options.ni),(function(t,i,l){var C="groups"===e.options.hi?l:i;w.push(n.K.A(t).call((function(){this.qb(r)})).wait(o?_*(d+C*p):0).fa({duration:o?_*g:0,P:{yb:{end:0,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).done()),S.L(t,(function(t){w.push(n.K.A(t).call((function(){this.mc(a),this.qb(s)})).wait(_*(b+v*C)).fa({duration:_*y,P:{Hd:{end:0,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).Xa((function(){this.selected=!1,this.Nc(a)})).done())})),w.push(n.K.A(t).call((function(){this.mc(a)})).wait(_*(c+h*C)).fa({duration:_*f,P:{ra:{end:0,easing:m.ia(u.di)}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).Xa((function(){this.selected=!1,this.Nc(a)})).done())})),n.K.A({}).Qa(w).Ta()},this.i=function(t){return function(t){function r(e,t){var n=Math.min(1,Math.max(0,e.ra));t.opacity=n,t.va=1,t.saturation=n,t.Da=n,t.ca=e.yb}var s=e.options,u=s.Ri,l=s.Si,c=s.Oi,f=s.Pi,d=s.Qi,p=s.Od,g=u+l+c+f+d,b=0<g?p/g:0,v=[];if(a.i(s.Mf,s.Lf,s.Nf,s.Of,s.Kf),0===b&&t.m&&t.R){for(p=t.m,g=0;g<p.length;g++){var y=p[g];y.ra=1,y.yb=1,y.qb(r),y.cc(),y.Mc(r)}return t.N=!0,e.j.D("foamtree:dirty",0<b),(new h).resolve().promise()}if(t.m&&t.R){D.u(t,D.i(t,e.options.Qd),(function(t,i,h){t.mc(a),t.qb(r),h="groups"===e.options.Pd?h:i,i=n.K.A(t).wait(h*b*u).fa({duration:b*l,P:{ra:{end:1,easing:m.ia(s.Ni)}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",0<b)}}).done(),h=n.K.A(t).wait(o?b*(c+h*f):0).fa({duration:o?b*d:0,P:{yb:{end:1,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",0<b)}}).done(),t=n.K.A(t).Qa([i,h]).Xd().Xa((function(){this.vd(),this.cc(),this.Nc(a),this.Mc(r)})).done(),v.push(t)})),i.i();var C=new h;return n.K.A({}).Qa(v).call((function(){i.u(),C.resolve()})).start(),C.promise()}return(new h).resolve().promise()}(t)}}function F(e){function t(t,i,s){function u(e,t){t.opacity=1-e.Cb,t.va=1,t.saturation=1,t.ca=1,t.Da=1}var l=[],c=[];return S.L(o,(function(i){if(i.R&&i.X){var o=y.has(t,i.id),a=n[i.id];if(a&&a.xb())a.stop();else if(i.open===o)return;i.Va=o,o||(i.open=o,i.Bd=!1),c.push(i),l.push(function(t,i){t.qb(u);var o=r.K.A(t).fa({duration:e.options.Kc,P:{Cb:{end:i?1:0,easing:m.fe}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).call((function(){this.open=i,t.Va=!1})).Xa((function(){this.cc(),this.Mc(u),delete n[this.id]})).done();return n[t.id]=o}(i,o))}})),0<l.length?(e.j.D("openclose:changing"),r.K.A({}).Qa(l).Ta().then((function(){a.bd(i,c,"open",(function(e){return e.open}),{indirect:s},e.options.rf,e.options.qf)}))):(new h).resolve().promise()}var n,r,i,o,a;e.j.subscribe("api:initialized",(function(e){a=e})),e.j.subscribe("model:loaded",(function(e){o=e,n={}})),e.j.subscribe("timeline:initialized",(function(e){r=e})),e.j.subscribe("expose:initialized",(function(e){i=e})),this.M=function(){e.j.D("openclose:initialized",this)},this.Bb=function(n,r,a){if("flattened"==e.options.mb)return(new h).resolve().promise();n=I(o,n,(function(e){return e.open||e.Va}));for(var s=new h,u=0;u<n.length;u++)n[u].Va=!0;0<n.length&&e.j.D("foamtree:attachChildren",n);var l=n.reduce((function(e,t){return e[t.id]=!0,e}),{});return function(e,t){var n,r=[];if(S.L(o,(function(t){if(t.m){var n=y.has(e,t.id);t.open!==n&&(n||t.U||S.L(t,(function(e){if(e.U)return r.push(t),!1})))}})),0===r.length)return(new h).resolve().promise();for(n=r.length-1;0<=n;n--)r[n].open=!1;for(t=i.Vb({m:r,Ca:!0,Ba:!0},t,!0,!0),n=r.length-1;0<=n;n--)r[n].open=!0;return t}(l,r).then((function(){t(l,r,a).then(s.resolve)})),s.promise()}}function R(e){var t,n;e.j.subscribe("api:initialized",(function(e){n=e})),e.j.subscribe("model:loaded",(function(e){t=e})),this.M=function(){e.j.D("select:initialized",this)},this.select=function(r,i){return function(r,i){var o;for(r=I(t,r,(function(e){return e.selected})),S.L(t,(function(e){!0===e.selected&&(e.selected=!e.selected,e.N=!e.N,e.Sa=!e.Sa)})),o=r.length-1;0<=o;o--){var a=r[o];a.selected=!a.selected,a.N=!a.N,a.Sa=!a.Sa}var s=[];S.L(t,(function(e){e.N&&s.push(e)})),0<s.length&&e.j.D("foamtree:dirty",!1),n.bd(i,s,"selected",(function(e){return e.selected}),{},e.options.tf,e.options.sf)}(r,i)}}function G(e){function n(e){return function(t){e.call(this,{x:t.x,y:t.y,scale:t.scale,ed:t.delta,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,shiftKey:t.shiftKey,lb:t.secondary,touches:t.touches})}}function r(){u.pc(2)?e.j.D("interaction:reset"):u.normalize(N.ob,m.ia(N.Kb))}function i(e){return function(){p.empty()||e.apply(this,arguments)}}function o(e,t,n){var r={},i={};return function(o){switch(e){case"click":var s=N.bf;break;case"doubleclick":s=N.cf;break;case"hold":s=N.jf;break;case"hover":s=N.kf;break;case"mousemove":s=N.mf;break;case"mousewheel":s=N.pf;break;case"mousedown":s=N.lf;break;case"mouseup":s=N.nf;break;case"dragstart":s=N.ff;break;case"drag":s=N.df;break;case"dragend":s=N.ef;break;case"transformstart":s=N.wf;break;case"transform":s=N.uf;break;case"transformend":s=N.vf}var l=!1,c=!s.empty(),h=u.absolute(o,r),f=(t||c)&&a(h),d=(t||c)&&function(e){var t=void 0,n=0;return S.sc(p,(function(r){!0===r.open&&r.Y&&r.scale>n&&U(r,e)&&(t=r,n=r.scale)})),t}(h);c&&(c=f?f.group:null,h=f?f.Jb(h,i):h,o.Db=void 0,s=s({type:e,group:c,topmostClosedGroup:c,bottommostOpenGroup:d?d.group:null,x:o.x,y:o.y,xAbsolute:h.x,yAbsolute:h.y,scale:y.I(o.scale,1),secondary:o.lb,touches:y.I(o.touches,1),delta:y.I(o.ed,0),ctrlKey:o.ctrlKey,metaKey:o.metaKey,altKey:o.altKey,shiftKey:o.shiftKey,preventDefault:function(){l=!0},preventOriginalEventDefault:function(){o.Db="prevent"},allowOriginalEventDefault:function(){o.Db="allow"}}),l=l||0<=s.indexOf(!1),f&&f.attribution&&"click"===e&&(l=!1)),l||n&&n({dd:f,ug:d},o)}}function a(e,t){if("flattened"===N.mb)e=function(e){function t(e,n){var r=n.m;if(r){for(var i,o=-Number.MAX_VALUE,a=0;a<r.length;a++){var s=r[a];!s.description&&s.Y&&U(s,e)&&s.scale>o&&(i=s,o=s.scale)}var u;return i&&(u=t(e,i)),u||i||n}return n}for(var n=E.length,r=E[0].scale,i=E[0].scale,o=0;o<n;o++){var a=E[o];(a=a.scale)<r&&(r=a),a>i&&(i=a)}if(r!==i)for(o=0;o<n;o++)if((a=E[o]).scale===i&&a.Y&&U(a,e))return t(e,a);return t(e,p)}(e);else{t=t||0;for(var n=E.length,r=void 0,i=0;i<n;i++){var o=E[i];o.scale>t&&!1===o.open&&o.Y&&U(o,e)&&(r=o,t=o.scale)}e=r}return e&&e.description&&(e=e.parent),e}var s,u,l,c,h,f,d,p,C,_,w,A,T,k,z,D,j,L,B,E,O=t.Lh(),I=this,N=e.options,P=!1;e.j.subscribe("stage:initialized",(function(t,n,r,i){s=n,L=r,B=i,function(){function t(e){return function(t){return t.x*=L/s.clientWidth,t.y*=B/s.clientHeight,e(t)}}"external"!==N.Me&&("hammerjs"===N.Me&&y.has(window,"Hammer")&&(G.M(s),G.A("tap",t(I.i),!0),G.A("doubletap",t(I.u),!0),G.A("hold",t(I.sa),!0),G.A("touch",t(I.ua),!0),G.A("release",t(I.wa),!1),G.A("dragstart",t(I.ga),!0),G.A("drag",t(I.H),!0),G.A("dragend",t(I.T),!0),G.A("transformstart",t(I.pb),!0),G.A("transform",t(I.transform),!0),G.A("transformend",t(I.Ka),!0)),D=new v(s),j=new b,D.i(t(I.i)),D.u(t(I.u)),D.sa(t(I.sa)),D.wa(t(I.ua)),D.Ka(t(I.wa)),D.ga(t(I.ga)),D.H(t(I.H)),D.T(t(I.T)),D.ta(t(I.ta)),D.Ja(t(I.ta)),D.ua(t(I.Ja)),j.addEventListener("keyup",(function(t){var n=!1,r=void 0,i=N.xf({keyCode:t.keyCode,preventDefault:function(){n=!0},preventOriginalEventDefault:function(){r="prevent"},allowOriginalEventDefault:function(){r="allow"}});"prevent"===r&&t.preventDefault(),(n=n||0<=i.indexOf(!1))||27===t.keyCode&&e.j.D("interaction:reset")})))}()})),e.j.subscribe("stage:resized",(function(e,t,n,r){L=n,B=r})),e.j.subscribe("stage:disposed",(function(){D.Za(),G.Za(),j.i()})),e.j.subscribe("expose:initialized",(function(e){c=e})),e.j.subscribe("zoom:initialized",(function(e){u=e})),e.j.subscribe("openclose:initialized",(function(e){h=e})),e.j.subscribe("select:initialized",(function(e){f=e})),e.j.subscribe("titlebar:initialized",(function(e){d=e})),e.j.subscribe("timeline:initialized",(function(e){l=e})),e.j.subscribe("model:loaded",(function(e,t){p=e,E=t})),e.j.subscribe("model:childrenAttached",(function(e){E=e})),this.M=function(){},this.ua=i(o("mousedown",!1,(function(){u.Wh()}))),this.wa=i(o("mouseup",!1,void 0)),this.i=i(o("click",!0,(function(e,t){t.lb||t.shiftKey||!(e=e.dd)||(e.attribution?t.ctrlKey?document.location.href=x("iuuqr;..b`ssnurd`sbi/bnl.gn`lusdd"):(t=m.ia(N.Kb),e.be?(u.reset(N.ob,t),e.be=!1):(u.bg(e,N.Yb,N.ob,t),e.be=!0)):f.select({m:[e],Ca:!e.selected,Ba:t.metaKey||t.ctrlKey},!0))}))),this.u=i(o("doubleclick",!0,(function(t,n){var r=t.dd;r&&r.attribution||(n.lb||n.shiftKey?r&&(r.parent.U&&(r=r.parent),t={m:r.parent!==p?[r.parent]:[],Ca:!0,Ba:!1},f.select(t,!0),c.Vb(t,!0,!0,!1)):r&&(t={m:[r],Ca:!0,Ba:!1},r.Va=!0,e.j.D("foamtree:attachChildren",[r]),c.Vb(t,!0,!0,!1)),r&&l.K.A({}).wait(N.Oa/2).call((function(){h.Bb({m:S.uc(p,(function(e){return e.Bd&&!S.Jh(r,e)})),Ca:!1,Ba:!0},!0,!0),r.Bd=!0,h.Bb({m:[r],Ca:!(n.lb||n.shiftKey),Ba:!0},!0,!0)})).start())}))),this.sa=i(o("hold",!0,(function(e,t){(e=(t=!(t.metaKey||t.ctrlKey||t.shiftKey||t.lb))?e.dd:e.ug)&&e!==p&&h.Bb({m:[e],Ca:t,Ba:!0},!0,!1)}))),this.ga=i(o("dragstart",!1,(function(e,t){C=t.x,_=t.y,w=Date.now(),P=!0}))),this.H=i(o("drag",!1,(function(e,t){if(P){e=Date.now(),k=Math.min(1,e-w),w=e,e=t.x-C;var n=t.y-_;u.Uh(e,n),A=e,T=n,C=t.x,_=t.y}}))),this.T=i(o("dragend",!1,(function(){if(P){P=!1;var e=Math.sqrt(A*A+T*T)/k;4<=e?u.Vh(e,A,T):u.$e()}}))),this.pb=i(o("transformstart",!1,(function(e,t){z=1,C=t.x,_=t.y})));var F=1,R=!1;this.transform=i(o("transform",!1,(function(e,t){e=t.scale-.01,u.pg(t,e/z,t.x-C,t.y-_),z=e,C=t.x,_=t.y,F=z,R=R||2<t.touches}))),this.Ka=i(o("transformend",!1,(function(){R&&.8>F?e.j.D("interaction:reset"):r(),R=!1}))),this.Ja=i(o("mousewheel",!1,function(){var e=y.yg((function(){r()}),300);return function(t,n){1!==(t=N.ij)&&(t=Math.pow(t,n.ed),O?(u.qg(n,t),e()):u.Nb(n,t,N.ob,m.ia(N.Kb)).then(r))}}())),this.ta=i(function(){var t,n=void 0,r={},i=!1,s=o("hover",!1,(function(){n&&(n.ub=!1,0<n.level&&(n.N=!0)),t&&(t.ub=!0,0<t.level&&(t.N=!0)),d.update(t),e.j.D("foamtree:dirty",!1)})),l=o("mousemove",!1,void 0);return function(e){if("out"===e.type)i=(t=void 0)!==n;else if(u.absolute(e,r),n&&!n.open&&U(n,r)){var o=a(r,n.scale);o&&o!==n?(i=!0,t=o):i=!1}else t=a(r),i=t!==n;i&&(s(e),n=t,i=!1),n&&l(e)}}()),this.Lb={click:n(this.i),doubleclick:n(this.u),hold:n(this.sa),mouseup:n(this.wa),mousedown:n(this.ua),dragstart:n(this.ga),drag:n(this.H),dragend:n(this.T),transformstart:n(this.pb),transform:n(this.transform),transformend:n(this.Ka),hover:n(this.ta),mousewheel:n(this.Ja)};var G=function(){var e,t={};return{M:function(t){e=window.Hammer(t,{doubletap_interval:350,hold_timeout:400,doubletap_distance:10})},A:function(n,r,i){t[n]=r,e.on(n,function(e,t){return function(n){var r=(n=n.gesture).center;(r=g.oe(s,r.pageX,r.pageY,{})).scale=n.scale,r.lb=1<n.touches.length,r.touches=n.touches.length,e.call(s,r),(void 0===r.Db&&t||"prevent"===r.Db)&&n.preventDefault()}}(r,i))},Za:function(){e&&y.Aa(t,(function(t,n){e.off(n,t)}))}}}(),U=function(){var e={};return function(t,n){return t.Jb(n,e),t.$&&M.sa(t.$,e)}}()}function U(e){function t(e,t,n,r){var i,o=0,a=[];for(i=0;i<t.length;i++){var s=Math.sqrt(M.i(t[i],t[(i+1)%t.length]));a.push(s),o+=s}for(i=0;i<a.length;i++)a[i]/=o;e[0].x=n.x,e[0].y=n.y;var u=s=o=0;for(i=1;i<e.length;i++){var l=e[i],c=.95*Math.pow(i/e.length,r);for(o+=.3819;s<o;)s+=a[u],u=(u+1)%a.length;var h=(u-1+a.length)%a.length,f=1-(s-o)/a[h],d=t[h].x;h=t[h].y;var p=t[u].x,g=t[u].y;d=(d-n.x)*c+n.x,h=(h-n.y)*c+n.y,p=(p-n.x)*c+n.x,g=(g-n.y)*c+n.y,l.x=d*(1-f)+p*f,l.y=h*(1-f)+g*f}}var n={random:{vb:function(e,t){for(var n=0;n<e.length;n++){var r=e[n];r.x=t.x+Math.random()*t.w,r.y=t.y+Math.random()*t.o}},Ob:"box"},ordered:{vb:function(e,t){e=e.slice(0),r.ac&&e.sort(te),L.i(e,t,!1,r.Ld)},Ob:"box"},squarified:{vb:function(e,t){e=e.slice(0),r.ac&&e.sort(te),L.u(e,t,!1,r.Ld)},Ob:"box"},fisheye:{vb:function(e,n,i){e=e.slice(0),r.ac&&e.sort(te),t(e,n,i,.25)},Ob:"polygon"},blackhole:{vb:function(e,n,i){e=e.slice(0),r.ac&&e.sort(te).reverse(),t(e,n,i,1)},Ob:"polygon"}};n.order=n.ordered,n.treemap=n.squarified;var r=e.options;this.i=function(e,t,i){if(0<e.length){if("box"===(i=n[i.relaxationInitializer||i.initializer||r.Ii||"random"]).Ob){var o=M.F(t,{});i.vb(e,o),M.wa(e,M.H(o),t)}else i.vb(e,t,M.u(t,{}));for(i=e.length-1;0<=i;i--){if((o=e[i]).description){var a=M.ta(t,r.qc,r.Ag);o.x=a.x,o.y=a.y}o.attribution&&(a=M.ta(t,r.$d,r.rg),o.x=a.x,o.y=a.y),y.wb(o.group.initialPosition)&&(a=o.group.initialPosition,a=M.ta(t,a.position||"bottomright",a.distanceFromCenter||1),o.x=a.x,o.y=a.y)}}}}function H(e){var t,n=e.options,r=new V(e,this),i=new q(e,this),o={relaxed:r,ordered:i,squarified:i},a=o[e.options.Dc]||r;this.jg=5e-5,e.j.subscribe("model:loaded",(function(e){t=e})),e.j.subscribe("options:changed",(function(e){e.layout&&y.has(o,n.Dc)&&(a=o[n.Dc])})),this.step=function(e,t,n,r){return a.step(e,t,n,r)},this.complete=function(e){a.complete(e)},this.Oe=function(e){return e===t||2*Math.sqrt(e.O.ha/(Math.PI*e.m.length))>=Math.max(n.Be,5e-5)},this.gd=function(e,t){var r=Math.pow(n.La,e.level),i=n.$a*r;r*=n.jd;for(var o=(e=e.m).length-1;0<=o;o--){var s=e[o];a.ce(s,r);var u=s;u.$=0<i?k.i(u.C,i):u.C,u.$&&(M.F(u.$,u.F),M.Ja(u.$,u.O)),s.m&&t.push(s)}},this.fc=function(e){a.fc(e)},this.Eb=function(e){a.Eb(e)}}function V(e,t){function n(e){if(e.m){e=e.m;for(var t=0;t<e.length;t++){var n=e[t];n.kc=n.hc*c.oh}}}function r(e,r){t.Oe(e)&&(e.G||(e.G=k.i(e.C,c.jd*Math.pow(c.La,e.level-1)),e.G&&e.m[0]&&e.m[0].description&&"stab"==c.Tb&&s(e)),e.G&&(l.Eb(e),h.i(i(e),e.G,e.group),e.R=!0,r(e)),n(e))}function i(e){return"stab"===c.Tb&&0<e.m.length&&e.m[0].description?e.m.slice(1):e.m}function o(e){var t=i(e);return z.i(t,e.G),z.u(t,e.G),D.H(e)*Math.sqrt(u.O.ha/e.O.ha)}function a(e){return e<c.Hf||1e-4>e}function s(e){var t=c.Sb/(1+c.Sb),n=M.F(e.G,{}),r={x:n.x,y:0},i=n.y,o=n.o,a=c.ie*Math.pow(c.La,e.level-1),s=o*c.he,u=c.qc;"bottom"==u||0<=u&&180>u?(u=Math.PI,i+=o,o=-1):(u=0,o=1);var l=e.G,h=u,f=0,d=1,p=M.u(l,{}),g=p.ha;t*=g;for(var b=0;f<d&&20>b++;){var v=(f+d)/2;r.y=n.y+n.o*v;var m=M.ua(l,r,h);M.u(m[0],p);var y=p.ha-t;if(.01>=Math.abs(y)/g)break;0<(0==h?1:-1)*y?d=v:f=v}M.F(m[0],n),(n.o<a||n.o>s)&&(r.y=n.o<a?i+o*Math.min(a,s):i+o*s,m=M.ua(e.G,r,u)),e.m[0].C=m[0],e.G=m[1]}var u,l=this,c=e.options,h=new U(e),f=0;e.j.subscribe("model:loaded",(function(e){u=e,f=0})),this.step=function(e,n,s,l){function h(n){if(n.R&&n.xa?function(e){e!==u&&2*Math.sqrt(e.O.ha/(Math.PI*e.m.length))<Math.max(.85*c.Be,t.jg)&&(e.R=!1,e.xa=!1,e.Ia=!0,e.G=null)}(n):n.Ia&&n.C&&r(n,(function(){var t=i(n);z.i(t,n.G),z.u(t,n.G),e(n)})),!n.G||!n.R)return 0;if(n.parent&&n.parent.Z||n.Ea){var h=o(n);l&&l(n),n.Ea=!a(h)&&!s,n.Z=!0}else h=0;return t.gd(n,p),h}for(var d=0,p=[u];0<p.length;)d=Math.max(d,h(p.shift()));var g=a(d);return n&&function(e,t,n){f<e&&(f=e);var r=c.Hf;c.Ad(t?1:1-(e-r)/(f-r||1),t,n),t&&(f=0)}(d,g,s),g},this.complete=function(e){for(var n=[u];0<n.length;){var i=n.shift();if(!i.R&&i.Ia&&i.C&&r(i,e),i.G){if(i.parent&&i.parent.Z||i.Ea){for(var s=1e-4>i.O.ha,l=0;!(a(o(i))||s&&32<l++););i.Z=!0,i.Ea=!1}t.gd(i,n)}}},this.fc=function(e){S.L(e,n)},this.ce=function(e,t){if(e.R){var n=e.G;n&&(e.Fd=n),e.G=k.i(e.C,t),e.G&&e.m[0]&&e.m[0].description&&"stab"==c.Tb&&s(e),n&&!e.G&&(e.Z=!0),e.G&&e.Fd&&M.wa(i(e),e.Fd,e.G)}},this.Eb=function(e){for(var t,n=i(e),r=e.ha,o=t=0;o<n.length;o++)t+=n[o].weight;for(e.Dj=t,e=0;e<n.length;e++)(o=n[e]).Vf=o.w,o.hc=r/Math.PI*(0<t?o.weight/t:1/n.length)}}function q(e,t){function n(e,n){if(t.Oe(e)){if(!e.G||e.parent&&e.parent.Z){var r=a.jd*Math.pow(a.La,e.level-1);e.G=M.H(function(e,t){var n=2*t;return e.x+=t,e.y+=t,e.w-=n,e.o-=n,e}(M.F(e.C,{}),r))}e.G&&(e.R=!0,n(e))}else e.R=!1,S.za(e,(function(e){e.G=null}))}function r(e){if("stab"==a.Tb&&0<e.m.length&&e.m[0].description){var t=e.m.slice(1);!function(e){function t(){r.C=M.H(i),r.x=i.x+i.w/2,r.y=i.y+i.o/2}var n=a.Sb/(1+a.Sb),r=e.m[0],i=M.F(e.G,{}),o=i.o;n=Math.min(Math.max(o*n,a.ie*Math.pow(a.La,e.level-1)),o*a.he);var s=a.qc;"bottom"==s||0<=s&&180>s?(i.o=o-n,e.G=M.H(i),i.y+=o-n,i.o=n,t()):(i.o=n,t(),i.y+=n,i.o=o-n,e.G=M.H(i))}(e)}else t=e.m;a.ac&&t.sort(te),"floating"==a.Tb&&i(t,a.qc,(function(e){return e.description})),i(t,a.$d,(function(e){return e.attribution}));var n=M.F(e.G,{});(s[a.Dc]||L.i)(t,n,!0,a.Ld),e.Ea=!1,e.Z=!0,e.N=!0,e.Fa=!0}function i(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];if(n(i)){e.splice(r,1),"topleft"==t||135<=t&&315>t?e.unshift(i):e.push(i);break}}}var o,a=e.options,s={squarified:L.u,ordered:L.i};e.j.subscribe("model:loaded",(function(e){o=e})),this.step=function(e,t,n){return this.complete(e),t&&a.Ad(1,!0,n),!0},this.complete=function(e){for(var i=[o];0<i.length;){var a=i.shift();(!a.R||a.parent&&a.parent.Z)&&a.Ia&&a.C&&n(a,e),a.G&&((a.parent&&a.parent.Z||a.Ea)&&r(a),t.gd(a,i))}},this.Eb=this.fc=this.ce=y.qa}var W,Z,$,K,Y=new function(){this.u=function(e,t){var n=e.globalAlpha;e.fillStyle="dark"===t?"white":"#1d3557",e.globalAlpha=1*n,e.save(),e.transform(.94115,0,0,.94247,-78.54,-58),e.beginPath(),e.moveTo(86.47,533.3),e.bezierCurveTo(83.52,531.5,83.45,530.6,83.45,488.3),e.bezierCurveTo(83.45,444.6,83.35,445.7,87.34,443.7),e.bezierCurveTo(88.39,443.1,90.5,442.5,92.02,442.4),e.bezierCurveTo(93.54,442.2,113,441.7,135.3,441.4),e.bezierCurveTo(177.9,440.7,179.3,440.7,182.7,443.4),e.bezierCurveTo(185.9,445.9,185.6,445,206.2,510.7),e.bezierCurveTo(207.8,515.8,209.5,521.3,210.1,522.9),e.bezierCurveTo(211.7,528,211.9,531.3,210.6,532.7),e.bezierCurveTo(209.5,534,208.4,534,148.5,534),e.bezierCurveTo(106.4,533.9,87.3,533.7,86.47,533.2),e.closePath(),e.fill(),e.globalAlpha=.8*n,e.beginPath(),e.moveTo(237.3,533.3),e.bezierCurveTo(234.8,532.5,233.1,530.9,231.7,528.1),e.bezierCurveTo(231,526.8,224.6,507,217.4,484.1),e.bezierCurveTo(203.1,438.8,202.6,436.7,205,431.4),e.bezierCurveTo(206.3,428.5,239.2,383.2,242.9,379.3),e.bezierCurveTo(245,377,246.9,376.7,249.7,378.2),e.bezierCurveTo(250.6,378.7,263.1,390.8,277.3,405.2),e.bezierCurveTo(301.1,429.2,303.4,431.6,305.1,435.5),e.bezierCurveTo(306.7,439,306.9,440.4,306.9,445.2),e.bezierCurveTo(306.8,455.3,302.2,526.4,301.5,528.9),e.bezierCurveTo(300.2,533.7,301,533.6,268.3,533.7),e.bezierCurveTo(252.2,533.8,238.3,533.6,237.3,533.3),e.closePath(),e.fill(),e.beginPath(),e.globalAlpha=.05*n,e.moveTo(329,533.3),e.bezierCurveTo(326.2,532.5,323.1,528.8,322.6,525.8),e.bezierCurveTo(322,521.6,327.2,446.1,328.4,442.2),e.bezierCurveTo(330.6,434.9,332.8,432.8,368.5,402.4),e.bezierCurveTo(387,386.7,403.9,372.8,406,371.4),e.bezierCurveTo(413.1,366.7,416,366.2,436.5,365.7),e.bezierCurveTo(456.8,365.2,463.6,365.6,470.2,367.6),e.bezierCurveTo(476.2,369.5,546.1,402.8,549.1,405.3),e.bezierCurveTo(550.4,406.3,552.2,408.7,553.2,410.5),e.lineTo(555,413.9),e.lineTo(555.2,459.5),e.bezierCurveTo(555.3,484.6,555.2,505.8,555,506.5),e.bezierCurveTo(554.4,509.1,548.1,517.9,543.8,522.2),e.bezierCurveTo(537.7,528.3,534.2,530.5,527.8,532.4),e.lineTo(522.3,534),e.lineTo(426.6,533.9),e.bezierCurveTo(371.1,533.9,330.1,533.6,328.9,533.3),e.closePath(),e.fill(),e.globalAlpha=.8*n,e.beginPath(),e.moveTo(87.66,423),e.bezierCurveTo(86.23,422.4,85.02,422,84.97,422),e.bezierCurveTo(84.91,422,84.55,421.1,84.16,419.9),e.bezierCurveTo(83.67,418.6,83.45,404.7,83.45,375.9),e.bezierCurveTo(83.45,328.4,83.27,330.3,88.12,328.1),e.bezierCurveTo(90.22,327.2,101.7,325.6,135.4,321.7),e.bezierCurveTo(159.9,318.8,181.1,316.5,182.5,316.5),e.bezierCurveTo(183.9,316.5,187,317.3,189.4,318.2),e.bezierCurveTo(193.5,319.8,194.7,320.8,210.1,336.2),e.bezierCurveTo(226.6,352.7,229.1,355.7,229.1,360),e.bezierCurveTo(229.1,363,226.8,366.5,212.9,385.4),e.bezierCurveTo(187.3,420.2,189.3,417.7,183.4,420.5),e.lineTo(179.5,422.3),e.lineTo(155.3,422.7),e.bezierCurveTo(89.91,424,90.39,423.9,87.65,423),e.closePath(),e.fill(),e.globalAlpha=.6*n,e.beginPath(),e.moveTo(314.6,415),e.bezierCurveTo(311.4,413.4,213.2,314.6,210.9,310.7),e.bezierCurveTo(208.9,307.2,208.5,303.4,209.9,300),e.bezierCurveTo(211.2,297,241.3,257,244.2,254.4),e.bezierCurveTo(247.3,251.7,252.9,249.7,257.4,249.7),e.bezierCurveTo(261.1,249.7,344.7,255.2,350.8,255.8),e.bezierCurveTo(358.5,256.6,363.1,259.5,366,265.1),e.bezierCurveTo(368.7,270.5,394.3,343.7,394.7,347.2),e.bezierCurveTo(395.1,351.6,393.6,356.1,390.5,359.5),e.bezierCurveTo(389.1,361,375.7,372.6,360.5,385.4),e.bezierCurveTo(326.7,414,327,413.7,324.5,415),e.bezierCurveTo(321.8,416.4,317.4,416.3,314.6,414.9),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(547.9,383.4),e.bezierCurveTo(547.1,383.2,533,376.6,516.5,368.7),e.bezierCurveTo(497.2,359.5,485.7,353.7,484.3,352.4),e.bezierCurveTo(481.6,349.8,480.2,346.5,480.2,342.5),e.bezierCurveTo(480.2,339.2,499.2,237,500.4,233.9),e.bezierCurveTo(502.2,229.1,506.2,225.8,511.3,224.9),e.bezierCurveTo(516.2,224,545.8,222.2,548.2,222.6),e.bezierCurveTo(551.5,223.2,553.7,224.7,555.1,227.3),e.bezierCurveTo(556.2,229.3,556.3,234,556.5,301.9),e.bezierCurveTo(556.6,341.8,556.5,375.7,556.3,377.2),e.bezierCurveTo(555.6,381.8,552,384.4,547.8,383.4),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(418.7,347),e.bezierCurveTo(416,346.1,413.6,344.3,412.3,342.1),e.bezierCurveTo(411.6,341,404.4,321.3,396.3,298.3),e.bezierCurveTo(382,258.1,381.5,256.4,381.5,251.7),e.bezierCurveTo(381.5,248.2,381.8,246.2,382.7,244.7),e.bezierCurveTo(383.4,243.4,389.5,233.9,396.5,223.4),e.bezierCurveTo(412.6,199,411.3,199.9,430.6,198.6),e.bezierCurveTo(445,197.6,449.5,197.9,454.2,200.4),e.bezierCurveTo(460.5,203.7,479.6,217.5,481.3,220.1),e.bezierCurveTo(484.3,224.6,484.3,224.6,473.1,284),e.bezierCurveTo(465.3,325.9,462.4,339.9,461.3,341.8),e.bezierCurveTo(458.7,346.4,457.1,346.7,437.5,347.1),e.bezierCurveTo(428.1,347.3,419.6,347.3,418.7,347),e.closePath(),e.fill(),e.globalAlpha=.05*n,e.beginPath(),e.moveTo(89.33,308.2),e.bezierCurveTo(88.1,307.5,86.5,306.2,85.77,305.2),e.bezierCurveTo(84.42,303.4,84.42,303.4,84.24,202.6),e.bezierCurveTo(84.11,131.7,84.27,100.2,84.77,96.34),e.bezierCurveTo(85.65,89.58,87.91,84.64,92.77,78.81),e.bezierCurveTo(96.86,73.9,103.2,68.42,107.1,66.53),e.bezierCurveTo(108.6,65.81,112.8,64.64,116.5,63.92),e.bezierCurveTo(122.7,62.73,125.4,62.64,148.5,62.81),e.lineTo(173.7,63),e.lineTo(177.4,64.82),e.bezierCurveTo(179.5,65.82,182.1,67.75,183.3,69.12),e.bezierCurveTo(185.6,71.9,228.8,145.1,231.3,150.7),e.bezierCurveTo(234.5,157.7,234.9,160.8,234.9,176.9),e.bezierCurveTo(234.8,201.7,233.8,229.6,232.8,233.2),e.bezierCurveTo(232.3,235,231.1,238.1,230.2,240),e.bezierCurveTo(228.3,243.9,196.9,286.6,192.7,290.9),e.bezierCurveTo(189.8,293.9,184.3,297.1,180.2,298.2),e.bezierCurveTo(177.6,298.9,95.84,309.3,93.04,309.3),e.bezierCurveTo(92.22,309.3,90.55,308.8,89.33,308.1),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(305.7,235.6),e.bezierCurveTo(254.5,232,256.5,232.3,253.9,227.1),e.lineTo(252.4,224.2),e.lineTo(253.1,196.7),e.bezierCurveTo(253.8,170.5,253.8,169.1,255.2,166.3),e.bezierCurveTo(257.7,161.2,256.9,161.4,309.3,151.9),e.bezierCurveTo(354.1,143.8,356.8,143.4,359.7,144.2),e.bezierCurveTo(361.4,144.6,363.8,145.8,365,146.8),e.bezierCurveTo(367.3,148.6,389,179.6,391.9,185.2),e.bezierCurveTo(393.8,188.7,394.1,193.5,392.6,196.9),e.bezierCurveTo(391.5,199.6,370.6,231.4,368.4,233.8),e.bezierCurveTo(365.4,237,362,238.3,356.3,238.5),e.bezierCurveTo(353.5,238.6,330.7,237.3,305.7,235.5),e.closePath(),e.fill(),e.globalAlpha=.2*n,e.beginPath(),e.moveTo(497.1,207.1),e.bezierCurveTo(496.2,206.8,494.4,206,493.2,205.4),e.bezierCurveTo(490,203.8,472.7,191.6,469.7,189),e.bezierCurveTo(467,186.6,465.7,183.2,466.2,180.2),e.bezierCurveTo(466.5,178.1,482.4,138.6,484.9,133.5),e.bezierCurveTo(486.5,130.3,488.4,128.2,490.9,126.8),e.bezierCurveTo(492.6,125.9,496.3,125.7,522.2,125.6),e.lineTo(551.5,125.4),e.lineTo(553.7,127.6),e.bezierCurveTo(555.2,129.1,556,130.5,556.3,132.6),e.bezierCurveTo(556.5,134.2,556.6,149.6,556.5,166.9),e.bezierCurveTo(556.3,195.4,556.2,198.5,555.1,200.4),e.bezierCurveTo(553.1,204.1,551.7,204.4,529.8,206.1),e.bezierCurveTo(509.2,207.7,499.9,207.9,497,207.1),e.closePath(),e.fill(),e.globalAlpha=.2*n,e.beginPath(),e.moveTo(412.5,180.5),e.bezierCurveTo(410.9,179.7,408.7,177.9,407.5,176.4),e.bezierCurveTo(403.5,171.3,380.5,137.2,379.2,134.3),e.bezierCurveTo(377.2,129.6,377.1,126.1,378.9,116.8),e.bezierCurveTo(386.5,77.56,388.4,68.28,389.5,66.46),e.bezierCurveTo(390.1,65.34,391.7,63.83,392.9,63.1),e.bezierCurveTo(395.1,61.84,396.2,61.78,419.4,61.78),e.bezierCurveTo(443.4,61.78,443.7,61.8,446.5,63.25),e.bezierCurveTo(448,64.06,449.9,65.81,450.7,67.14),e.bezierCurveTo(452.3,69.73,468,105.5,470,111.1),e.bezierCurveTo(471.4,114.9,471.6,119.1,470.5,122.3),e.bezierCurveTo(470.1,123.5,465.2,135.8,459.7,149.5),e.bezierCurveTo(446.7,181.4,448.1,179.8,431.5,181.2),e.bezierCurveTo(419,182.2,415.7,182,412.5,180.5),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(253.6,142.8),e.bezierCurveTo(250.2,141.8,246.6,139.4,244.7,136.7),e.bezierCurveTo(242.1,132.9,207.4,73.28,206.2,70.42),e.bezierCurveTo(205.1,67.89,205,67.1,205.7,65.54),e.bezierCurveTo(207.3,61.54,202.3,61.8,284.4,61.59),e.bezierCurveTo(325.7,61.48,360.8,61.58,362.4,61.81),e.bezierCurveTo(366,62.32,369.3,65.36,369.9,68.75),e.bezierCurveTo(370.4,71.55,362.4,113.9,360.5,118.1),e.bezierCurveTo(359.1,121.3,355,125,351.4,126.4),e.bezierCurveTo(348.9,127.3,267.1,142.3,259.5,143.2),e.bezierCurveTo(257.9,143.4,255.2,143.2,253.6,142.7),e.closePath(),e.fill(),e.globalAlpha=.1*n,e.beginPath(),e.moveTo(493.4,106.8),e.bezierCurveTo(490.3,106,488.2,104.5,486.5,101.7),e.bezierCurveTo(483.8,97.43,471.8,68.81,471.8,66.76),e.bezierCurveTo(471.8,62.64,470.7,62.76,512.1,62.76),e.bezierCurveTo(553.3,62.76,552.3,62.67,554.4,66.68),e.bezierCurveTo(555.2,68.34,555.3,71.23,555.2,85.75),e.lineTo(555,102.8),e.lineTo(551.4,106.4),e.lineTo(534.1,106.8),e.bezierCurveTo(510.7,107.4,495.9,107.4,493.3,106.8),e.closePath(),e.fill(),e.restore(),e.transform(.15905,0,0,.15905,-88.65,443.2),e.globalAlpha=1*n,e.save(),e.beginPath(),e.moveTo(557.4,564.9),e.lineTo(557.4,98),e.lineTo(885.8,98),e.lineTo(885.8,185.1),e.lineTo(650.8,185.1),e.lineTo(650.8,284.7),e.lineTo(824.1,284.7),e.lineTo(824.1,371.6),e.lineTo(650.8,371.6),e.lineTo(650.8,564.9),e.lineTo(557.4,564.9),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1029,568),e.quadraticCurveTo(961.1,568,915.7,522.5),e.quadraticCurveTo(870.2,476.7,870.2,409.2),e.quadraticCurveTo(870.2,341.3,915.7,295.9),e.quadraticCurveTo(961.1,250.4,1029,250.4),e.quadraticCurveTo(1096.8,250.4,1142.3,295.9),e.quadraticCurveTo(1187.7,341.3,1187.7,409.2),e.quadraticCurveTo(1187.7,477.1,1142.3,522.5),e.quadraticCurveTo(1097.3,568.1,1029.3,568.1),e.closePath(),e.moveTo(1028.6,492.6),e.quadraticCurveTo(1064.1,492.6,1086.2,469),e.quadraticCurveTo(1108.3,445,1108.3,409.5),e.quadraticCurveTo(1108.3,374,1086.2,350),e.quadraticCurveTo(1064.1,326.1,1028.3,326.1),e.quadraticCurveTo(993.1,326.1,971,350),e.quadraticCurveTo(948.9,374,948.9,409.5),e.quadraticCurveTo(948.9,445,971,469),e.quadraticCurveTo(993.1,492.6,1028.6,492.6),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1253,291),e.quadraticCurveTo(1312.1,253.6,1390,253.6),e.quadraticCurveTo(1446,253.6,1478.7,284.7),e.quadraticCurveTo(1511.4,315.9,1511.4,378.1),e.lineTo(1511.4,564.9),e.lineTo(1424.2,564.9),e.lineTo(1424.2,540),e.quadraticCurveTo(1386.2,564.9,1355.7,564.9),e.quadraticCurveTo(1293.5,564.9,1262.3,538.5),e.quadraticCurveTo(1231.2,512,1231.2,465.3),e.quadraticCurveTo(1231.2,421.7,1260.4,387.5),e.quadraticCurveTo(1290,353.3,1355.7,353.3),e.quadraticCurveTo(1385.9,353.3,1424.2,371.9),e.lineTo(1424.2,362.6),e.quadraticCurveTo(1423.6,328.4,1374.4,325.2),e.quadraticCurveTo(1318.3,325.2,1287.2,343.9),e.lineTo(1253,291),e.closePath(),e.moveTo(1424.2,471.5),e.lineTo(1424.2,436.3),e.quadraticCurveTo(1411.7,412.3,1365,412.3),e.quadraticCurveTo(1309,418.5,1305.9,455.9),e.quadraticCurveTo(1309,492.9,1365,496),e.quadraticCurveTo(1411.7,496,1424.2,471.5),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1675,365.7),e.lineTo(1675,564.9),e.lineTo(1587.8,564.9),e.lineTo(1587.8,262.5),e.lineTo(1675,253.2),e.lineTo(1675,280.9),e.quadraticCurveTo(1704.2,253.5,1749.7,253.5),e.quadraticCurveTo(1808.8,253.5,1839.9,289.3),e.quadraticCurveTo(1874.2,253.5,1942.6,253.5),e.quadraticCurveTo(2001.8,253.5,2032.9,289.3),e.quadraticCurveTo(2064,325.1,2064,371.8),e.lineTo(2064,564.8),e.lineTo(1976.9,564.8),e.lineTo(1976.9,393.6),e.quadraticCurveTo(1976.9,362.5,1962.9,345.4),e.quadraticCurveTo(1948.8,328.2,1917.4,327.3),e.quadraticCurveTo(1891.6,329.2,1872.6,361.6),e.quadraticCurveTo(1871,371.2,1871,381.2),e.lineTo(1871,564.9),e.lineTo(1783.9,564.9),e.lineTo(1783.9,393.7),e.quadraticCurveTo(1783.9,362.5,1769.9,345.4),e.quadraticCurveTo(1755.9,328.3,1724.4,327.4),e.quadraticCurveTo(1695.8,329.2,1674.9,365.7),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2058,97.96),e.lineTo(2058,185.1),e.lineTo(2213.6,185.1),e.lineTo(2213.6,564.9),e.lineTo(2306.9,564.9),e.lineTo(2306.9,185.1),e.lineTo(2462.5,185.1),e.lineTo(2462.5,97.96),e.lineTo(2057.8,97.96),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2549,287.8),e.quadraticCurveTo(2582.3,253.5,2630.2,253.5),e.quadraticCurveTo(2645.5,253.5,2659.2,256),e.lineTo(2645.5,341.9),e.quadraticCurveTo(2630.2,328.2,2601.9,327.3),e.quadraticCurveTo(2570.1,329.5,2549,373.4),e.lineTo(2549,564.8),e.lineTo(2461.8,564.8),e.lineTo(2461.8,262.5),e.lineTo(2549,253.1),e.lineTo(2549,287.7),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2694,409.2),e.quadraticCurveTo(2694,340.7,2737.5,297.1),e.quadraticCurveTo(2781.1,253.5,2849.6,253.5),e.quadraticCurveTo(2918.1,253.5,2958.5,297.1),e.quadraticCurveTo(2999,340.6,2999,409.2),e.lineTo(2999,440.3),e.lineTo(2784.2,440.3),e.quadraticCurveTo(2787.3,465.2,2806,479.2),e.quadraticCurveTo(2824.7,493.2,2849.6,493.2),e.quadraticCurveTo(2893.1,493.2,2927.4,468.3),e.lineTo(2977.2,518.1),e.quadraticCurveTo(2943,564.8,2849.6,564.8),e.quadraticCurveTo(2781.1,564.8,2737.5,521.2),e.quadraticCurveTo(2693.9,477.6,2693.9,409.1),e.closePath(),e.moveTo(2911.9,378),e.quadraticCurveTo(2911.9,353.1,2893.2,339.1),e.quadraticCurveTo(2874.5,325.1,2849.6,325.1),e.quadraticCurveTo(2824.7,325.1,2806,339.1),e.quadraticCurveTo(2787.3,353.1,2787.3,378),e.lineTo(2911.8,378),e.closePath(),e.fill(),e.beginPath(),e.moveTo(3052,409.2),e.quadraticCurveTo(3052,340.7,3095.5,297.1),e.quadraticCurveTo(3139.1,253.5,3207.6,253.5),e.quadraticCurveTo(3276.1,253.5,3316.5,297.1),e.quadraticCurveTo(3357,340.6,3357,409.2),e.lineTo(3357,440.3),e.lineTo(3142.2,440.3),e.quadraticCurveTo(3145.3,465.2,3164,479.2),e.quadraticCurveTo(3182.7,493.2,3207.6,493.2),e.quadraticCurveTo(3251.1,493.2,3285.4,468.3),e.lineTo(3335.2,518.1),e.quadraticCurveTo(3301,564.8,3207.6,564.8),e.quadraticCurveTo(3139.1,564.8,3095.5,521.2),e.quadraticCurveTo(3051.9,477.6,3051.9,409.1),e.closePath(),e.moveTo(3269.9,378),e.quadraticCurveTo(3269.9,353.1,3251.2,339.1),e.quadraticCurveTo(3232.5,325.1,3207.6,325.1),e.quadraticCurveTo(3182.7,325.1,3164,339.1),e.quadraticCurveTo(3145.3,353.1,3145.3,378),e.lineTo(3269.8,378),e.closePath(),e.fill(),e.restore()}};function J(e,n){function r(e,t){var n=e.O.r,r=n/15,i=.5*n/15;n/=5;var o=e.O.x;e=e.O.y,t.fillRect(o-i,e-i,r,r),t.fillRect(o-i-n,e-i,r,r),t.fillRect(o-i+n,e-i,r,r)}function i(e,t,n,r){null===e&&n.clearRect(0,0,D,j);var i,o=Array(te.length);for(i=te.length-1;0<=i;i--)o[i]=te[i].na(n,r);for(i=te.length-1;0<=i;i--)o[i]&&te[i].before(n,r);for(B.rc([n,z],(function(r){var i;if(null!==e){for(n.save(),n.globalCompositeOperation="destination-out",n.fillStyle=n.strokeStyle="rgba(255, 255, 255, 1)",i=e.length-1;0<=i;i--){var a=e[i],u=a.C;u&&(n.save(),n.beginPath(),a.Ib(n),s.Ud(n,u),n.fill(),0<(a=V.$a*Math.pow(V.La,a.level-1))&&(n.lineWidth=a/2,n.stroke()),n.restore())}n.restore()}if(r=r.scale,0!==t.length){for(i={},u=te.length-1;0<=u;u--)te[u].ng(i);for(a=ee.length-1;0<=a;a--)if(i[(u=ee[a]).id]){var l=u.Kd;for(u=0;u<t.length;u++){var c=t[u];!c.parent||c.parent.xa&&c.parent.R?l(c,r):c.aa.clear()}}}for(i=te.length-1;0<=i;i--)a=te[i],o[i]&&a.Nd(t,n,r)})),i=te.length-1;0<=i;i--)o[i]&&te[i].after(n);V.Zc&&(n.canvas.style.opacity=.99,setTimeout((function(){n.canvas.style.opacity=1}),1))}function u(){function e(t,n,r){t.sb=Math.floor(1e3*t.scale)-r*n,0<t.opacity&&!t.open&&n++;var i=t.m;if(i)for(var o=i.length-1;0<=o;o--)t.W&&e(i[o],n,r)}var t=null,n=null,r=null;return B.rc([],(function(i){!function(e){b===_?e<.9*G&&(b=v,x=A,h()):e>=G&&(b=_,x=T,h())}(i.scale);var o=!1;S.L(I,(function(e){e.W&&(o=e.vd()||o,e.cc(),e.Ma=U.i(e)||e.Ma)})),o&&(I.N=!0);var a="onSurfaceDirty"===V.Lg;S.fd(I,(function(e){e.parent&&e.parent.Z&&(e.aa.clear(),e.Ma=!0,a||(e.oc=!0,e.Qb.clear())),a&&(e.oc=!0,e.Qb.clear())}));var s=i.scale*i.scale;if(S.fd(I,(function(e){if(e.R){for(var t=e.m,n=0;n<t.length;n++)if(5<t[n].O.ha*s)return void(e.X=!0);e.X=!1}})),function(e){I.Y=!0,S.fd(I,(function(t){if(t.W&&t.X&&t.xa&&t.R&&(I.N||t.Z||t.Vd)){t.Vd=!1;var n=t.m,r={x:0,y:0,w:0,o:0},i=!!t.G;if(1<D/e.w){var o;for(o=n.length-1;0<=o;o--)n[o].Y=!1;if(t.Y&&i)for(o=n.length-1;0<=o;o--)if(1!==(t=n[o]).scale&&(t.Jb(e,r),r.w=e.w/t.scale,r.o=e.o/t.scale),!1===t.Y&&t.C){var a=(i=t.C).length;if(M.sa(t.C,1===t.scale?e:r))t.Y=!0;else for(var s=0;s<a;s++)if(M.Vc(i[s],i[(s+1)%a],1===t.scale?e:r)){t.Y=!0,t.J&&(t=t.J[s])&&(n[t.index].Y=!0);break}}}else for(o=0;o<n.length;o++)n[o].Y=i}}))}(i),r=[],S.tc(I,(function(e){if(e.parent.X&&e.Y&&e.W){r.push(e);for(var t=e.parent;t!==I&&(t.open||0===t.opacity);)t=t.parent;t!==I&&.02>Math.abs(t.scale-e.scale)&&(e.scale=Math.min(e.scale,t.scale))}})),e(I,0,"flattened"===V.mb?-1:1),r.sort((function(e,t){return e.sb-t.sb})),l())t=r,n=null;else{var u={},c={},f="none"!=V.ld&&V.$a<V.ab/2,d=V.$a<V.yc/2+V.kd*V.De.a;S.L(I,(function(e){if(e.W&&!e.description&&(e.Z||e.N||e.Fc&&e.parent.X&&e.Ma)){var t,n,r,i=[e],o=e.J||e.parent.m;if(f)for(t=0;t<o.length;t++)(n=o[t])&&i.push(n);else if(d)if(!e.selected&&e.Sa){for(n=!0,t=0;t<o.length;t++)o[t]?i.push(o[t]):n=!1;!n&&1<e.level&&i.push(e.parent)}else for(t=0;t<o.length;t++)(n=o[t])&&n.selected&&i.push(n);for(t=e.parent;t!=I;)t.selected&&(r=t),t=t.parent;for(r&&i.push(r),t=0;t<i.length;t++){for(e=(r=i[t]).parent;e&&e!==I;)0<e.opacity&&(r=e),e=e.parent;c[r.id]=!0,S.za(r,(function(e){u[e.id]=!0}))}}})),t=r.filter((function(e){return u[e.id]})),n=t.filter((function(e){return c[e.id]}))}})),function(){var e=!1;V.Gf&&S.L(I,(function(t){if(t.W&&0!==t.pa.a&&1!==t.pa.a)return e=!0,!1})),e?(S.sc(I,(function(e){if(e.W&&(e.opacity!==e.Jc||e.Fa)){var t=e.m;if(t){for(var n=0,r=t.length-1;0<=r;r--)n=Math.max(n,t[r].Ec);e.Ec=n+e.opacity*e.pa.a}else e.Ec=e.opacity*e.pa.a}})),S.L(I,(function(e){if(e.W&&(e.opacity!==e.Jc||e.Fa)){for(var t=e.Ec,n=e;(n=n.parent)&&n!==I;)t+=n.opacity*n.pa.a*V.Ef;e.$c=0<t?1-Math.pow(1-e.pa.a,1/t):0,e.Jc=e.opacity}}))):S.L(I,(function(e){e.W&&(e.$c=1,e.Jc=-1)}))}(),{ag:t,$f:n,Y:r}}function l(){var e=I.Z||I.N||"none"==V.Ke;if(!e&&!I.empty()){var t=I.m[0].scale;S.L(I,(function(n){if(n.W&&n.Y&&n.scale!==t)return e=!0,!1}))}return!e&&0<V.xe&&1!=V.Pa&&S.L(I,(function(t){if(t.W&&0<t.ja)return e=!0,!1})),"accurate"==V.Ke&&(!(e=(e=e||0===V.$a)||"none"!=V.ld&&V.$a<V.ab/2)&&V.$a<V.yc/2+V.kd*V.De.a&&S.L(I,(function(t){if(t.W&&(t.selected&&!t.Sa||!t.selected&&t.Sa))return e=!0,!1}))),e}function h(){function e(e,n,r,i,o){function a(e,t,n,r,i){return e[r]&&(t-=n*p[r],e[r]=!1,i&&(t+=n*p[i],e[i]=!0)),t}switch(e=y.extend({},e),r){case"never":e.labelPlainFill=!1;break;case"always":case"auto":e.labelPlainFill=!0}if(V.xc)switch(i){case"never":e.contentDecoration=!1;break;case"always":case"auto":e.contentDecoration=!0}else e.contentDecoration=!1;var s=0;return y.Aa(e,(function(e,t){e&&(s+=n*p["contentDecoration"===t?"labelPlainFill":t])})),e.polygonExposureShadow=t,(s+=2*p.polygonExposureShadow)<=o||(s=a(e,s,2,"polygonExposureShadow"))<=o||(s=a(e,s,n,"polygonGradientFill","polygonPlainFill"))<=o||(s=a(e,s,n,"polygonGradientStroke"))<=o||(s=a(e,s,n,"polygonPlainStroke"))<=o||"auto"===i&&(s=a(e,s,n,"contentDecoration"))<=o||"auto"===r&&(s=a(e,s,n,"labelPlainFill")),e}var t=b===v,n=0,r=0;S.ne(I,(function(e){var t=1;S.L(e,(function(){t++})),n+=t,r=Math.max(r,t)}));var i={};switch(V.Ug){case"plain":i.polygonPlainFill=!0;break;case"gradient":i.polygonPlainFill=!t,i.polygonGradientFill=t}switch(V.ld){case"plain":i.polygonPlainStroke=!0;break;case"gradient":i.polygonPlainStroke=!t,i.polygonGradientStroke=t}P=e(i,n,V.gj,V.ej,V.fj),R=e(i,2*r,"always","always",V.Eg),F=e(i,n,"always","always",V.Dg)}function f(e){return function(t,n){return t===b?!0===P[e]:!0===(n?R:F)[e]}}function d(e,t){return function(n,r){return e(n,r)&&t(n,r)}}var p,g,b,v,_,w,x,A,T,k,z,D,j,L,B,E,O,I,N,P,F,R,G=t.Se()?50:1e4,U=new X(e),H=new Q(e),V=e.options;e.j.subscribe("stage:initialized",(function(e,t,n,r){D=n,j=r,g=(L=e).dc("wireframe",V.nb,!1),v=g.getContext("2d"),_=new o(v),w=L.dc("hifi",V.B,!1),A=w.getContext("2d"),T=new o(A),b=v,x=A,v.B=V.nb,_.B=V.nb,A.B=V.B,T.B=V.B,k=L.dc("tmp",Math.max(V.B,V.nb),!0),(z=k.getContext("2d")).B=1,[v,A,z].forEach((function(e){e.scale(e.B,e.B)}))})),e.j.subscribe("stage:resized",(function(e,t,n,r){D=n,j=r,[v,A,z].forEach((function(e){e.scale(e.B,e.B)}))})),e.j.subscribe("model:loaded",(function(t){N=!0,function e(t){var n=0;if(!t.empty()){for(var r=t.m,i=r.length-1;0<=i;i--)n=Math.max(n,e(r[i]));n+=1}return t.Sf=n}(I=t),h(),e.j.D("render:renderers:resolved",P,R,F)}));var q="groupFillType groupStrokeType wireframeDrawMaxDuration wireframeLabelDrawing wireframeContentDecorationDrawing finalCompleteDrawMaxDuration finalIncrementalDrawMaxDuration groupContentDecorator".split(" "),J=["groupLabelLightColor","groupLabelDarkColor","groupLabelColorThreshold","groupUnexposureLabelColorThreshold"];e.j.subscribe("options:changed",(function(e){function t(e,t,n,r){L.Hi(e,n),t.B=n,r&&t.scale(n,n)}e.dataObject||(y.bb(e,q)&&h(),y.bb(e,J)&&S.L(I,(function(e){e.hd=-1})));var n=y.has(e,"pixelRatio");e=y.has(e,"wireframePixelRatio"),(n||e)&&(n&&t(w,x,V.B,!0),e&&t(g,b,V.nb,!0),t(k,z,Math.max(V.B,V.nb),!1))})),e.j.subscribe("zoom:initialized",(function(e){B=e})),e.j.subscribe("timeline:initialized",(function(e){E=e})),e.j.subscribe("api:initialized",(function(e){O=e}));var ee=[{id:"offsetPolygon",Kd:function(e){if((e.selected||0<e.opacity&&!1===e.open||!e.X)&&e.aa.Ga()){var t=e.aa;if(t.clear(),e.$){var n=e.$,r=V.Gg;0<r?s.Ti(t,n,e.parent.O.r/32,Math.min(1,r*Math.pow(1-V.Hg*r,e.Sf))):s.Ud(t,n)}e.Dd=!0}}},{id:"label",Kd:function(e){e.Ma&&e.Fc&&U.u(e)}},{id:"custom",Kd:function(t,n){if(t.$&&(0<t.opacity&&(!1===t.open||!0===t.selected)||!t.X)&&t.oc&&e.options.xc&&!t.attribution){var r={};O.Xc(r,t),O.Yc(r,t),O.Wc(r,t,!0),r.context=t.Qb,r.polygonContext=t.aa,r.labelContext=t.Bc,r.shapeDirty=t.Dd,r.viewportScale=n,n={groupLabelDrawn:!0,groupPolygonDrawn:!0},e.options.Kg(e.Cd,r,n),t.Te=n.groupLabelDrawn,t.Ed=n.groupPolygonDrawn,t.Dd=!1,t.oc=!1}}}].reverse(),te=[new function(e){var t=Array(e.length);this.Nd=function(n,r,i){if(0!==n.length){var o,a=[],s=n[0].sb;for(o=0;o<n.length;o++){var u=n[o];u.sb!==s&&(a.push(o),s=u.sb)}a.push(o);for(var l=s=0;l<a.length;l++){for(var c=a[l],h=e.length-1;0<=h;h--)if(t[h]){var f=e[h];for(r.save(),o=s;o<c;o++)u=n[o],r.save(),u.Ib(r),f.kb.call(f,u,r,i),r.restore();f.Wa.call(f,r,i),r.restore()}s=c}}},this.na=function(n,r){for(var i=!1,o=e.length-1;0<=o;o--)t[o]=e[o].na(n,r),i|=t[o];return i},this.before=function(n,r){for(var i=e.length-1;0<=i;i--)if(t[i]){var o=e[i];o.before.call(o,n,r)}},this.after=function(n){for(var r=e.length-1;0<=r;r--)if(t[r]){var i=e[r];i.after.call(i,n)}},this.ng=function(n){for(var r=e.length-1;0<=r;r--){var i=e[r];if(t[r])for(var o=i.Ra.length-1;0<=o;o--)n[i.Ra[o]]=!0}}}([{Ra:["offsetPolygon"],na:f("polygonExposureShadow"),before:function(e){z.save(),z.scale(e.B,e.B)},after:function(){z.restore()},rb:function(){},Wa:function(e){this.Rf&&(this.Rf=!1,e.save(),e.setTransform(1,0,0,1,0,0),e.drawImage(k,0,0,e.canvas.width,e.canvas.height,0,0,e.canvas.width,e.canvas.height),e.restore(),z.save(),z.setTransform(1,0,0,1,0,0),z.clearRect(0,0,k.width,k.height),z.restore())},kb:function(e,t,n){if(!(e.open&&e.X||e.aa.Ga())){var r=V.xe*e.opacity*e.ja*("flattened"===V.mb?1-e.parent.ja:(1-e.Cb)*e.parent.Cb)*(1.1<=V.Pa?1:(V.Pa-1)/.1);0<r&&(z.save(),z.beginPath(),e.Ib(z),e.aa.Na(z),z.shadowBlur=n*t.B*r,z.shadowColor=V.Mg,z.fillStyle="rgba(0, 0, 0, 1)",z.globalCompositeOperation="source-over",z.globalAlpha=e.opacity,z.fill(),z.shadowBlur=0,z.shadowColor="transparent",z.globalCompositeOperation="destination-out",z.fill(),z.restore(),this.Rf=!0)}}},{Ra:["offsetPolygon"],na:function(){return!0},before:function(){function e(e){var n=e.pa,r=e.ub,i=e.selected,o=(n.h+(r?V.Yg:0)+(i?V.ph:0))%360,a=t(n.l*e.va+(r?V.Zg:0)+(i?V.qh:0));return n=t(n.s*e.saturation+(r?V.$g:0)+(i?V.rh:0)),(e=e.we).h=o,e.s=n,e.l=a,e}function t(e){return 100<e?100:0>e?0:e}var n=[{type:"fill",na:f("polygonPlainFill"),Pc:function(t,n){n.fillStyle=c.H(e(t))}},{type:"fill",na:f("polygonGradientFill"),Pc:function(n,r){var i=n.O.r,o=e(n);i=r.createRadialGradient(n.x,n.y,0,n.x,n.y,i*V.Qg);var a=o.l,s=V.Og;i.addColorStop(0,c.i((o.h+V.Ng)%360,t(o.s+V.Pg),t(a+s))),a=o.l,s=V.Sg,i.addColorStop(1,c.i((o.h+V.Rg)%360,t(o.s+V.Tg),t(a+s))),n.aa.Na(r),r.fillStyle=i}},{type:"stroke",na:d(f("polygonPlainStroke"),(function(){return 0<V.ab})),Pc:function(e,n){var r=e.pa,i=e.ub,o=e.selected,a=(r.h+V.He+(i?V.ye:0)+(o?V.Ee:0))%360,s=t(r.s*e.saturation+V.Je+(i?V.Ae:0)+(o?V.Ge:0));r=t(r.l*e.va+V.Ie+(i?V.ze:0)+(o?V.Fe:0)),n.strokeStyle=c.i(a,s,r),n.lineWidth=V.ab*Math.pow(V.La,e.level-1)}},{type:"stroke",na:d(f("polygonGradientStroke"),(function(){return 0<V.ab})),Pc:function(e,n){var r=e.O.r*V.xh,i=e.pa,o=Math.PI*V.th/180;r=n.createLinearGradient(e.x+r*Math.cos(o),e.y+r*Math.sin(o),e.x+r*Math.cos(o+Math.PI),e.y+r*Math.sin(o+Math.PI));var a=e.ub,s=e.selected;o=(i.h+V.He+(a?V.ye:0)+(s?V.Ee:0))%360;var u=t(i.s*e.saturation+V.Je+(a?V.Ae:0)+(s?V.Ge:0));i=t(i.l*e.va+V.Ie+(a?V.ze:0)+(s?V.Fe:0)),a=V.vh,r.addColorStop(0,c.i((o+V.uh)%360,t(u+V.wh),t(i+a))),a=V.zh,r.addColorStop(1,c.i((o+V.yh)%360,t(u+V.Ah),t(i+a))),n.strokeStyle=r,n.lineWidth=V.ab*Math.pow(V.La,e.level-1)}}],r=Array(n.length);return function(e,t){for(var i=n.length-1;0<=i;i--)r[i]=n[i].na(e,t);this.Xi=n,this.vg=r}}(),after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){if(!(!e.Ed||(0===e.opacity||e.open)&&e.X||e.aa.Ga()||!V.je&&e.description)){var n=this.Xi,r=this.vg;t.beginPath(),e.aa.Na(t);for(var i=!1,o=!1,a=n.length-1;0<=a;a--){var s=n[a];if(r[a])switch(s.Pc(e,t),s.type){case"fill":i=!0;break;case"stroke":o=!0}}n=(e.X?e.opacity:1)*e.pa.a,r=!e.empty(),a=V.Gf?e.$c:1,i&&(e=r&&e.X&&e.R&&e.m[0].W?1-e.m.reduce((function(e,t){return e+t.ra*t.Hd}),0)/e.m.length*(1-V.Ef):1,t.globalAlpha=n*e*a,W(t)),o&&(t.globalAlpha=n*(r?V.Xh:1)*a,t.closePath(),Z(t),t.stroke())}}},{Ra:["offsetPolygon"],na:function(){return 0<V.yc},before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){if(e.Ed&&e.selected&&!e.aa.Ga()){t.globalAlpha=e.Da,t.beginPath();var r=Math.pow(V.La,e.level-1);t.lineWidth=V.yc*r,t.strokeStyle=V.sh;var i=V.kd;0<i&&(t.shadowBlur=i*r*n*t.B,t.shadowColor=V.Ce),e.aa.Na(t),t.closePath(),t.stroke()}}},{Ra:[],na:function(){return!0},before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){e.attribution&&!e.aa.Ga()&&function(n,r,i){var o=M.Ka(e.$,e.O,n/r);o=Math.min(Math.min(.9*o,.5*e.F.o)/r,.5*e.F.w/n),t.save(),t.translate(e.x,e.y),t.globalAlpha=e.opacity*e.ca,t.scale(o,o),t.translate(-n/2,-r/2),i(t),t.restore()}(Y.i.width,Y.i.height,(function(e){Y.u(e,V.ae)}))}},{Ra:[],na:function(e,t){return function(n,r){return e(n,r)||t(n,r)}}(f("labelPlainFill"),d(f("contentDecoration"),(function(){return V.xc}))),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){(0<e.opacity&&0<e.ca&&!e.open||!e.X)&&!e.aa.Ga()&&(e.Cc=e.oa&&e.oa.ka&&V.B*e.oa.fontSize*e.scale*n>=V.mh,"auto"===e.pd?!V.je&&e.description?e.fb=e.parent.fb:(t=(n=e.we).h+(n.s<<9)+(n.l<<16),e.hd!==t&&(n=c.T(n),e.fb=n>(0>e.ja?V.Bh:V.ah)?V.bh:V.lh,e.hd=t)):e.fb=e.pd)}},{Ra:["custom"],na:d(f("contentDecoration"),(function(){return V.xc})),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){!(0<e.opacity&&0<e.ca&&!e.open||!e.X)||e.Qb.Ga()||e.aa.Ga()||(e.Cc||void 0===e.oa?(t.globalAlpha=e.ca*(e.X?e.opacity:1)*(e.empty()?1:V.Ff),t.fillStyle=e.fb,t.strokeStyle=e.fb,e.Qb.Na(t)):r(e,t))}},{Ra:["label"],na:f("labelPlainFill"),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){e.Te&&e.Fc&&(0<e.opacity&&0<e.ca&&!e.open||!e.X)&&!e.aa.Ga()&&e.oa&&(t.fillStyle=e.fb,t.globalAlpha=e.ca*(e.X?e.opacity:1)*(e.empty()?1:V.Ff),e.Cc?K(e,t,n):r(e,t))}}].reverse())];this.M=function(){p=C((function(){return a.estimate()}),"CarrotSearchFoamTree",12096e5)({version:"3.5.0",build:"bugfix/3.5.x/e3b91c8e",brandingAllowed:!1}),H.M()},this.clear=function(){b.clearRect(0,0,D,j),x.clearRect(0,0,D,j)};var ne=!1,re=void 0;this.u=function(e){ne?re=e:e()},this.Nd=function(){function e(){window.clearTimeout(t),ne=!0,t=setTimeout((function(){if(ne=!1,function(){if(V.B!==V.nb)return!0;var e="polygonPlainFill polygonPlainStroke polygonGradientFill polygonGradientStroke labelPlainFill contentDecoration".split(" ");S.L(I,(function(t){if(t.W&&t.U)return e.push("polygonExposureShadow"),!1}));for(var t=e.length-1;0<=t;t--){var n=e[t];if(!!P[n]!=!!R[n])return!0}return!1}()){var e=!l();i(null,r.Y,x,e),y.defer((function(){ie.Ui(),re&&(re(),re=void 0)}))}else re&&(re(),re=void 0)}),Math.max(V.hj,3*n.Wf.sd,3*n.Wf.rd))}var t,r;return function(t){$(H);var n=null!==(r=u()).$f,o=0<L.$b("hifi"),a=o&&(n||!t);t=n||N||!t,N=!1,o&&!a&&ie.Vi(),i(r.$f,r.ag,a?x:b,t),S.za(I,(function(e){e.Z=!1,e.N=!1,e.Sa=!1})),a||e(),V.Af(n)}}(),this.i=function(e){e=e||{},$(H),I.N=!0;var t=u(),n=V.B;try{var r=y.I(e.pixelRatio,V.B);V.B=r;var a=L.dc("export",r,!0),s=a.getContext("2d");b===_&&(s=new o(s)),s.scale(r,r);var l=y.has(e,"backgroundColor");l&&(s.save(),s.fillStyle=e.backgroundColor,s.fillRect(0,0,D,j),s.restore()),i(l?[]:null,t.ag,s,!0)}finally{V.B=n}return a.toDataURL(y.I(e.format,"image/png"),y.I(e.quality,.8))};var ie=function(){function e(e,t,r,i,o,a){function s(e,t,n,r){return E.K.A({opacity:L.$b(e)}).fa({duration:n,P:{opacity:{end:t,easing:r}},ba:function(){L.$b(e,this.opacity)}}).done()}var u=y.od(L.$b(e),t),l=y.od(L.$b(i),o);if(!u||!l){for(var c=n.length-1;0<=c;c--)n[c].stop();return n=[],u||n.push(s(e,t,r,m.Gb)),l||n.push(s(i,o,a,m.Tf)),E.K.A({}).Qa(n).start()}}var t,n=[];return{Vi:function(){V.Zc?1!==g.style.opacity&&(g.style.visibility="visible",w.style.visibility="hidden",g.style.opacity=1,w.style.opacity=0):t&&t.xb()||(t=e("wireframe",1,V.se,"hifi",0,V.se))},Ui:function(){V.Zc?(w.style.visibility="visible",g.style.visibility="hidden",g.style.opacity=0,w.style.opacity=1):e("hifi",1,V.dg,"wireframe",0,V.dg)}}}();return $=function(e){e.apply()},W=function(e){e.fill()},Z=function(e){e.stroke()},this}function X(e){function t(e){void 0!==e.groupLabelFontFamily&&(i.fontFamily=e.groupLabelFontFamily),void 0!==e.groupLabelFontStyle&&(i.fontStyle=e.groupLabelFontStyle),void 0!==e.groupLabelFontVariant&&(i.fontVariant=e.groupLabelFontVariant),void 0!==e.groupLabelFontWeight&&(i.fontWeight=e.groupLabelFontWeight),void 0!==e.groupLabelLineHeight&&(i.lineHeight=e.groupLabelLineHeight),void 0!==e.groupLabelHorizontalPadding&&(i.cb=e.groupLabelHorizontalPadding),void 0!==e.groupLabelVerticalPadding&&(i.Ua=e.groupLabelVerticalPadding),void 0!==e.groupLabelMaxTotalHeight&&(i.ib=e.groupLabelMaxTotalHeight),void 0!==e.groupLabelMaxFontSize&&(i.hb=e.groupLabelMaxFontSize)}var n,r=e.options,i={},o={},a={groupLabel:""},s={};e.j.subscribe("api:initialized",(function(e){n=e})),e.j.subscribe("options:changed",t),t(e.Cd),this.i=function(e){if(!e.$)return!1;var t=e.group.label;return r.eh&&!e.attribution&&(a.labelText=t,n.nc(r.dh,e,a),t=a.labelText),e.Ue=t,e.qd!==t},this.u=function(e){var t=e.Ue;if(e.qd=t,e.Bc.clear(),e.oa=void 0,e.$&&!y.Ne(t)&&("flattened"!==r.mb||e.empty()||!e.R||!e.m[0].W)){var a=j,u=a.de;if(r.kh){s.fontFamily=i.fontFamily,s.fontStyle=i.fontStyle,s.fontVariant=i.fontVariant,s.fontWeight=i.fontWeight,s.lineHeight=i.lineHeight,s.horizontalPadding=i.cb,s.verticalPadding=i.Ua,s.maxTotalTextHeight=i.ib,s.maxFontSize=i.hb,n.nc(r.jh,e,s),o.fontFamily=s.fontFamily,o.fontStyle=s.fontStyle,o.fontVariant=s.fontVariant,o.fontWeight=s.fontWeight,o.lineHeight=s.lineHeight,o.cb=s.horizontalPadding,o.Ua=s.verticalPadding,o.ib=s.maxTotalTextHeight,o.hb=s.maxFontSize;var l=o}else l=i;e.oa=u.call(a,l,e.Bc,t,e.$,e.F,e.O,!1,!1,e.Mh,e.O.ha,r.nh,e.Ma)}e.Ma=!1},K=this.H=function(e,t){e.Bc.Na(t)}}function Q(e){function t(e,t){var n,r=e.m,i=r.length,a=o.O.r;for(n=0;n<i;n++){var s=r[n];s.tb=(180*(Math.atan2(s.x-e.x,s.y-e.y)+t)/Math.PI+180)/360,s.wc=Math.min(1,Math.sqrt(M.i(s,e))/a)}}function n(e,t){var n=(e=e.m).length;if(1===n||2===n&&e[0].description)e[0].tb=.5;else{var r=0,i=Number.MAX_VALUE,o=Math.sin(t),a=Math.cos(t);for(t=0;t<n;t++){var s=e[t],u=s.x*o+s.y*a;r<u&&(r=u),i>u&&(i=u),s.tb=u,s.wc=1}for(t=0;t<n;t++)(s=e[t]).tb=(s.tb-i)/(r-i)}}function r(e,t,n,r){return(t=t[r])+(n[r]-t)*e}var i,o,a={radial:t,linear:n},s=e.options,u={groupColor:null,labelColor:null};return e.j.subscribe("model:loaded",(function(e){o=e})),e.j.subscribe("api:initialized",(function(e){i=e})),this.M=function(){},this.apply=function(){function e(e,t,n,r){var i=l(e+n*r);return i+t*((e=l(e-n*(1-r)))-i)}function l(e){return 0>e?0:100<e?100:e}var h=a[s.vi]||t,f=n,d=s.Fi,p=s.yi,g=s.Ig,b=s.Jg,v=s.zi,m=s.Di;!function t(n){if(n.R&&n.xa){var o,a=n.m;if(n.Z||n.Fa||b){for(0===n.level?h(n,s.wi*Math.PI/180):f(n,s.Ai*Math.PI/180),o=a.length-1;0<=o;o--){var l=a[o];l.Fa=!0;var C=l.tb,_=l.ve;if(0===n.level)var w=r(C,d,p,"h"),x=(m+(1-m)*l.wc)*r(C,d,p,"s"),A=(1+(0>l.ja?v*(l.ja+1):v)*(1-l.wc))*r(C,d,p,"l"),S=r(C,d,p,"a");else w=(A=n.pa).h,x=A.s,A=e(A.l,C,s.Bi,s.Ci),S=n.ve.a;_.h=w,_.s=x,_.l=A,_.a=S,w=l.pa,l.attribution?(w.h=0,w.s=0,w.l="light"==s.ae?90:10,w.a=1):(w.h=_.h,w.s=_.s,w.l=_.l,w.a=_.a),b&&!l.attribution&&(u.groupColor=w,u.labelColor="auto",i.nc(g,l,u,(function(e){e.ratio=C})),l.pa=c.u(u.groupColor),l.pa.a=y.has(u.groupColor,"a")?u.groupColor.a:1,"auto"!==u.labelColor&&(l.pd=c.wa(u.labelColor)))}n.Fa=!1}for(o=a.length-1;0<=o;o--)t(a[o])}}(o)},this}function ee(){this.kc=this.Yd=this.hc=this.Vf=this.w=this.cg=this.weight=this.y=this.x=this.id=0,this.C=this.parent=this.m=null,this.F={x:0,y:0,w:0,o:0},this.J=null,this.qd=this.Ue=void 0,this.Sc=!1,this.wc=this.tb=0,this.ve={h:0,s:0,l:0,a:0,model:"hsla"},this.pa={h:0,s:0,l:0,a:0,model:"hsla"},this.we={h:0,s:0,l:0,model:"hsl"},this.hd=-1,this.pd="auto",this.fb="#000",this.Sf=this.level=this.nd=this.index=0,this.attribution=!1,this.ha=this.Ze=0,this.Y=!1,this.$=null,this.O={x:0,y:0,ha:0,r:0},this.Fd=this.G=null,this.Fc=this.W=this.Sa=this.oc=this.Vd=this.Dd=this.Ma=this.Fa=this.N=this.Z=this.Ea=this.xa=this.R=this.Ia=!1,this.saturation=this.va=this.Da=this.ca=this.opacity=this.scale=1,this.ra=0,this.Hd=1,this.Cb=this.ja=this.yb=0,this.description=this.selected=this.ub=this.Bd=this.open=this.U=!1,this.sb=0,this.Te=this.Ed=this.X=!0,this.oa=void 0,this.Cc=!1,this.Bc=new i,this.aa=new i,this.Qb=new i,this.Mh=j.Zh(),this.Ec=0,this.$c=1,this.Jc=-1,this.empty=function(){return!this.m||0===this.m.length};var e=[];this.mc=function(t){e.push(t)},this.Nc=function(t){y.If(e,t)};var t={scale:1};this.vd=function(){var n=!1;this.scale=1;for(var r=0;r<e.length;r++)n=e[r].Ve(this,t)||n,this.scale*=t.scale;return n},this.Ib=function(t){for(var n=0;n<e.length;n++)e[n].Ib(this,t)},this.transformPoint=function(t,n){for(n.x=t.x,n.y=t.y,t=0;t<e.length;t++)e[t].transformPoint(this,n,n);return n},this.Jb=function(t,n){for(n.x=t.x,n.y=t.y,t=0;t<e.length;t++)e[t].Jb(this,n,n);return n};var n=[];this.qb=function(e){n.push(e)},this.Mc=function(e){y.If(n,e)};var r={opacity:1,saturation:1,va:1,ca:1,Da:1};this.cc=function(){if(0!==n.length){this.Da=this.ca=this.va=this.saturation=this.opacity=1;for(var e=n.length-1;0<=e;e--)(0,n[e])(this,r),this.opacity*=r.opacity,this.va*=r.va,this.saturation*=r.saturation,this.ca*=r.ca,this.Da*=r.Da}}}function te(e,t){return t.weight>e.weight?1:t.weight<e.weight?-1:e.index-t.index}function ne(e){var t,n,r,i,o,a,s=this,u=e.options;e.j.subscribe("stage:initialized",(function(o,a,l,c){r=l,i=c,t=o.dc("titlebar",u.B,!1),(n=t.getContext("2d")).B=u.B,n.scale(n.B,n.B),e.j.D("titlebar:initialized",s)})),e.j.subscribe("stage:resized",(function(e,t,o,a){r=o,i=a,n.scale(n.B,n.B)})),e.j.subscribe("zoom:initialized",(function(e){a=e})),e.j.subscribe("api:initialized",(function(e){o=e})),e.j.subscribe("model:loaded",(function(){n.clearRect(0,0,r,i)})),this.update=function(e){if(n.clearRect(0,0,r,i),e){!e.empty()&&e.m[0].description&&(e=e.m[0]);var t=u.bj,s=u.aj,l=Math.min(i/2,u.Wd+2*t),c=l-2*t,h=r-2*s;if(!(0>=c||0>=h)){var f=e.Cc?e.oa.fontSize*e.scale*a.scale():0,d={titleBarText:e.qd,titleBarTextColor:u.Zf,titleBarBackgroundColor:u.Yf,titleBarMaxFontSize:u.Wd,titleBarShown:f<u.Sh};if(e.attribution)var p=x("B`ssnu!Rd`sbi!Gn`lUsdd!whrt`mh{`uhno/!Busm,bmhbj!uid!mnfn!un!fn!un!iuuqr;..b`ssnurd`sbi/bnl.gn`lusdd!gns!lnsd!edu`hmr/");else o.nc(u.Yi,e,d,(function(e){e.titleBarWidth=h,e.titleBarHeight=c,e.labelFontSize=f,e.viewportScale=a.scale()})),p=d.titleBarText;p&&0!==p.length&&d.titleBarShown&&(t={x:s,y:(e=a.Uc(e.transformPoint(e,{}),{}).y>i/2)?t:i-l+t,w:h,o:c},s=M.H(t),n.fillStyle=u.Yf,n.fillRect(0,e?0:i-l,r,l),n.fillStyle=u.Zf,j.re({fontFamily:u.Zi||u.fh,fontStyle:u.Aj||u.gh,fontWeight:u.Cj||u.ih,fontVariant:u.Bj||u.hh,hb:u.Wd,Gc:u.$i,cb:0,Ua:0,ib:1},n,p,s,t,{x:t.x+t.w/2,y:t.y+t.o/2},!0,!0).ka||n.clearRect(0,0,r,i))}}}}function re(e){function t(e,t,n){return C=!0,u&&u.stop(),c&&c.stop(),a(p.reset(e),t,n).then((function(){C=!1}))}function n(t){p.update(t),f.N=!0,e.j.D("foamtree:dirty",!0)}function r(e,t){return p.i((0!==p.u()?.35:1)*e,(0!==p.H()?.35:1)*t)}function i(){if(1===g.ratio){var e=Math.round(1e4*p.u())/1e4;0!==e&&(b.Id=e,u=d.K.jc(b).fa({duration:500,P:{x:{start:e,end:0,easing:m.Gb}},ba:function(){p.i(b.x-b.Id,0),n(1),b.Id=b.x}}).start())}}function o(){if(1===g.ratio){var e=Math.round(1e4*p.H())/1e4;0!==e&&(v.Jd=e,c=d.K.jc(v).fa({duration:500,P:{y:{start:e,end:0,easing:m.Gb}},ba:function(){p.i(0,v.y-v.Jd),n(1),v.Jd=v.y}}).start())}}function a(e,t,r){return e?d.K.jc(g).fa({duration:void 0===t?700:t,P:{ratio:{start:0,end:1,easing:r||m.Uf}},ba:function(){n(g.ratio)}}).Ta():(new h).resolve().promise()}function s(e){return function(){return C?(new h).resolve().promise():e.apply(this,arguments)}}var u,c,f,d,p=new l(e),g={ratio:1},b={ke:0,x:0,Id:0},v={le:0,y:0,Jd:0},y=this,C=!1;e.j.subscribe("model:loaded",(function(e){f=e,p.reset(!1),p.update(1)})),e.j.subscribe("timeline:initialized",(function(e){d=e})),this.M=function(){e.j.D("zoom:initialized",this)},this.reset=function(e,n){return p.Fb(1),t(!0,e,n)},this.normalize=s((function(e,n){p.pc(1)?t(!1,e,n):y.$e()})),this.$e=function(){i(),o()},this.bg=s((function(e,t,n,r){return y.ic(e.F,t,n,r)})),this.Nb=s((function(e,t,n,r){return a(p.Nb(e,t),n,r)})),this.ic=s((function(e,t,n,r){return a(p.ic(e,t),n,r)})),this.cj=s((function(e,t){p.ic(e,t)&&n(1)})),this.Uh=s((function(e,t){1===g.ratio&&r(e,t)&&n(1)})),this.qg=s((function(e,t){p.Nb(e,t)&&n(1)})),this.pg=s((function(e,t,i,o){e=0|p.Nb(e,t),(e|=r(i,o))&&n(1)})),this.Vh=s((function(e,t,a){1===g.ratio&&(u=d.K.jc(b).fa({duration:e/.03,P:{ke:{start:t,end:0,easing:m.Gb}},ba:function(){p.i(b.ke,0)&&n(1),i()}}).start(),c=d.K.jc(v).fa({duration:e/.03,P:{le:{start:a,end:0,easing:m.Gb}},ba:function(){r(0,v.le)&&n(1),o()}}).start())})),this.Wh=function(){u&&0===p.u()&&u.stop(),c&&0===p.H()&&c.stop()},this.rc=function(e,t){p.rc(e,t)},this.Fb=function(e){return p.Fb(e)},this.pc=function(e){return p.pc(e)},this.zd=function(){return p.zd()},this.absolute=function(e,t){return p.absolute(e,t)},this.Uc=function(e,t){return p.Uc(e,t)},this.scale=function(){return p.scale()},this.i=function(e){return p.T(e)},this.content=function(e,t,n,r){p.content(e,t,n,r)}}function ie(t,i,o){function a(e){var t=[];return S.L(v,(function(n){e(n)&&t.push(n.group)})),{groups:t}}function s(e,t){var n=_.options,r=n.Mi,i=n.Li;n=n.Od;var o=0<r+i?n:0,a=[];return D.u(e,D.i(e,_.options.Qd),(function(e,n,s){n="groups"===_.options.Pd?s:n,e.m&&(e=T.K.A(e).wait(o*(i+r*n)).call(t).done(),a.push(e))})),T.K.A({}).Qa(a).Ta()}function l(e){ce||(ce=!0,x.once((function(){ce=!1,_.j.D("repaint:before"),U.Nd(this.og)}),{og:e}))}function c(e){function t(e,i){var o=e.W;if(e.W=i<=n,e.Fc=i<=r,e.W!==o&&S.me(e,(function(e){e.Vd=!0})),e.open||e.Va||i++,e=e.m)for(o=0;o<e.length;o++)t(e[o],i)}var n=_.options.We,r=Math.min(_.options.We,_.options.Ph);if(e)for(var i=0;i<e.length;i++){var o=e[i];t(o,b(o))}else t(v,0)}function p(e,t){var n=[];for((e=g(e,t)).Th&&_.j.D("model:childrenAttached",S.uc(v)),e.Gi&&I.complete((function(e){ue.eb(e),n.push(e)})),t=e=0;t<n.length;t++){var r=n[t];r.m&&(e+=r.m.length),r.xa=!0,W.i(r)}return e}function g(e,t){function n(e,t){var n=!e.attribution&&t-(e.Va?1:0)<o;s=s||n,e.Ia=e.Ia||n,e.open||e.Va||t++;var i=e.m;if(!i&&n&&(a=E.T(e)||a,i=e.m,u&&(e.Ma=!0)),i)for(e=0;e<i.length;e++)r.push(i[e],t)}var r,o=t||_.options.Qh,a=!1,s=!1,u="flattened"===i.mb;for(r=e?e.reduce((function(e,t){return e.push(t,1),e}),[]):[v,1];0<r.length;)n(r.shift(),r.shift());return{Th:a,Gi:s}}function b(e){for(var t=0;e.parent;)e.open||e.Va||t++,e=e.parent;return t}var v,C=this,_={j:new w,options:i,Cd:o},x=new r,T=new A(x),k=n.create(),z=new u(_),j=new re(_),L=new B(_),E=new O(_.options),I=new H(_),U=new J(_,x),V=new G(_);new ne(_);var q=new N(_),W=new P(_),Z=new F(_),$=new R(_);_.j.subscribe("stage:initialized",(function(e,t,n,r){ie.Le(n,r)})),_.j.subscribe("stage:resized",(function(e,t,n,r){ie.Ki(e,t,n,r)})),_.j.subscribe("foamtree:attachChildren",p),_.j.subscribe("openclose:changing",c),_.j.subscribe("interaction:reset",(function(){le(!0)})),_.j.subscribe("foamtree:dirty",l),this.M=function(){_.j.D("timeline:initialized",T),v=E.M(),z.M(t),L.M(),U.M(),V.M(),q.M(),W.M(),j.M(),Z.M(),$.M()},this.Za=function(){T.i(),se.stop(),x.i(),z.Za()};var K="groupLabelFontFamily groupLabelFontStyle groupLabelFontVariant groupLabelFontWeight groupLabelLineHeight groupLabelHorizontalPadding groupLabelVerticalPadding groupLabelDottingThreshold groupLabelMaxTotalHeight groupLabelMinFontSize groupLabelMaxFontSize groupLabelDecorator".split(" "),Y="rainbowColorDistribution rainbowLightnessDistribution rainbowColorDistributionAngle rainbowLightnessDistributionAngle rainbowColorModelStartPoint rainbowLightnessCorrection rainbowSaturationCorrection rainbowStartColor rainbowEndColor rainbowHueShift rainbowHueShiftCenter rainbowSaturationShift rainbowSaturationShiftCenter rainbowLightnessShift rainbowLightnessShiftCenter attributionTheme".split(" "),X=!1,Q=["groupBorderRadius","groupBorderRadiusCorrection","groupBorderWidth","groupInsetWidth","groupBorderWidthScaling"],ee=["maxGroupLevelsDrawn","maxGroupLabelLevelsDrawn"];this.hg=function(e){_.j.D("options:changed",e),y.bb(e,K)&&S.L(v,(function(e){e.Ma=!0})),y.bb(e,Y)&&(v.Fa=!0),y.bb(e,Q)&&(X=!0),y.bb(e,ee)&&(c(),p())},this.reload=function(){oe.reload()},this.ig=function(e,t){y.defer((function(){if(X)ie.Nh(e),X=!1;else{if(t)for(var n=E.u(t),r=n.length-1;0<=r;r--)n[r].N=!0;else v.N=!0;l(e)}}))},this.ga=function(){z.u()},this.update=function(e){var t=(e=e?E.u(e):[v]).reduce((function(e,t){return e[t.id]=t,e}),{});e=e.filter((function(e){for(e=e.parent;e;){if(y.has(t,e.id))return!1;e=e.parent}return!0})),E.update(e),ie.dj(e)},this.reset=function(){return le(!1)},this.T=U.i,this.Ja=function(){var e={};return function(t,n){return(t=E.i(t))?L.Wc(e,t,n):null}}(),this.wa=function(){var e={x:0,y:0},t={x:0,y:0};return function(n,r){return(n=E.i(n))?(e.x=r.x,e.y=r.y,n.transformPoint(e,e),j.Uc(e,e),t.x=e.x,t.y=e.y,t):null}}(),this.sa=function(){var e={};return function(t){return(t=E.i(t))?L.Yc(e,t):null}}(),this.gg=function(){var e={};return function(t){return(t=E.i(t))?L.Xc(e,t):null}}(),this.ta=function(){var e={};return function(){return j.i(e)}}(),this.kg=function(){this.H({groups:a((function(e){return e.group.selected})),newState:!0,keepPrevious:!1}),this.u({groups:a((function(e){return e.group.open})),newState:!0,keepPrevious:!1}),this.i({groups:a((function(e){return e.group.exposed})),newState:!0,keepPrevious:!1})},this.Ka=function(){return a((function(e){return e.U}))},this.i=function(e){return oe.submit((function(){return q.Vb(E.H(e,"exposed",!1),!1,!0,!1)}))},this.pb=function(){return a((function(e){return e.open}))},this.u=function(e){return oe.submit((function(){return Z.Bb(E.H(e,"open",!0),!1,!1)}))},this.Lb=function(){return a((function(e){return e.selected}))},this.H=function(e){return oe.submit((function(){return $.select(E.H(e,"selected",!0),!1),(new h).resolve().promise()}))},this.mg=function(e){return(e=E.i(e))?e===v?j.reset(i.ob,m.ia(i.Kb)):j.bg(e,i.Yb,i.ob,m.ia(i.Kb)):(new h).resolve().promise()},this.ua=function(e,t){return(e=E.u(e))?(t=p(e,t),c(e),t):0},this.Vc=function(e){return V.Lb[e]},this.lg=function(){var t=e;return{frames:t.frames,totalTime:t.totalTime,lastFrameTime:t.rd,lastInterFrameTime:t.sd,fps:t.ue}};var te,ie=function(){function e(e,o){var a=e||n,s=o||r;n=a,r=s,(e=i.Rb&&i.Rb.boundary)&&2<e.length?v.C=e.map((function(e){return{x:a*e.x,y:s*e.y}})):v.C=[{x:0,y:0},{x:a,y:0},{x:a,y:s},{x:0,y:s}],t()}function t(){v.Z=!0,v.G=v.C,v.F=M.F(v.C,v.F),v.O=v,M.Ja(v.C,v.O)}var n,r;return{Le:e,Ki:function(t,n,r,i){ue.stop();var o=r/t,a=i/n;S.ne(v,(function(e){e.x=e.x*o+(Math.random()-.5)*r/1e3,e.y=e.y*a+(Math.random()-.5)*i/1e3})),e(r,i),v.Ea=!0,I.step(ue.eb,!0,!1,(function(e){var t=e.m;if(t){I.Eb(e);for(var n=t.length-1;0<=n;n--){var r=t[n];r.w=r.hc}e.Ea=!0}}))?l(!1):(I.fc(v),_.options.Md?(l(!1),se.Jf(),se.Oc()):(I.complete(ue.eb),v.Fa=!0,l(!1)))},Nh:function(e){var n=!1;return v.empty()||(t(),se.xb()||(n=I.step(ue.eb,!1,!1),l(e))),n},dj:function(e){e.forEach((function(e){S.za(e,(function(e){e.empty()||I.Eb(e)})),I.fc(e),_.options.Md?(se.Jf(),S.za(e,(function(e){e.empty()||ue.grow(e)}))):(S.za(e,(function(e){e.empty()||ue.eb(e)})),I.complete(ue.eb),e.Fa=!0,l(!1))}))}}}(),oe=function(){function e(){if(0===i.Gd&&j.reset(0),_.options.zf(i.Rb),ie.Le(),E.load(i.Rb),g(),c(),_.j.D("model:loaded",v,S.uc(v)),!v.empty()){if(v.open=!0,v.Ia=!0,i.Md)var e=se.Oc();else se.Yh(),e=function(){S.za(v,(function(e){e.xa=!1}));var e=new h,t=new d(e.resolve);return t.i(),v.xa=!0,W.i(v).then(t.u),s(v,(function e(){this.R&&this.C&&(this.Z=this.xa=!0,t.i(),W.i(this).then(t.u),t.i(),s(this,e).then(t.u))})),e.promise()}();!function(){var e=i.Oa,t=i.Kc;i.Oa=0,i.Kc=0,C.kg(),i.Oa=e,i.Kc=t}(),0<i.Od?(U.clear(),z.i(1)):e=f([e,t(1)])}_.options.yf(i.Rb),e&&(_.options.Cf(),e.then((function(){U.u((function(){x.once(_.options.Bf)}))})))}function t(e,t){return 0===i.qe||t?(z.i(e),(new h).resolve().promise()):T.K.A({opacity:z.i()}).Xd(2).fa({duration:i.qe,P:{opacity:{end:e,easing:m.ia(i.Cg)}},ba:function(){z.i(this.opacity)}}).Ta()}function n(){for(var e=0;e<o.length;e++){var t=o[e],n=t.action();y.has(n,"then")?n.then(t.ge.resolve):t.ge.resolve()}o=[]}var r=!1,o=[];return{reload:function(){r||(v.empty()?e():(ue.stop(),T.i(),se.stop(),r=!0,f(0<i.Gd?[W.u(),le(!1)]:[t(0)]).then((function(){t(0,!0),r=!1,e(),y.defer(n)}))))},submit:function(e){if(r){var t=new h;return o.push({action:e,ge:t}),t.promise()}return e()}}}(),ae=new d((function(){te.resolve()})),se=function(){function e(){return o||(ae.initial()&&(te=new h),ae.i(),t(),o=!0,x.repeat(n)),te.promise()}function t(){r=k.now()}function n(){var t=k.now()-r>i.Ji;return t=I.step((function(t){t.xa=!0,ue.grow(t),ae.i(),W.i(t).then(ae.u),ae.i(),s(t,(function(){this.Ia=!0,e()})).then(ae.u)}),!0,t)||t,l(!0),t&&(o=!1,ae.u()),t}var r,o=!1;return{Yh:function(){I.complete(ue.eb)},Oc:e,Jf:t,xb:function(){return!ae.initial()},stop:function(){x.cancel(n),o=!1,ae.clear()}}}(),ue=function(){function e(e){var t=!e.empty();if(e.xa=!0,t){for(var n=e.m,r=n.length-1;0<=r;r--){var i=n[r];i.w=i.hc}e.Ea=!0}return t}var t=[];return{grow:function(n){var r=_.options,i=r.Wg;0<i?D.u(n,D.i(n,_.options.Qd),(function(e,n,o){n="groups"===_.options.Pd?o:n,ae.i(),t.push(T.K.A(e).wait(n*r.Vg*i).fa({duration:i,P:{w:{start:e.Vf,end:e.hc,easing:m.ia(r.Xg)}},ba:function(){this.w=Math.max(0,this.w),this.parent.Ea=!0,se.Oc()}}).Xa(ae.u).start())})):e(n)&&se.Oc()},eb:e,stop:function(){for(var e=t.length-1;0<=e;e--)t[e].stop();t=[]}}}(),le=function(){var e=!1;return function(t){if(e)return(new h).resolve().promise();e=!0;var n=[];n.push(j.reset(i.ob,m.ia(i.Kb)));var r=new h;return q.Vb({m:[],Ca:!1,Ba:!1},t,!1,!0).then((function(){Z.Bb({m:[],Ca:!1,Ba:!1},t,!1).then(r.resolve)})),n.push(r.promise()),f(n).then((function(){e=!1,t&&i.Df()}))}}(),ce=!1}function oe(){return{version:"3.5.0",build:"bugfix/3.5.x/e3b91c8e",brandingAllowed:!1}}Y.i={width:445.2,height:533.5},t.md((function(){window.CarrotSearchFoamTree=function(e){function t(e,t){if(!s||s.exists(e))switch(e){case"selection":return h.Lb();case"open":return h.pb();case"exposure":return h.Ka();case"state":return h.sa.apply(this,t);case"geometry":return h.Ja.apply(this,t);case"hierarchy":return h.gg.apply(this,t);case"containerCoordinates":return h.wa.apply(this,t);case"imageData":return h.T.apply(this,t);case"viewport":return h.ta();case"times":return h.lg();case"onModelChanged":case"onRedraw":case"onRolloutStart":case"onRolloutComplete":case"onRelaxationStep":case"onGroupHover":case"onGroupOpenOrCloseChanging":case"onGroupExposureChanging":case"onGroupSelectionChanging":case"onGroupSelectionChanged":case"onGroupClick":case"onGroupDoubleClick":case"onGroupHold":return e=u[e],Array.isArray(e)?e:[e];default:return u[e]}}function n(e){function t(e,t){return y.has(n,e)?(t(n[e]),delete n[e],1):0}if(0===arguments.length)return 0;if(1===arguments.length)var n=y.extend({},arguments[0]);else 2===arguments.length&&((n={})[arguments[0]]=arguments[1]);s&&s.validate(n,l.Oh);var r=0;h&&(r+=t("selection",h.H),r+=t("open",h.u),r+=t("exposure",h.i));var o={};return y.Aa(n,(function(e,t){(u[t]!==e||y.wb(e))&&(o[t]=e,r++),u[t]=e})),0<r&&i(o),r}function r(e,t){e="on"+e.charAt(0).toUpperCase()+e.slice(1);var n=u[e];u[e]=t(Array.isArray(n)?n:[n]),(t={})[e]=u[e],i(t)}function i(e){!function(){function t(t,n){return y.has(e,t)||void 0===n?_(u[t],a):n}l.Oh=u.logging,l.Rb=u.dataObject,l.B=u.pixelRatio,l.nb=u.wireframePixelRatio,l.mb=u.stacking,l.zg=u.descriptionGroup,l.Tb=u.descriptionGroupType,l.qc=u.descriptionGroupPosition,l.Ag=u.descriptionGroupDistanceFromCenter,l.Sb=u.descriptionGroupSize,l.ie=u.descriptionGroupMinHeight,l.he=u.descriptionGroupMaxHeight,l.je=u.descriptionGroupPolygonDrawn,l.Dc=u.layout,l.ac=u.layoutByWeightOrder,l.Wi=u.showZeroWeightGroups,l.Be=u.groupMinDiameter,l.Ld=u.rectangleAspectRatioPreference,l.Ii=u.initializer||u.relaxationInitializer,l.Ji=u.relaxationMaxDuration,l.Md=u.relaxationVisible,l.Hf=u.relaxationQualityThreshold,l.oh=u.groupResizingBudget,l.Wg=u.groupGrowingDuration,l.Vg=u.groupGrowingDrag,l.Xg=u.groupGrowingEasing,l.Gg=u.groupBorderRadius,l.$a=u.groupBorderWidth,l.La=u.groupBorderWidthScaling,l.jd=u.groupInsetWidth,l.Hg=u.groupBorderRadiusCorrection,l.ab=u.groupStrokeWidth,l.yc=u.groupSelectionOutlineWidth,l.sh=u.groupSelectionOutlineColor,l.kd=u.groupSelectionOutlineShadowSize,l.Ce=u.groupSelectionOutlineShadowColor,l.ph=u.groupSelectionFillHueShift,l.rh=u.groupSelectionFillSaturationShift,l.qh=u.groupSelectionFillLightnessShift,l.Ee=u.groupSelectionStrokeHueShift,l.Ge=u.groupSelectionStrokeSaturationShift,l.Fe=u.groupSelectionStrokeLightnessShift,l.Ug=u.groupFillType,l.Qg=u.groupFillGradientRadius,l.Ng=u.groupFillGradientCenterHueShift,l.Pg=u.groupFillGradientCenterSaturationShift,l.Og=u.groupFillGradientCenterLightnessShift,l.Rg=u.groupFillGradientRimHueShift,l.Tg=u.groupFillGradientRimSaturationShift,l.Sg=u.groupFillGradientRimLightnessShift,l.ld=u.groupStrokeType,l.ab=u.groupStrokeWidth,l.He=u.groupStrokePlainHueShift,l.Je=u.groupStrokePlainSaturationShift,l.Ie=u.groupStrokePlainLightnessShift,l.xh=u.groupStrokeGradientRadius,l.th=u.groupStrokeGradientAngle,l.yh=u.groupStrokeGradientUpperHueShift,l.Ah=u.groupStrokeGradientUpperSaturationShift,l.zh=u.groupStrokeGradientUpperLightnessShift,l.uh=u.groupStrokeGradientLowerHueShift,l.wh=u.groupStrokeGradientLowerSaturationShift,l.vh=u.groupStrokeGradientLowerLightnessShift,l.Yg=u.groupHoverFillHueShift,l.$g=u.groupHoverFillSaturationShift,l.Zg=u.groupHoverFillLightnessShift,l.ye=u.groupHoverStrokeHueShift,l.Ae=u.groupHoverStrokeSaturationShift,l.ze=u.groupHoverStrokeLightnessShift,l.Pa=u.groupExposureScale,l.Mg=u.groupExposureShadowColor,l.xe=u.groupExposureShadowSize,l.Yb=u.groupExposureZoomMargin,l.Ch=u.groupUnexposureLightnessShift,l.Dh=u.groupUnexposureSaturationShift,l.Bh=u.groupUnexposureLabelColorThreshold,l.Oa=u.exposeDuration,l.Wb=u.exposeEasing,l.Kc=u.openCloseDuration,l.Ig=_(u.groupColorDecorator,a),l.Jg=u.groupColorDecorator!==y.qa,l.dh=_(u.groupLabelDecorator,a),l.eh=u.groupLabelDecorator!==y.qa,l.jh=_(u.groupLabelLayoutDecorator,a),l.kh=u.groupLabelLayoutDecorator!==y.qa,l.Kg=_(u.groupContentDecorator,a),l.xc=u.groupContentDecorator!==y.qa,l.Lg=u.groupContentDecoratorTriggering,l.Ei=u.rainbowStartColor,l.xi=u.rainbowEndColor,l.vi=u.rainbowColorDistribution,l.wi=u.rainbowColorDistributionAngle,l.Ai=u.rainbowLightnessDistributionAngle,l.Bi=u.rainbowLightnessShift,l.Ci=u.rainbowLightnessShiftCenter,l.Di=u.rainbowSaturationCorrection,l.zi=u.rainbowLightnessCorrection,l.Ef=u.parentFillOpacity,l.Xh=u.parentStrokeOpacity,l.Ff=u.parentLabelOpacity,l.Gf=u.parentOpacityBalancing,l.nh=u.groupLabelUpdateThreshold,l.fh=u.groupLabelFontFamily,l.gh=u.groupLabelFontStyle,l.hh=u.groupLabelFontVariant,l.ih=u.groupLabelFontWeight,l.mh=u.groupLabelMinFontSize,l.sj=u.groupLabelMaxFontSize,l.rj=u.groupLabelLineHeight,l.qj=u.groupLabelHorizontalPadding,l.uj=u.groupLabelVerticalPadding,l.tj=u.groupLabelMaxTotalHeight,l.bh=u.groupLabelDarkColor,l.lh=u.groupLabelLightColor,l.ah=u.groupLabelColorThreshold,l.fj=u.wireframeDrawMaxDuration,l.gj=u.wireframeLabelDrawing,l.ej=u.wireframeContentDecorationDrawing,l.dg=u.wireframeToFinalFadeDuration,l.hj=u.wireframeToFinalFadeDelay,l.Dg=u.finalCompleteDrawMaxDuration,l.Eg=u.finalIncrementalDrawMaxDuration,l.se=u.finalToWireframeFadeDuration,l.Zc=u.androidStockBrowserWorkaround,l.Ke=u.incrementalDraw,l.Rh=u.maxGroups,l.Qh=u.maxGroupLevelsAttached,l.We=u.maxGroupLevelsDrawn,l.Ph=u.maxGroupLabelLevelsDrawn,l.Qd=u.rolloutStartPoint,l.Pd=u.rolloutMethod,l.Ni=u.rolloutEasing,l.Od=u.rolloutDuration,l.Lf=u.rolloutScalingStrength,l.Nf=u.rolloutTranslationXStrength,l.Of=u.rolloutTranslationYStrength,l.Kf=u.rolloutRotationStrength,l.Mf=u.rolloutTransformationCenter,l.Ri=u.rolloutPolygonDrag,l.Si=u.rolloutPolygonDuration,l.Oi=u.rolloutLabelDelay,l.Pi=u.rolloutLabelDrag,l.Qi=u.rolloutLabelDuration,l.Mi=u.rolloutChildGroupsDrag,l.Li=u.rolloutChildGroupsDelay,l.ni=u.pullbackStartPoint,l.hi=u.pullbackMethod,l.di=u.pullbackEasing,l.yj=u.pullbackType,l.Gd=u.pullbackDuration,l.mi=u.pullbackScalingStrength,l.pi=u.pullbackTranslationXStrength,l.ri=u.pullbackTranslationYStrength,l.li=u.pullbackRotationStrength,l.oi=u.pullbackTransformationCenter,l.ii=u.pullbackPolygonDelay,l.ji=u.pullbackPolygonDrag,l.ki=u.pullbackPolygonDuration,l.ei=u.pullbackLabelDelay,l.fi=u.pullbackLabelDrag,l.gi=u.pullbackLabelDuration,l.ai=u.pullbackChildGroupsDelay,l.bi=u.pullbackChildGroupsDrag,l.ci=u.pullbackChildGroupsDuration,l.qe=u.fadeDuration,l.Cg=u.fadeEasing,l.ij=u.zoomMouseWheelFactor,l.ob=u.zoomMouseWheelDuration,l.Kb=u.zoomMouseWheelEasing,l.Sh=u.maxLabelSizeForTitleBar,l.Zi=u.titleBarFontFamily,l.Yf=u.titleBarBackgroundColor,l.Zf=u.titleBarTextColor,l.$i=u.titleBarMinFontSize,l.Wd=u.titleBarMaxFontSize,l.aj=u.titleBarTextPaddingLeftRight,l.bj=u.titleBarTextPaddingTopBottom,l.Yi=u.titleBarDecorator,l.mj=u.attributionText,l.jj=u.attributionLogo,l.lj=u.attributionLogoScale,l.nj=u.attributionUrl,l.$d=u.attributionPosition,l.rg=u.attributionDistanceFromCenter,l.sg=u.attributionWeight,l.ae=u.attributionTheme,l.Me=u.interactionHandler,l.zf=t("onModelChanging",l.zf),l.yf=t("onModelChanged",l.yf),l.Af=t("onRedraw",l.Af),l.Cf=t("onRolloutStart",l.Cf),l.Bf=t("onRolloutComplete",l.Bf),l.Ad=t("onRelaxationStep",l.Ad),l.Df=t("onViewReset",l.Df),l.rf=t("onGroupOpenOrCloseChanging",l.rf),l.qf=t("onGroupOpenOrCloseChanged",l.qf),l.hf=t("onGroupExposureChanging",l.hf),l.gf=t("onGroupExposureChanged",l.gf),l.tf=t("onGroupSelectionChanging",l.tf),l.sf=t("onGroupSelectionChanged",l.sf),l.kf=t("onGroupHover",l.kf),l.mf=t("onGroupMouseMove",l.mf),l.bf=t("onGroupClick",l.bf),l.cf=t("onGroupDoubleClick",l.cf),l.jf=t("onGroupHold",l.jf),l.pf=t("onGroupMouseWheel",l.pf),l.nf=t("onGroupMouseUp",l.nf),l.lf=t("onGroupMouseDown",l.lf),l.ff=t("onGroupDragStart",l.ff),l.df=t("onGroupDrag",l.df),l.ef=t("onGroupDragEnd",l.ef),l.wf=t("onGroupTransformStart",l.wf),l.uf=t("onGroupTransform",l.uf),l.vf=t("onGroupTransformEnd",l.vf),l.xf=t("onKeyUp",l.xf)}(),l.Fi=c.u(l.Ei),l.yi=c.u(l.xi),l.De=c.u(l.Ce),l.kj=null,h&&(h.hg(e),y.has(e,"dataObject")&&h.reload())}function o(e){return function(){return e.apply(this,arguments).Fg(a)}}var a=this,s=window.CarrotSearchFoamTree.asserts,u=y.extend({},window.CarrotSearchFoamTree.defaults),l={};n(e),(e=u.element||document.getElementById(u.id))||E.i("Element to embed FoamTree in not found."),u.element=e;var h=new ie(e,l,u);h.M();var f={get:function(e){return 0===arguments.length?y.extend({},u):t(arguments[0],Array.prototype.slice.call(arguments,1))},set:n,on:function(e,t){r(e,(function(e){return e.push(t),e}))},off:function(e,t){r(e,(function(e){return e.filter((function(e){return e!==t}))}))},resize:h.ga,redraw:h.ig,update:h.update,attach:h.ua,select:o(h.H),expose:o(h.i),open:o(h.u),reset:o(h.reset),zoom:o(h.mg),trigger:function(e,t){(e=h.Vc(e))&&e(t)},dispose:function(){function e(){throw"FoamTree instance disposed"}h.Za(),y.Aa(f,(function(t,n){"dispose"!==n&&(a[n]=e)}))}};y.Aa(f,(function(e,t){a[t]=e})),h.reload()},window["CarrotSearchFoamTree.asserts"]&&(window.CarrotSearchFoamTree.asserts=window["CarrotSearchFoamTree.asserts"],delete window["CarrotSearchFoamTree.asserts"]),window.CarrotSearchFoamTree.supported=!0,window.CarrotSearchFoamTree.version=oe,window.CarrotSearchFoamTree.defaults=Object.freeze({id:void 0,element:void 0,logging:!1,dataObject:void 0,pixelRatio:1,wireframePixelRatio:1,layout:"relaxed",layoutByWeightOrder:!0,showZeroWeightGroups:!0,groupMinDiameter:10,rectangleAspectRatioPreference:-1,relaxationInitializer:"fisheye",relaxationMaxDuration:3e3,relaxationVisible:!1,relaxationQualityThreshold:1,stacking:"hierarchical",descriptionGroup:"auto",descriptionGroupType:"stab",descriptionGroupPosition:225,descriptionGroupDistanceFromCenter:1,descriptionGroupSize:.125,descriptionGroupMinHeight:35,descriptionGroupMaxHeight:.5,descriptionGroupPolygonDrawn:!1,maxGroups:5e4,maxGroupLevelsAttached:4,maxGroupLevelsDrawn:4,maxGroupLabelLevelsDrawn:3,groupGrowingDuration:0,groupGrowingEasing:"bounce",groupGrowingDrag:0,groupResizingBudget:2,groupBorderRadius:.15,groupBorderWidth:4,groupBorderWidthScaling:.6,groupInsetWidth:6,groupBorderRadiusCorrection:1,groupSelectionOutlineWidth:5,groupSelectionOutlineColor:"#222",groupSelectionOutlineShadowSize:0,groupSelectionOutlineShadowColor:"#fff",groupSelectionFillHueShift:0,groupSelectionFillSaturationShift:0,groupSelectionFillLightnessShift:0,groupSelectionStrokeHueShift:0,groupSelectionStrokeSaturationShift:0,groupSelectionStrokeLightnessShift:-10,groupFillType:"gradient",groupFillGradientRadius:1,groupFillGradientCenterHueShift:0,groupFillGradientCenterSaturationShift:0,groupFillGradientCenterLightnessShift:20,groupFillGradientRimHueShift:0,groupFillGradientRimSaturationShift:0,groupFillGradientRimLightnessShift:-5,groupStrokeType:"plain",groupStrokeWidth:1.5,groupStrokePlainHueShift:0,groupStrokePlainSaturationShift:0,groupStrokePlainLightnessShift:-10,groupStrokeGradientRadius:1,groupStrokeGradientAngle:45,groupStrokeGradientUpperHueShift:0,groupStrokeGradientUpperSaturationShift:0,groupStrokeGradientUpperLightnessShift:20,groupStrokeGradientLowerHueShift:0,groupStrokeGradientLowerSaturationShift:0,groupStrokeGradientLowerLightnessShift:-20,groupHoverFillHueShift:0,groupHoverFillSaturationShift:0,groupHoverFillLightnessShift:20,groupHoverStrokeHueShift:0,groupHoverStrokeSaturationShift:0,groupHoverStrokeLightnessShift:-10,groupExposureScale:1.15,groupExposureShadowColor:"rgba(0, 0, 0, 0.5)",groupExposureShadowSize:50,groupExposureZoomMargin:.1,groupUnexposureLightnessShift:65,groupUnexposureSaturationShift:-65,groupUnexposureLabelColorThreshold:.35,exposeDuration:700,exposeEasing:"squareInOut",groupColorDecorator:y.qa,groupLabelDecorator:y.qa,groupLabelLayoutDecorator:y.qa,groupContentDecorator:y.qa,groupContentDecoratorTriggering:"onLayoutDirty",openCloseDuration:500,rainbowColorDistribution:"radial",rainbowColorDistributionAngle:-45,rainbowLightnessDistributionAngle:45,rainbowSaturationCorrection:.1,rainbowLightnessCorrection:.4,rainbowStartColor:"hsla(0, 100%, 55%, 1)",rainbowEndColor:"hsla(359, 100%, 55%, 1)",rainbowLightnessShift:30,rainbowLightnessShiftCenter:.4,parentFillOpacity:.7,parentStrokeOpacity:1,parentLabelOpacity:1,parentOpacityBalancing:!0,wireframeDrawMaxDuration:15,wireframeLabelDrawing:"auto",wireframeContentDecorationDrawing:"auto",wireframeToFinalFadeDuration:500,wireframeToFinalFadeDelay:300,finalCompleteDrawMaxDuration:80,finalIncrementalDrawMaxDuration:100,finalToWireframeFadeDuration:200,androidStockBrowserWorkaround:!1,incrementalDraw:"fast",groupLabelFontFamily:"sans-serif",groupLabelFontStyle:"normal",groupLabelFontWeight:"normal",groupLabelFontVariant:"normal",groupLabelLineHeight:1.05,groupLabelHorizontalPadding:1,groupLabelVerticalPadding:1,groupLabelMinFontSize:6,groupLabelMaxFontSize:160,groupLabelMaxTotalHeight:.9,groupLabelUpdateThreshold:.05,groupLabelDarkColor:"#000",groupLabelLightColor:"#fff",groupLabelColorThreshold:.35,rolloutStartPoint:"center",rolloutEasing:"squareOut",rolloutMethod:"groups",rolloutDuration:2e3,rolloutScalingStrength:-.7,rolloutTranslationXStrength:0,rolloutTranslationYStrength:0,rolloutRotationStrength:-.7,rolloutTransformationCenter:.7,rolloutPolygonDrag:.1,rolloutPolygonDuration:.5,rolloutLabelDelay:.8,rolloutLabelDrag:.1,rolloutLabelDuration:.5,rolloutChildGroupsDrag:.1,rolloutChildGroupsDelay:.2,pullbackStartPoint:"center",pullbackEasing:"squareIn",pullbackMethod:"groups",pullbackDuration:1500,pullbackScalingStrength:-.7,pullbackTranslationXStrength:0,pullbackTranslationYStrength:0,pullbackRotationStrength:-.7,pullbackTransformationCenter:.7,pullbackPolygonDelay:.3,pullbackPolygonDrag:.1,pullbackPolygonDuration:.8,pullbackLabelDelay:0,pullbackLabelDrag:.1,pullbackLabelDuration:.3,pullbackChildGroupsDelay:.1,pullbackChildGroupsDrag:.1,pullbackChildGroupsDuration:.3,fadeDuration:700,fadeEasing:"cubicInOut",zoomMouseWheelFactor:1.5,zoomMouseWheelDuration:500,zoomMouseWheelEasing:"squareOut",maxLabelSizeForTitleBar:8,titleBarFontFamily:null,titleBarFontStyle:"normal",titleBarFontWeight:"normal",titleBarFontVariant:"normal",titleBarBackgroundColor:"rgba(0, 0, 0, 0.5)",titleBarTextColor:"rgba(255, 255, 255, 1)",titleBarMinFontSize:10,titleBarMaxFontSize:40,titleBarTextPaddingLeftRight:20,titleBarTextPaddingTopBottom:15,titleBarDecorator:y.qa,attributionText:null,attributionLogo:null,attributionLogoScale:.5,attributionUrl:"http://carrotsearch.com/foamtree",attributionPosition:"bottomright",attributionDistanceFromCenter:1,attributionWeight:.025,attributionTheme:"light",interactionHandler:t.Gh()?"hammerjs":"builtin",onModelChanging:[],onModelChanged:[],onRedraw:[],onRolloutStart:[],onRolloutComplete:[],onRelaxationStep:[],onViewReset:[],onGroupOpenOrCloseChanging:[],onGroupOpenOrCloseChanged:[],onGroupExposureChanging:[],onGroupExposureChanged:[],onGroupSelectionChanging:[],onGroupSelectionChanged:[],onGroupHover:[],onGroupMouseMove:[],onGroupClick:[],onGroupDoubleClick:[],onGroupHold:[],onGroupMouseWheel:[],onGroupMouseUp:[],onGroupMouseDown:[],onGroupDragStart:[],onGroupDrag:[],onGroupDragEnd:[],onGroupTransformStart:[],onGroupTransform:[],onGroupTransformEnd:[],onKeyUp:[],selection:null,open:null,exposure:null,imageData:null,hierarchy:null,geometry:null,containerCoordinates:null,state:null,viewport:null,times:null}),window.CarrotSearchFoamTree.geometry=Object.freeze({rectangleInPolygon:function(e,t,n,r,i,o,a){return i=y.I(i,1),o=y.I(o,.5),a=y.I(a,.5),{x:t-(e=M.Ka(e,{x:t,y:n},r,o,a)*i)*r*o,y:n-e*a,w:e*r,h:e}},circleInPolygon:function(e,t,n){return M.pb(e,{x:t,y:n})},stabPolygon:function(e,t,n,r){return M.ua(e,{x:t,y:n},r)},polygonCentroid:function(e){return{x:(e=M.u(e,{})).x,y:e.y,area:e.ha}},boundingBox:function(e){for(var t=e[0].x,n=e[0].y,r=e[0].x,i=e[0].y,o=1;o<e.length;o++){var a=e[o];a.x<t&&(t=a.x),a.y<n&&(n=a.y),a.x>r&&(r=a.x),a.y>i&&(i=a.y)}return{x:t,y:n,w:r-t,h:i-n}}})}),(function(){window.CarrotSearchFoamTree=function(){window.console.error("FoamTree is not supported on this browser.")},window.CarrotSearchFoamTree.supported=!1}))}();const Pi=window.CarrotSearchFoamTree;function Fi(t,n,r,i,o){var a={};for(var s in n)"ref"!=s&&(a[s]=n[s]);var u,l,c={type:t,props:a,key:r,ref:n&&n.ref,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:++e.__v,__source:i,__self:o};if("function"==typeof t&&(u=t.defaultProps))for(l in u)void 0===a[l]&&(a[l]=u[l]);return e.vnode&&e.vnode(c),c}class Ri extends p{constructor(e){super(e),this.saveNodeRef=e=>this.node=e,this.resize=()=>{const{props:e}=this;this.treemap.resize(),e.onResize&&e.onResize()},this.treemap=null,this.zoomOutDisabled=!1}componentDidMount(){this.treemap=this.createTreemap(),window.addEventListener("resize",this.resize)}componentWillReceiveProps(e){if(e.data!==this.props.data)this.treemap.set({dataObject:this.getTreemapDataObject(e.data)});else if(e.highlightGroups!==this.props.highlightGroups){const t=[...e.highlightGroups,...this.props.highlightGroups];setTimeout((()=>this.treemap.redraw(!1,t)))}}shouldComponentUpdate(){return!1}componentWillUnmount(){window.removeEventListener("resize",this.resize),this.treemap.dispose()}render(){return Fi("div",{...this.props,ref:this.saveNodeRef})}getTreemapDataObject(e=this.props.data){return{groups:e}}createTreemap(){const e=this,{props:t}=this;return new Pi({element:this.node,layout:"squarified",stacking:"flattened",pixelRatio:window.devicePixelRatio||1,maxGroups:1/0,maxGroupLevelsDrawn:1/0,maxGroupLabelLevelsDrawn:1/0,maxGroupLevelsAttached:1/0,wireframeLabelDrawing:"always",groupMinDiameter:0,groupLabelVerticalPadding:.2,rolloutDuration:0,pullbackDuration:0,fadeDuration:0,groupExposureZoomMargin:.2,zoomMouseWheelDuration:300,openCloseDuration:200,dataObject:this.getTreemapDataObject(),titleBarDecorator(e,t,n){n.titleBarShown=!1},groupColorDecorator(t,n,r){const{highlightGroups:i}=e.props,o=n.group;i&&i.has(o)&&(r.groupColor={model:"rgba",r:255,g:0,b:0,a:.8})},onGroupClick(n){Gi(n),(n.ctrlKey||n.secondary)&&t.onGroupSecondaryClick?t.onGroupSecondaryClick.call(e,n):(e.zoomOutDisabled=!1,this.zoom(n.group))},onGroupDoubleClick:Gi,onGroupHover(n){if(n.group&&(n.group.attribution||n.group===this.get("dataObject")))return n.preventDefault(),void(t.onMouseLeave&&t.onMouseLeave.call(e,n));t.onGroupHover&&t.onGroupHover.call(e,n)},onGroupMouseWheel(t){const{scale:n}=this.get("viewport");if(t.delta<0){if(e.zoomOutDisabled)return Gi(t);n<1&&(e.zoomOutDisabled=!0,Gi(t))}else e.zoomOutDisabled=!1}})}zoomToGroup(e){for(this.zoomOutDisabled=!1;e&&!this.treemap.get("state",e).revealed;)e=this.treemap.get("hierarchy",e).parent;e&&this.treemap.zoom(e)}isGroupRendered(e){const t=this.treemap.get("state",e);return!!t&&t.revealed}update(){this.treemap.update()}}function Gi(e){e.preventDefault()}var Ui=n(4184),Hi=n.n(Ui),Vi=n(3379),qi=n.n(Vi),Wi=n(7527),Zi={insert:"head",singleton:!1};qi()(Wi.Z,Zi);const $i=Wi.Z.locals||{};class Ki extends p{constructor(...e){super(...e),this.mouseCoords={x:0,y:0},this.state={left:0,top:0},this.handleMouseMove=e=>{Object.assign(this.mouseCoords,{x:e.pageX,y:e.pageY}),this.props.visible&&this.updatePosition()},this.saveNode=e=>this.node=e}componentDidMount(){document.addEventListener("mousemove",this.handleMouseMove,!0)}shouldComponentUpdate(e){return this.props.visible||e.visible}componentWillUnmount(){document.removeEventListener("mousemove",this.handleMouseMove,!0)}render(){const{children:e,visible:t}=this.props,n=Hi()({[$i.container]:!0,[$i.hidden]:!t});return Fi("div",{ref:this.saveNode,className:n,style:this.getStyle(),children:e})}getStyle(){return{left:this.state.left,top:this.state.top}}updatePosition(){if(!this.props.visible)return;const e={left:this.mouseCoords.x+Ki.marginX,top:this.mouseCoords.y+Ki.marginY},t=this.node.getBoundingClientRect();e.left+t.width>window.innerWidth&&(e.left=window.innerWidth-t.width),e.top+t.height>window.innerHeight&&(e.top=this.mouseCoords.y-Ki.marginY-t.height),this.setState(e)}}Ki.marginX=10,Ki.marginY=30;var Yi=n(3908),Ji={insert:"head",singleton:!1};qi()(Yi.Z,Ji);const Xi=Yi.Z.locals||{};class Qi extends p{shouldComponentUpdate(e,t){return!eo(e,this.props)||!eo(this.state,t)}}function eo(e,t){if(e===t)return!0;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;r++){const i=n[r];if(e[i]!==t[i])return!1}return!0}class to extends Qi{constructor(...e){super(...e),this.handleClick=e=>{this.elem.blur(),this.props.onClick(e)},this.saveRef=e=>this.elem=e}render({active:e,toggle:t,className:n,children:r,...i}){const o=Hi()(n,{[Xi.button]:!0,[Xi.active]:e,[Xi.toggle]:t});return Fi("button",{...i,ref:this.saveRef,type:"button",className:o,disabled:this.disabled,onClick:this.handleClick,children:r})}get disabled(){const{props:e}=this;return e.disabled||e.active&&!e.toggle}}class no extends Qi{constructor(...e){super(...e),this.handleClick=()=>{this.props.onClick(this.props.item)}}render({item:e,...t}){return Fi(to,{...t,onClick:this.handleClick,children:e.label})}}var ro=n(6897),io={insert:"head",singleton:!1};qi()(ro.Z,io);const oo=ro.Z.locals||{};class ao extends Qi{render(){const{label:e,items:t,activeItem:n,onSwitch:r}=this.props;return Fi("div",{className:oo.container,children:[Fi("div",{className:oo.label,children:[e,":"]}),Fi("div",{children:t.map((e=>Fi(no,{className:oo.item,item:e,active:e===n,onClick:r},e.label)))})]})}}var so=n(4826),uo={insert:"head",singleton:!1};qi()(so.Z,uo);const lo=so.Z.locals||{};var co=n(1746),ho={insert:"head",singleton:!1};qi()(co.Z,ho);const fo=co.Z.locals||{},po={"arrow-right":{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNLjgyMiAxMi44MTFhLjQ0NS40NDUgMCAwIDEtLjMyMi4xMzMuNDU2LjQ1NiAwIDAgMS0uMzIyLS43NzhMNS44NDQgNi41LjE3OC44MzNBLjQ1Ni40NTYgMCAwIDEgLjgyMi4xOWw1Ljk5IDUuOTg5YS40NTYuNDU2IDAgMCAxIDAgLjY0NGwtNS45OSA1Ljk5eiIvPjwvc3ZnPg==",size:[7,13]},pin:{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYuMDEyIDE3Ljk0OWwtMS40OTMtNi4zNzZhMTAuOTM1IDEwLjkzNSAwIDAgMCAyLjk4NS4wMDJMNi4wMTIgMTcuOTV6TTkuMjQ2IDEuODU3YzAgLjUyLS40MTUuOTg1LTEuMDcgMS4zMDhsLS4wMDEgMi42MTZjMS43MjUuNDEgMi45MjIgMS4yOTQgMi45MjIgMi4zMiAwIC40MTYtLjE5NS44MDktLjU0MiAxLjE1Ni0uNjQuNjM5LTEuNzk0IDEuMTI0LTMuMTg3IDEuMzE4LS4wMy4wMDUtLjA2Mi4wMDgtLjA5My4wMTJhOC45MTcgOC45MTcgMCAwIDEtLjcyNS4wNjVsLS4xMi4wMDdhMTAuMTU0IDEwLjE1NCAwIDAgMS0uODk1LS4wMDNjLS4wOTgtLjAwNS0uMTkzLS4wMTMtLjI4OC0uMDItLjA1My0uMDA0LS4xMDYtLjAwNy0uMTU4LS4wMTJhOS4yNDcgOS4yNDcgMCAwIDEtLjM3Mi0uMDQzbC0uMDQ1LS4wMDZDMi41MTQgMTAuMjc4LjkyNiA5LjI4NS45MjYgOC4xMDFjMC0uNDE1LjE5Ni0uODA3LjU0My0xLjE1NC41MTEtLjUxMiAxLjM1Mi0uOTI0IDIuMzgtMS4xNjhWMy4xNjVjLS42NTYtLjMyMy0xLjA3LS43ODktMS4wNy0xLjMwOUMyLjc3OC44ODIgNC4yMjUuMDkyIDYuMDExLjA5MnMzLjIzNC43OSAzLjIzNCAxLjc2NXoiLz48L3N2Zz4=",size:[12,18]}};class go extends Qi{render({className:e}){return Fi("i",{className:Hi()(fo.icon,e),style:this.style})}get style(){const{name:e,size:t,rotate:n}=this.props,r=po[e];if(!r)throw new TypeError(`Can't find "${e}" icon.`);let[i,o]=r.size;if(t){const e=t/Math.max(i,o);i=Math.min(Math.ceil(i*e),t),o=Math.min(Math.ceil(o*e),t)}return{backgroundImage:`url(${r.src})`,width:`${i}px`,height:`${o}px`,transform:n?`rotate(${n}deg)`:""}}}const bo=parseInt(lo.toggleTime);class vo extends p{constructor(...e){super(...e),this.allowHide=!0,this.toggling=!1,this.hideContentTimeout=null,this.width=null,this.state={visible:!0,renderContent:!0},this.handleClick=()=>{this.allowHide=!1},this.handleMouseEnter=()=>{this.toggling||this.props.pinned||(clearTimeout(this.hideTimeoutId),this.toggleVisibility(!0))},this.handleMouseMove=()=>{this.allowHide=!0},this.handleMouseLeave=()=>{!this.allowHide||this.toggling||this.props.pinned||this.toggleVisibility(!1)},this.handleToggleButtonClick=()=>{this.toggleVisibility()},this.handlePinButtonClick=()=>{const e=!this.props.pinned;this.width=e?this.node.getBoundingClientRect().width:null,this.updateNodeWidth(),this.props.onPinStateChange(e)},this.handleResizeStart=e=>{this.resizeInfo={startPageX:e.pageX,initialWidth:this.width},document.body.classList.add("resizing","col"),document.addEventListener("mousemove",this.handleResize,!0),document.addEventListener("mouseup",this.handleResizeEnd,!0)},this.handleResize=e=>{this.width=this.resizeInfo.initialWidth+(e.pageX-this.resizeInfo.startPageX),this.updateNodeWidth()},this.handleResizeEnd=()=>{document.body.classList.remove("resizing","col"),document.removeEventListener("mousemove",this.handleResize,!0),document.removeEventListener("mouseup",this.handleResizeEnd,!0),this.props.onResize()},this.saveNode=e=>this.node=e}componentDidMount(){this.hideTimeoutId=setTimeout((()=>this.toggleVisibility(!1)),3e3)}componentWillUnmount(){clearTimeout(this.hideTimeoutId),clearTimeout(this.hideContentTimeout)}render(){const{position:e,pinned:t,children:n}=this.props,{visible:r,renderContent:i}=this.state,o=Hi()({[lo.container]:!0,[lo.pinned]:t,[lo.left]:"left"===e,[lo.hidden]:!r,[lo.empty]:!i});return Fi("div",{ref:this.saveNode,className:o,onClick:this.handleClick,onMouseLeave:this.handleMouseLeave,children:[r&&Fi(to,{type:"button",title:"Pin",className:lo.pinButton,active:t,toggle:!0,onClick:this.handlePinButtonClick,children:Fi(go,{name:"pin",size:13})}),Fi(to,{type:"button",title:r?"Hide":"Show sidebar",className:lo.toggleButton,onClick:this.handleToggleButtonClick,children:Fi(go,{name:"arrow-right",size:10,rotate:r?180:0})}),t&&r&&Fi("div",{className:lo.resizer,onMouseDown:this.handleResizeStart}),Fi("div",{className:lo.content,onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,children:i?n:null})]})}toggleVisibility(e){clearTimeout(this.hideContentTimeout);const{visible:t}=this.state,{onToggle:n,pinned:r}=this.props;if(void 0===e)e=!t;else if(e===t)return;this.setState({visible:e}),this.toggling=!0,setTimeout((()=>{this.toggling=!1}),bo),r&&this.updateNodeWidth(e?this.width:null),e||r?(this.setState({renderContent:e}),n(e)):e||(this.hideContentTimeout=setTimeout((()=>{this.hideContentTimeout=null,this.setState({renderContent:!1}),n(!1)}),bo))}updateNodeWidth(e=this.width){this.node.style.width=e?`${e}px`:""}}vo.defaultProps={pinned:!1,position:"left"};var mo=n(2396),yo={insert:"head",singleton:!1};qi()(mo.Z,yo);const Co=mo.Z.locals||{};class _o extends p{constructor(...e){super(...e),this.handleChange=()=>{this.props.onChange(!this.props.checked)}}render(){const{checked:e,className:t,children:n}=this.props;return Fi("label",{className:Hi()(Co.label,t),children:[Fi("input",{className:Co.checkbox,type:"checkbox",checked:e,onChange:this.handleChange}),Fi("span",{className:Co.itemText,children:n})]})}}var wo=n(3213),xo={insert:"head",singleton:!1};qi()(wo.Z,xo);const Ao=wo.Z.locals||{};class So extends p{constructor(...e){super(...e),this.handleChange=()=>{this.props.onChange(this.props.item)}}render(){return Fi("div",{className:Ao.item,children:Fi(_o,{...this.props,onChange:this.handleChange,children:this.renderLabel()})})}renderLabel(){const{children:e,item:t}=this.props;return e?e(t):t===To.ALL_ITEM?"All":t.label}}const Mo=Symbol("ALL_ITEM");class To extends Qi{constructor(e){super(e),this.handleToggleAllCheck=()=>{const e=this.isAllChecked()?[]:this.props.items;this.setState({checkedItems:e}),this.informAboutChange(e)},this.handleItemCheck=e=>{let t;t=this.isItemChecked(e)?this.state.checkedItems.filter((t=>t!==e)):[...this.state.checkedItems,e],this.setState({checkedItems:t}),this.informAboutChange(t)},this.state={checkedItems:e.checkedItems||e.items}}componentWillReceiveProps(e){if(e.items!==this.props.items){if(this.isAllChecked())this.setState({checkedItems:e.items}),this.informAboutChange(e.items);else if(this.state.checkedItems.length){const t=e.items.filter((e=>this.state.checkedItems.find((t=>t.label===e.label))));this.setState({checkedItems:t}),this.informAboutChange(t)}}else e.checkedItems!==this.props.checkedItems&&this.setState({checkedItems:e.checkedItems})}render(){const{label:e,items:t,renderLabel:n}=this.props;return Fi("div",{className:Ao.container,children:[Fi("div",{className:Ao.label,children:[e,":"]}),Fi("div",{children:[Fi(So,{item:Mo,checked:this.isAllChecked(),onChange:this.handleToggleAllCheck,children:n}),t.map((e=>Fi(So,{item:e,checked:this.isItemChecked(e),onChange:this.handleItemCheck,children:n},e.label)))]})]})}isItemChecked(e){return this.state.checkedItems.includes(e)}isAllChecked(){return this.props.items.length===this.state.checkedItems.length}informAboutChange(e){setTimeout((()=>this.props.onChange(e)))}}To.ALL_ITEM=Mo;var ko=n(9270),zo={insert:"head",singleton:!1};qi()(ko.Z,zo);const Do=ko.Z.locals||{};function jo(){return!1}function Lo({children:e,disabled:t,onClick:n}){return Fi("li",{className:Hi()({[Do.item]:!0,[Do.disabled]:t}),onClick:t?jo:n,children:e})}var Bo=n(580),Eo={insert:"head",singleton:!1};qi()(Bo.Z,Eo);const Oo=Bo.Z.locals||{};class Io extends Qi{constructor(...e){super(...e),this.handleClickHideChunk=()=>{const{chunk:e}=this.props;if(e&&e.label){const t=Fn.selectedChunks.filter((t=>t.label!==e.label));Fn.selectedChunks=t}this.hide()},this.handleClickFilterToChunk=()=>{const{chunk:e}=this.props;if(e&&e.label){const t=Fn.allChunks.filter((t=>t.label===e.label));Fn.selectedChunks=t}this.hide()},this.handleClickShowAllChunks=()=>{Fn.selectedChunks=Fn.allChunks,this.hide()},this.handleDocumentMousedown=e=>{var t,n;e.ctrlKey||2===e.button||(t=e.target,n=this.node,t===n||n.contains(t))||(e.preventDefault(),e.stopPropagation(),this.hide())},this.saveNode=e=>this.node=e}componentDidMount(){this.boundingRect=this.node.getBoundingClientRect()}componentDidUpdate(e){this.props.visible&&!e.visible?document.addEventListener("mousedown",this.handleDocumentMousedown,!0):e.visible&&!this.props.visible&&document.removeEventListener("mousedown",this.handleDocumentMousedown,!0)}render(){const{visible:e}=this.props,t=Hi()({[Oo.container]:!0,[Oo.hidden]:!e}),n=Fn.selectedChunks.length>1;return Fi("ul",{ref:this.saveNode,className:t,style:this.getStyle(),children:[Fi(Lo,{disabled:!n,onClick:this.handleClickHideChunk,children:"Hide chunk"}),Fi(Lo,{disabled:!n,onClick:this.handleClickFilterToChunk,children:"Hide all other chunks"}),Fi("hr",{}),Fi(Lo,{disabled:Fn.allChunksSelected,onClick:this.handleClickShowAllChunks,children:"Show all chunks"})]})}hide(){this.props.onHide&&this.props.onHide()}getStyle(){const{boundingRect:e}=this;if(!e)return;const{coords:t}=this.props,n={left:t.x,top:t.y};return n.left+e.width>window.innerWidth&&(n.left=window.innerWidth-e.width),n.top+e.height>window.innerHeight&&(n.top=t.y-e.height),n}}var No=n(2393),Po={insert:"head",singleton:!1};qi()(No.Z,Po);const Fo=No.Z.locals||{};var Ro=n(3279),Go=n.n(Ro),Uo=n(9976),Ho={insert:"head",singleton:!1};qi()(Uo.Z,Ho);const Vo=Uo.Z.locals||{};class qo extends Qi{constructor(...e){super(...e),this.handleValueChange=Go()((e=>{this.informChange(e.target.value)}),400),this.handleInputBlur=()=>{this.handleValueChange.flush()},this.handleClearClick=()=>{this.clear(),this.focus()},this.handleKeyDown=e=>{let t=!0;switch(e.key){case"Escape":this.clear();break;case"Enter":this.handleValueChange.flush();break;default:t=!1}t&&e.stopPropagation()},this.saveInputNode=e=>this.input=e}componentDidMount(){this.props.autofocus&&this.focus()}componentWillUnmount(){this.handleValueChange.cancel()}render(){const{label:e,query:t}=this.props;return Fi("div",{className:Vo.container,children:[Fi("div",{className:Vo.label,children:[e,":"]}),Fi("div",{className:Vo.row,children:[Fi("input",{ref:this.saveInputNode,className:Vo.input,type:"text",value:t,placeholder:"Enter regexp",onInput:this.handleValueChange,onBlur:this.handleInputBlur,onKeyDown:this.handleKeyDown}),Fi(to,{className:Vo.clear,onClick:this.handleClearClick,children:"x"})]})]})}focus(){this.input&&this.input.focus()}clear(){this.handleValueChange.cancel(),this.informChange(""),this.input.value=""}informChange(e){this.props.onQueryChange(e)}}var Wo=n(3784),Zo={insert:"head",singleton:!1};qi()(Wo.Z,Zo);const $o=Wo.Z.locals||{};var Ko=n(1700),Yo=n.n(Ko),Jo=n(7187),Xo=n.n(Jo),Qo=n(3522),ea=n.n(Qo),ta=n(697),na={insert:"head",singleton:!1};qi()(ta.Z,na);const ra=ta.Z.locals||{};class ia extends Qi{constructor(...e){super(...e),this.state={visible:!0},this.handleClick=()=>this.props.onClick(this.props.module),this.handleMouseEnter=()=>{this.props.isVisible&&this.setState({visible:this.isVisible})}}render({module:e,showSize:t}){const n=!this.state.visible;return Fi("div",{className:Hi()(ra.container,ra[this.itemType],{[ra.invisible]:n}),title:n?this.invisibleHint:null,onClick:this.handleClick,onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,children:[Fi("span",{dangerouslySetInnerHTML:{__html:this.titleHtml}}),t&&[" (",Fi("strong",{children:Vn()(e[t])}),")"]]})}get itemType(){const{module:e}=this.props;return e.path?e.groups?"folder":"module":"chunk"}get titleHtml(){let e;const{module:t}=this.props,n=t.path||t.label,r=this.props.highlightedText;if(r){const t=r instanceof RegExp?new RegExp(r.source,"igu"):new RegExp(`(?:${ea()(r)})+`,"iu");let i,o;do{o=i,i=t.exec(n)}while(i);o&&(e=Xo()(n.slice(0,o.index))+`<strong>${Xo()(o[0])}</strong>`+Xo()(n.slice(o.index+o[0].length)))}return e||(e=Xo()(n)),e}get invisibleHint(){return`${Yo()(this.itemType)} is not rendered in the treemap because it's too small.`}get isVisible(){const{isVisible:e}=this.props;return!e||e(this.props.module)}}class oa extends Qi{constructor(...e){super(...e),this.handleModuleClick=e=>this.props.onModuleClick(e)}render({modules:e,showSize:t,highlightedText:n,isModuleVisible:r,className:i}){return Fi("div",{className:Hi()($o.container,i),children:e.map((e=>Fi(ia,{module:e,showSize:t,highlightedText:n,isVisible:r,onClick:this.handleModuleClick},e.cid)))})}}var aa,sa;const ua=[{label:"Stat",prop:"statSize"},{label:"Parsed",prop:"parsedSize"},{label:"Gzipped",prop:"gzipSize"}];let la=Ni((N((sa=class extends p{constructor(...e){super(...e),this.mouseCoords={x:0,y:0},this.state={selectedChunk:null,selectedMouseCoords:{x:0,y:0},sidebarPinned:!1,showChunkContextMenu:!1,showTooltip:!1,tooltipContent:null},this.renderChunkItemLabel=e=>{const t=e===To.ALL_ITEM,n=t?"All":e.label,r=t?Fn.totalChunksSize:e[Fn.activeSize];return[`${n} (`,Fi("strong",{children:Vn()(r)}),")"]},this.handleConcatenatedModulesContentToggle=e=>{Fn.showConcatenatedModulesContent=e,e?zn.setItem("showConcatenatedModulesContent",!0):zn.removeItem("showConcatenatedModulesContent")},this.handleChunkContextMenuHide=()=>{this.setState({showChunkContextMenu:!1})},this.handleResize=()=>{this.state.showChunkContextMenu&&this.setState({showChunkContextMenu:!1})},this.handleSidebarToggle=()=>{this.state.sidebarPinned&&setTimeout((()=>this.treemap.resize()))},this.handleSidebarPinStateChange=e=>{this.setState({sidebarPinned:e}),setTimeout((()=>this.treemap.resize()))},this.handleSidebarResize=()=>{this.treemap.resize()},this.handleSizeSwitch=e=>{Fn.selectedSize=e.prop},this.handleQueryChange=e=>{Fn.searchQuery=e},this.handleSelectedChunksChange=e=>{Fn.selectedChunks=e},this.handleMouseLeaveTreemap=()=>{this.setState({showTooltip:!1})},this.handleTreemapGroupSecondaryClick=e=>{const{group:t}=e;t&&t.isAsset?this.setState({selectedChunk:t,selectedMouseCoords:{...this.mouseCoords},showChunkContextMenu:!0}):this.setState({selectedChunk:null,showChunkContextMenu:!1})},this.handleTreemapGroupHover=e=>{const{group:t}=e;t?this.setState({showTooltip:!0,tooltipContent:this.getTooltipContent(t)}):this.setState({showTooltip:!1})},this.handleFoundModuleClick=e=>this.treemap.zoomToGroup(e),this.handleMouseMove=e=>{Object.assign(this.mouseCoords,{x:e.pageX,y:e.pageY})},this.isModuleVisible=e=>this.treemap.isGroupRendered(e),this.saveTreemapRef=e=>this.treemap=e}componentDidMount(){document.addEventListener("mousemove",this.handleMouseMove,!0)}componentWillUnmount(){document.removeEventListener("mousemove",this.handleMouseMove,!0)}render(){const{selectedChunk:e,selectedMouseCoords:t,sidebarPinned:n,showChunkContextMenu:r,showTooltip:i,tooltipContent:o}=this.state;return Fi("div",{className:Fo.container,children:[Fi(vo,{pinned:n,onToggle:this.handleSidebarToggle,onPinStateChange:this.handleSidebarPinStateChange,onResize:this.handleSidebarResize,children:[Fi("div",{className:Fo.sidebarGroup,children:[Fi(ao,{label:"Treemap sizes",items:this.sizeSwitchItems,activeItem:this.activeSizeItem,onSwitch:this.handleSizeSwitch}),Fn.hasConcatenatedModules&&Fi("div",{className:Fo.showOption,children:Fi(_o,{checked:Fn.showConcatenatedModulesContent,onChange:this.handleConcatenatedModulesContentToggle,children:"Show content of concatenated modules"+("statSize"===Fn.activeSize?"":" (inaccurate)")})})]}),Fi("div",{className:Fo.sidebarGroup,children:[Fi(qo,{label:"Search modules",query:Fn.searchQuery,autofocus:!0,onQueryChange:this.handleQueryChange}),Fi("div",{className:Fo.foundModulesInfo,children:this.foundModulesInfo}),Fn.isSearching&&Fn.hasFoundModules&&Fi("div",{className:Fo.foundModulesContainer,children:Fn.foundModulesByChunk.map((({chunk:e,modules:t})=>Fi("div",{className:Fo.foundModulesChunk,children:[Fi("div",{className:Fo.foundModulesChunkName,onClick:()=>this.treemap.zoomToGroup(e),children:e.label}),Fi(oa,{className:Fo.foundModulesList,modules:t,showSize:Fn.activeSize,highlightedText:Fn.searchQueryRegexp,isModuleVisible:this.isModuleVisible,onModuleClick:this.handleFoundModuleClick})]},e.cid)))})]}),this.chunkItems.length>1&&Fi("div",{className:Fo.sidebarGroup,children:Fi(To,{label:"Show chunks",items:this.chunkItems,checkedItems:Fn.selectedChunks,renderLabel:this.renderChunkItemLabel,onChange:this.handleSelectedChunksChange})})]}),Fi(Ri,{ref:this.saveTreemapRef,className:Fo.map,data:Fn.visibleChunks,highlightGroups:this.highlightedModules,weightProp:Fn.activeSize,onMouseLeave:this.handleMouseLeaveTreemap,onGroupHover:this.handleTreemapGroupHover,onGroupSecondaryClick:this.handleTreemapGroupSecondaryClick,onResize:this.handleResize}),Fi(Ki,{visible:i,children:o}),Fi(Io,{visible:r,chunk:e,coords:t,onHide:this.handleChunkContextMenuHide})]})}renderModuleSize(e,t){const n=`${t}Size`,r=e[n],i=ua.find((e=>e.prop===n)).label,o=Fn.activeSize===n;return"number"==typeof r?Fi("div",{className:o?Fo.activeSize:"",children:[i," size: ",Fi("strong",{children:Vn()(r)})]}):null}get sizeSwitchItems(){return Fn.hasParsedSizes?ua:ua.slice(0,1)}get activeSizeItem(){return this.sizeSwitchItems.find((e=>e.prop===Fn.activeSize))}get chunkItems(){const{allChunks:e,activeSize:t}=Fn;let n=[...e];return"statSize"!==t&&(n=n.filter(Tn)),n.sort(((e,n)=>n[t]-e[t])),n}get highlightedModules(){return new Set(Fn.foundModules)}get foundModulesInfo(){return Fn.isSearching?Fn.hasFoundModules?[Fi("div",{className:Fo.foundModulesInfoItem,children:["Count: ",Fi("strong",{children:Fn.foundModules.length})]}),Fi("div",{className:Fo.foundModulesInfoItem,children:["Total size: ",Fi("strong",{children:Vn()(Fn.foundModulesSize)})]})]:"Nothing found"+(Fn.allChunksSelected?"":" in selected chunks"):" "}getTooltipContent(e){return e?Fi("div",{children:[Fi("div",{children:Fi("strong",{children:e.label})}),Fi("br",{}),this.renderModuleSize(e,"stat"),!e.inaccurateSizes&&this.renderModuleSize(e,"parsed"),!e.inaccurateSizes&&this.renderModuleSize(e,"gzip"),e.path&&Fi("div",{children:["Path: ",Fi("strong",{children:e.path})]}),e.isAsset&&Fi("div",{children:[Fi("br",{}),Fi("strong",{children:Fi("em",{children:"Right-click to view options related to this chunk"})})]})]}):null}}).prototype,"sizeSwitchItems",[ze],Object.getOwnPropertyDescriptor(sa.prototype,"sizeSwitchItems"),sa.prototype),N(sa.prototype,"activeSizeItem",[ze],Object.getOwnPropertyDescriptor(sa.prototype,"activeSizeItem"),sa.prototype),N(sa.prototype,"chunkItems",[ze],Object.getOwnPropertyDescriptor(sa.prototype,"chunkItems"),sa.prototype),N(sa.prototype,"highlightedModules",[ze],Object.getOwnPropertyDescriptor(sa.prototype,"highlightedModules"),sa.prototype),N(sa.prototype,"foundModulesInfo",[ze],Object.getOwnPropertyDescriptor(sa.prototype,"foundModulesInfo"),sa.prototype),aa=sa))||aa;var ca=n(1194),ha={insert:"head",singleton:!1};qi()(ca.Z,ha);ca.Z.locals;let fa;try{window.enableWebSocket&&(fa=new WebSocket(`ws://${location.host}`))}catch(da){console.warn("Couldn't connect to analyzer websocket server so you'll have to reload page manually to see updates in the treemap")}window.addEventListener("load",(()=>{Fn.defaultSize=`${window.defaultSizes}Size`,Fn.setModules(window.chartData),B(Fi(la,{}),document.getElementById("app")),fa&&fa.addEventListener("message",(e=>{const t=JSON.parse(e.data);"chartDataUpdated"===t.event&&Fn.setModules(t.data)}))}),!1)})()})();
//# sourceMappingURL=viewer.js.map</script>
  </head>

  <body>
    <div id="app"></div>
    <script>
      window.chartData = [{"label":"kubedoom-extension-0.1.0.umd.min.js","isAsset":true,"statSize":222775,"parsedSize":104788,"gzipSize":29911,"groups":[{"label":"../node_modules","path":"./../node_modules","statSize":208453,"groups":[{"label":"@vue/cli-service/lib/commands/build","path":"./../node_modules/@vue/cli-service/lib/commands/build","statSize":29551,"groups":[{"id":458,"label":"entry-lib.js + 16 modules (concatenated)","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)","statSize":29551,"parsedSize":104788,"gzipSize":29911,"concatenated":true,"groups":[{"label":"../node_modules/@vue/cli-service/lib/commands/build","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/../node_modules/@vue/cli-service/lib/commands/build","statSize":876,"groups":[{"id":null,"label":"entry-lib.js","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js","statSize":92,"parsedSize":326,"gzipSize":93,"inaccurateSizes":true},{"id":null,"label":"setPublicPath.js","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/../node_modules/@vue/cli-service/lib/commands/build/setPublicPath.js","statSize":784,"parsedSize":2780,"gzipSize":793,"inaccurateSizes":true}],"parsedSize":3106,"gzipSize":886,"inaccurateSizes":true},{"id":null,"label":"index.ts","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/index.ts","statSize":460,"parsedSize":1631,"gzipSize":465,"inaccurateSizes":true},{"label":"node_modules/@rancher","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/node_modules/@rancher","statSize":43,"groups":[{"id":null,"label":"auto-import","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/node_modules/@rancher/auto-import","statSize":43,"parsedSize":152,"gzipSize":43,"inaccurateSizes":true}],"parsedSize":152,"gzipSize":43,"inaccurateSizes":true},{"label":"routing","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/routing","statSize":707,"groups":[{"id":null,"label":"extension-routing.ts","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/routing/extension-routing.ts","statSize":707,"parsedSize":2507,"gzipSize":715,"inaccurateSizes":true}],"parsedSize":2507,"gzipSize":715,"inaccurateSizes":true},{"label":"pages","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages","statSize":27465,"groups":[{"id":null,"label":"Dashboard.vue","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/Dashboard.vue","statSize":505,"parsedSize":1790,"gzipSize":511,"inaccurateSizes":true},{"id":null,"label":"KubeDoomGame.vue","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/KubeDoomGame.vue","statSize":517,"parsedSize":1833,"gzipSize":523,"inaccurateSizes":true},{"id":null,"label":"Dashboard.vue?vue&type=template&id=471e0416&scoped=true","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/Dashboard.vue?vue&type=template&id=471e0416&scoped=true","statSize":6772,"parsedSize":24013,"gzipSize":6854,"inaccurateSizes":true},{"id":null,"label":"Dashboard.vue?vue&type=script&lang=js","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/Dashboard.vue?vue&type=script&lang=js","statSize":4446,"parsedSize":15765,"gzipSize":4500,"inaccurateSizes":true},{"id":null,"label":"Dashboard.vue?vue&type=style&index=0&id=471e0416&lang=scss&scoped=true","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/Dashboard.vue?vue&type=style&index=0&id=471e0416&lang=scss&scoped=true","statSize":641,"parsedSize":2272,"gzipSize":648,"inaccurateSizes":true},{"id":null,"label":"KubeDoomGame.vue?vue&type=template&id=6d118c02&scoped=true","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/KubeDoomGame.vue?vue&type=template&id=6d118c02&scoped=true","statSize":7588,"parsedSize":26907,"gzipSize":7680,"inaccurateSizes":true},{"id":null,"label":"KubeDoomGame.vue?vue&type=script&lang=js","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/KubeDoomGame.vue?vue&type=script&lang=js","statSize":6352,"parsedSize":22524,"gzipSize":6429,"inaccurateSizes":true},{"id":null,"label":"KubeDoomGame.vue?vue&type=style&index=0&id=6d118c02&lang=scss&scoped=true","path":"./../node_modules/@vue/cli-service/lib/commands/build/entry-lib.js + 16 modules (concatenated)/pages/KubeDoomGame.vue?vue&type=style&index=0&id=6d118c02&lang=scss&scoped=true","statSize":644,"parsedSize":2283,"gzipSize":651,"inaccurateSizes":true}],"parsedSize":97391,"gzipSize":27799,"inaccurateSizes":true}]}],"parsedSize":104788,"gzipSize":29911},{"label":"assert/build","path":"./../node_modules/assert/build","statSize":76059,"groups":[{"id":6093,"label":"assert.js","path":"./../node_modules/assert/build/assert.js","statSize":22080},{"label":"internal","path":"./../node_modules/assert/build/internal","statSize":53979,"groups":[{"label":"assert","path":"./../node_modules/assert/build/internal/assert","statSize":23227,"groups":[{"id":9801,"label":"assertion_error.js","path":"./../node_modules/assert/build/internal/assert/assertion_error.js","statSize":23227}],"parsedSize":0,"gzipSize":0},{"id":1342,"label":"errors.js","path":"./../node_modules/assert/build/internal/errors.js","statSize":9339},{"label":"util","path":"./../node_modules/assert/build/internal/util","statSize":21413,"groups":[{"id":5656,"label":"comparisons.js","path":"./../node_modules/assert/build/internal/util/comparisons.js","statSize":21413}],"parsedSize":0,"gzipSize":0}],"parsedSize":0,"gzipSize":0}],"parsedSize":0,"gzipSize":0},{"label":"available-typed-arrays","path":"./../node_modules/available-typed-arrays","statSize":475,"groups":[{"id":4834,"label":"index.js","path":"./../node_modules/available-typed-arrays/index.js","statSize":475}],"parsedSize":0,"gzipSize":0},{"label":"call-bind-apply-helpers","path":"./../node_modules/call-bind-apply-helpers","statSize":1383,"groups":[{"id":8165,"label":"actualApply.js","path":"./../node_modules/call-bind-apply-helpers/actualApply.js","statSize":280},{"id":2012,"label":"applyBind.js","path":"./../node_modules/call-bind-apply-helpers/applyBind.js","statSize":264},{"id":6095,"label":"functionApply.js","path":"./../node_modules/call-bind-apply-helpers/functionApply.js","statSize":99},{"id":4531,"label":"functionCall.js","path":"./../node_modules/call-bind-apply-helpers/functionCall.js","statSize":97},{"id":9903,"label":"index.js","path":"./../node_modules/call-bind-apply-helpers/index.js","statSize":511},{"id":7196,"label":"reflectApply.js","path":"./../node_modules/call-bind-apply-helpers/reflectApply.js","statSize":132}],"parsedSize":0,"gzipSize":0},{"label":"call-bind","path":"./../node_modules/call-bind","statSize":1056,"groups":[{"id":9818,"label":"callBound.js","path":"./../node_modules/call-bind/callBound.js","statSize":413},{"id":8498,"label":"index.js","path":"./../node_modules/call-bind/index.js","statSize":643}],"parsedSize":0,"gzipSize":0},{"label":"call-bound","path":"./../node_modules/call-bound","statSize":687,"groups":[{"id":4607,"label":"index.js","path":"./../node_modules/call-bound/index.js","statSize":687}],"parsedSize":0,"gzipSize":0},{"label":"console-browserify","path":"./../node_modules/console-browserify","statSize":1709,"groups":[{"id":4364,"label":"index.js","path":"./../node_modules/console-browserify/index.js","statSize":1709}],"parsedSize":0,"gzipSize":0},{"label":"css-loader/dist/runtime","path":"./../node_modules/css-loader/dist/runtime","statSize":2367,"groups":[{"id":935,"label":"api.js","path":"./../node_modules/css-loader/dist/runtime/api.js","statSize":2303},{"id":6758,"label":"noSourceMaps.js","path":"./../node_modules/css-loader/dist/runtime/noSourceMaps.js","statSize":64}],"parsedSize":0,"gzipSize":0},{"label":"define-data-property","path":"./../node_modules/define-data-property","statSize":2336,"groups":[{"id":686,"label":"index.js","path":"./../node_modules/define-data-property/index.js","statSize":2336}],"parsedSize":0,"gzipSize":0},{"label":"define-properties","path":"./../node_modules/define-properties","statSize":1268,"groups":[{"id":1857,"label":"index.js","path":"./../node_modules/define-properties/index.js","statSize":1268}],"parsedSize":0,"gzipSize":0},{"label":"dunder-proto","path":"./../node_modules/dunder-proto","statSize":980,"groups":[{"id":6423,"label":"get.js","path":"./../node_modules/dunder-proto/get.js","statSize":980}],"parsedSize":0,"gzipSize":0},{"label":"es-define-property","path":"./../node_modules/es-define-property","statSize":288,"groups":[{"id":4940,"label":"index.js","path":"./../node_modules/es-define-property/index.js","statSize":288}],"parsedSize":0,"gzipSize":0},{"label":"es-errors","path":"./../node_modules/es-errors","statSize":524,"groups":[{"id":9110,"label":"eval.js","path":"./../node_modules/es-errors/eval.js","statSize":75},{"id":9838,"label":"index.js","path":"./../node_modules/es-errors/index.js","statSize":66},{"id":1155,"label":"range.js","path":"./../node_modules/es-errors/range.js","statSize":77},{"id":4943,"label":"ref.js","path":"./../node_modules/es-errors/ref.js","statSize":79},{"id":5731,"label":"syntax.js","path":"./../node_modules/es-errors/syntax.js","statSize":79},{"id":3468,"label":"type.js","path":"./../node_modules/es-errors/type.js","statSize":75},{"id":2140,"label":"uri.js","path":"./../node_modules/es-errors/uri.js","statSize":73}],"parsedSize":0,"gzipSize":0},{"label":"es-object-atoms","path":"./../node_modules/es-object-atoms","statSize":67,"groups":[{"id":9629,"label":"index.js","path":"./../node_modules/es-object-atoms/index.js","statSize":67}],"parsedSize":0,"gzipSize":0},{"label":"for-each","path":"./../node_modules/for-each","statSize":2374,"groups":[{"id":705,"label":"index.js","path":"./../node_modules/for-each/index.js","statSize":2374}],"parsedSize":0,"gzipSize":0},{"label":"function-bind","path":"./../node_modules/function-bind","statSize":2169,"groups":[{"id":8794,"label":"implementation.js","path":"./../node_modules/function-bind/implementation.js","statSize":2043},{"id":9138,"label":"index.js","path":"./../node_modules/function-bind/index.js","statSize":126}],"parsedSize":0,"gzipSize":0},{"label":"get-intrinsic","path":"./../node_modules/get-intrinsic","statSize":14439,"groups":[{"id":528,"label":"index.js","path":"./../node_modules/get-intrinsic/index.js","statSize":14439}],"parsedSize":0,"gzipSize":0},{"label":"get-proto","path":"./../node_modules/get-proto","statSize":1127,"groups":[{"id":7345,"label":"Object.getPrototypeOf.js","path":"./../node_modules/get-proto/Object.getPrototypeOf.js","statSize":156},{"id":7859,"label":"Reflect.getPrototypeOf.js","path":"./../node_modules/get-proto/Reflect.getPrototypeOf.js","statSize":150},{"id":6369,"label":"index.js","path":"./../node_modules/get-proto/index.js","statSize":821}],"parsedSize":0,"gzipSize":0},{"label":"gopd","path":"./../node_modules/gopd","statSize":303,"groups":[{"id":1292,"label":"gOPD.js","path":"./../node_modules/gopd/gOPD.js","statSize":97},{"id":9336,"label":"index.js","path":"./../node_modules/gopd/index.js","statSize":206}],"parsedSize":0,"gzipSize":0},{"label":"has-property-descriptors","path":"./../node_modules/has-property-descriptors","statSize":588,"groups":[{"id":7239,"label":"index.js","path":"./../node_modules/has-property-descriptors/index.js","statSize":588}],"parsedSize":0,"gzipSize":0},{"label":"has-symbols","path":"./../node_modules/has-symbols","statSize":2369,"groups":[{"id":3558,"label":"index.js","path":"./../node_modules/has-symbols/index.js","statSize":447},{"id":2908,"label":"shams.js","path":"./../node_modules/has-symbols/shams.js","statSize":1922}],"parsedSize":0,"gzipSize":0},{"label":"has-tostringtag","path":"./../node_modules/has-tostringtag","statSize":189,"groups":[{"id":1913,"label":"shams.js","path":"./../node_modules/has-tostringtag/shams.js","statSize":189}],"parsedSize":0,"gzipSize":0},{"label":"hasown","path":"./../node_modules/hasown","statSize":206,"groups":[{"id":8554,"label":"index.js","path":"./../node_modules/hasown/index.js","statSize":206}],"parsedSize":0,"gzipSize":0},{"label":"inherits","path":"./../node_modules/inherits","statSize":753,"groups":[{"id":5615,"label":"inherits_browser.js","path":"./../node_modules/inherits/inherits_browser.js","statSize":753}],"parsedSize":0,"gzipSize":0},{"label":"is-arguments","path":"./../node_modules/is-arguments","statSize":1150,"groups":[{"id":5387,"label":"index.js","path":"./../node_modules/is-arguments/index.js","statSize":1150}],"parsedSize":0,"gzipSize":0},{"label":"is-callable","path":"./../node_modules/is-callable","statSize":3224,"groups":[{"id":9617,"label":"index.js","path":"./../node_modules/is-callable/index.js","statSize":3224}],"parsedSize":0,"gzipSize":0},{"label":"is-generator-function","path":"./../node_modules/is-generator-function","statSize":1275,"groups":[{"id":2625,"label":"index.js","path":"./../node_modules/is-generator-function/index.js","statSize":1275}],"parsedSize":0,"gzipSize":0},{"label":"is-nan","path":"./../node_modules/is-nan","statSize":1230,"groups":[{"id":8006,"label":"implementation.js","path":"./../node_modules/is-nan/implementation.js","statSize":156},{"id":7838,"label":"index.js","path":"./../node_modules/is-nan/index.js","statSize":462},{"id":1591,"label":"polyfill.js","path":"./../node_modules/is-nan/polyfill.js","statSize":225},{"id":1641,"label":"shim.js","path":"./../node_modules/is-nan/shim.js","statSize":387}],"parsedSize":0,"gzipSize":0},{"label":"is-regex","path":"./../node_modules/is-regex","statSize":2221,"groups":[{"id":2672,"label":"index.js","path":"./../node_modules/is-regex/index.js","statSize":2221}],"parsedSize":0,"gzipSize":0},{"label":"is-typed-array","path":"./../node_modules/is-typed-array","statSize":180,"groups":[{"id":5943,"label":"index.js","path":"./../node_modules/is-typed-array/index.js","statSize":180}],"parsedSize":0,"gzipSize":0},{"label":"math-intrinsics","path":"./../node_modules/math-intrinsics","statSize":781,"groups":[{"id":8479,"label":"abs.js","path":"./../node_modules/math-intrinsics/abs.js","statSize":73},{"id":8449,"label":"floor.js","path":"./../node_modules/math-intrinsics/floor.js","statSize":77},{"id":2422,"label":"isNaN.js","path":"./../node_modules/math-intrinsics/isNaN.js","statSize":121},{"id":8129,"label":"max.js","path":"./../node_modules/math-intrinsics/max.js","statSize":73},{"id":2387,"label":"min.js","path":"./../node_modules/math-intrinsics/min.js","statSize":73},{"id":5865,"label":"pow.js","path":"./../node_modules/math-intrinsics/pow.js","statSize":73},{"id":1319,"label":"round.js","path":"./../node_modules/math-intrinsics/round.js","statSize":77},{"id":6882,"label":"sign.js","path":"./../node_modules/math-intrinsics/sign.js","statSize":214}],"parsedSize":0,"gzipSize":0},{"label":"object-is","path":"./../node_modules/object-is","statSize":1163,"groups":[{"id":2372,"label":"implementation.js","path":"./../node_modules/object-is/implementation.js","statSize":286},{"id":5968,"label":"index.js","path":"./../node_modules/object-is/index.js","statSize":390},{"id":1937,"label":"polyfill.js","path":"./../node_modules/object-is/polyfill.js","statSize":181},{"id":5087,"label":"shim.js","path":"./../node_modules/object-is/shim.js","statSize":306}],"parsedSize":0,"gzipSize":0},{"label":"object-keys","path":"./../node_modules/object-keys","statSize":4463,"groups":[{"id":8160,"label":"implementation.js","path":"./../node_modules/object-keys/implementation.js","statSize":3218},{"id":9228,"label":"index.js","path":"./../node_modules/object-keys/index.js","statSize":823},{"id":968,"label":"isArguments.js","path":"./../node_modules/object-keys/isArguments.js","statSize":422}],"parsedSize":0,"gzipSize":0},{"label":"object.assign","path":"./../node_modules/object.assign","statSize":2723,"groups":[{"id":5164,"label":"implementation.js","path":"./../node_modules/object.assign/implementation.js","statSize":1424},{"id":3225,"label":"polyfill.js","path":"./../node_modules/object.assign/polyfill.js","statSize":1299}],"parsedSize":0,"gzipSize":0},{"label":"possible-typed-array-names","path":"./../node_modules/possible-typed-array-names","statSize":264,"groups":[{"id":9501,"label":"index.js","path":"./../node_modules/possible-typed-array-names/index.js","statSize":264}],"parsedSize":0,"gzipSize":0},{"label":"process","path":"./../node_modules/process","statSize":5418,"groups":[{"id":9907,"label":"browser.js","path":"./../node_modules/process/browser.js","statSize":5418}],"parsedSize":0,"gzipSize":0},{"label":"safe-regex-test","path":"./../node_modules/safe-regex-test","statSize":405,"groups":[{"id":6132,"label":"index.js","path":"./../node_modules/safe-regex-test/index.js","statSize":405}],"parsedSize":0,"gzipSize":0},{"label":"set-function-length","path":"./../node_modules/set-function-length","statSize":1273,"groups":[{"id":6108,"label":"index.js","path":"./../node_modules/set-function-length/index.js","statSize":1273}],"parsedSize":0,"gzipSize":0},{"label":"util","path":"./../node_modules/util","statSize":28572,"groups":[{"label":"support","path":"./../node_modules/util/support","statSize":8875,"groups":[{"id":5272,"label":"isBufferBrowser.js","path":"./../node_modules/util/support/isBufferBrowser.js","statSize":203},{"id":1531,"label":"types.js","path":"./../node_modules/util/support/types.js","statSize":8672}],"parsedSize":0,"gzipSize":0},{"id":9208,"label":"util.js","path":"./../node_modules/util/util.js","statSize":19697}],"parsedSize":0,"gzipSize":0},{"label":"vue-loader/dist","path":"./../node_modules/vue-loader/dist","statSize":328,"groups":[{"id":7433,"label":"exportHelper.js","path":"./../node_modules/vue-loader/dist/exportHelper.js","statSize":328}],"parsedSize":0,"gzipSize":0},{"label":"vue-style-loader/lib","path":"./../node_modules/vue-style-loader/lib","statSize":6869,"groups":[{"id":4825,"label":"addStylesClient.js + 1 modules (concatenated)","path":"./../node_modules/vue-style-loader/lib/addStylesClient.js + 1 modules (concatenated)","statSize":6869,"concatenated":true,"groups":[{"label":"../node_modules/vue-style-loader/lib","path":"./../node_modules/vue-style-loader/lib/addStylesClient.js + 1 modules (concatenated)/../node_modules/vue-style-loader/lib","statSize":6869,"groups":[{"id":null,"label":"addStylesClient.js","path":"./../node_modules/vue-style-loader/lib/addStylesClient.js + 1 modules (concatenated)/../node_modules/vue-style-loader/lib/addStylesClient.js","statSize":6232,"inaccurateSizes":true},{"id":null,"label":"listToStyles.js","path":"./../node_modules/vue-style-loader/lib/addStylesClient.js + 1 modules (concatenated)/../node_modules/vue-style-loader/lib/listToStyles.js","statSize":637,"inaccurateSizes":true}],"inaccurateSizes":true}]}],"parsedSize":0,"gzipSize":0},{"label":"which-typed-array","path":"./../node_modules/which-typed-array","statSize":3647,"groups":[{"id":2730,"label":"index.js","path":"./../node_modules/which-typed-array/index.js","statSize":3647}],"parsedSize":0,"gzipSize":0}],"parsedSize":104788,"gzipSize":29911},{"label":"pages","path":"./pages","statSize":13269,"groups":[{"id":2662,"label":"Dashboard.vue?vue&type=style&index=0&id=471e0416&lang=scss&scoped=true","path":"./pages/Dashboard.vue?vue&type=style&index=0&id=471e0416&lang=scss&scoped=true","statSize":4974},{"id":6989,"label":"KubeDoomGame.vue?vue&type=style&index=0&id=6d118c02&lang=scss&scoped=true","path":"./pages/KubeDoomGame.vue?vue&type=style&index=0&id=6d118c02&lang=scss&scoped=true","statSize":8295}],"parsedSize":0,"gzipSize":0},{"id":8330,"label":"package.json","path":"./package.json","statSize":511},{"id":3100,"label":"product.ts","path":"./product.ts","statSize":542}]}];
      window.defaultSizes = "parsed";
    </script>
  </body>
</html>