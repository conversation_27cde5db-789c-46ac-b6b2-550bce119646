(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t(require("vue")):"function"===typeof define&&define.amd?define([],t):"object"===typeof exports?exports["kubedoom-extension-0.1.0"]=t(require("vue")):e["kubedoom-extension-0.1.0"]=t(e["Vue"])})("undefined"!==typeof self?self:this,(function(e){return function(){var t={528:function(e,t,n){"use strict";var r,o=n(9629),a=n(9838),i=n(9110),c=n(1155),s=n(4943),u=n(5731),l=n(3468),f=n(2140),p=n(8479),d=n(8449),y=n(8129),m=n(2387),g=n(5865),h=n(1319),b=n(6882),v=Function,w=function(e){try{return v('"use strict"; return ('+e+").constructor;")()}catch(t){}},x=n(9336),E=n(4940),k=function(){throw new l},S=x?function(){try{return k}catch(e){try{return x(arguments,"callee").get}catch(t){return k}}}():k,j=n(3558)(),O=n(6369),A=n(7345),N=n(7859),P=n(6095),D=n(4531),R={},T="undefined"!==typeof Uint8Array&&O?O(Uint8Array):r,C={__proto__:null,"%AggregateError%":"undefined"===typeof AggregateError?r:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"===typeof ArrayBuffer?r:ArrayBuffer,"%ArrayIteratorPrototype%":j&&O?O([][Symbol.iterator]()):r,"%AsyncFromSyncIteratorPrototype%":r,"%AsyncFunction%":R,"%AsyncGenerator%":R,"%AsyncGeneratorFunction%":R,"%AsyncIteratorPrototype%":R,"%Atomics%":"undefined"===typeof Atomics?r:Atomics,"%BigInt%":"undefined"===typeof BigInt?r:BigInt,"%BigInt64Array%":"undefined"===typeof BigInt64Array?r:BigInt64Array,"%BigUint64Array%":"undefined"===typeof BigUint64Array?r:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"===typeof DataView?r:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":a,"%eval%":eval,"%EvalError%":i,"%Float16Array%":"undefined"===typeof Float16Array?r:Float16Array,"%Float32Array%":"undefined"===typeof Float32Array?r:Float32Array,"%Float64Array%":"undefined"===typeof Float64Array?r:Float64Array,"%FinalizationRegistry%":"undefined"===typeof FinalizationRegistry?r:FinalizationRegistry,"%Function%":v,"%GeneratorFunction%":R,"%Int8Array%":"undefined"===typeof Int8Array?r:Int8Array,"%Int16Array%":"undefined"===typeof Int16Array?r:Int16Array,"%Int32Array%":"undefined"===typeof Int32Array?r:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":j&&O?O(O([][Symbol.iterator]())):r,"%JSON%":"object"===typeof JSON?JSON:r,"%Map%":"undefined"===typeof Map?r:Map,"%MapIteratorPrototype%":"undefined"!==typeof Map&&j&&O?O((new Map)[Symbol.iterator]()):r,"%Math%":Math,"%Number%":Number,"%Object%":o,"%Object.getOwnPropertyDescriptor%":x,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"===typeof Promise?r:Promise,"%Proxy%":"undefined"===typeof Proxy?r:Proxy,"%RangeError%":c,"%ReferenceError%":s,"%Reflect%":"undefined"===typeof Reflect?r:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"===typeof Set?r:Set,"%SetIteratorPrototype%":"undefined"!==typeof Set&&j&&O?O((new Set)[Symbol.iterator]()):r,"%SharedArrayBuffer%":"undefined"===typeof SharedArrayBuffer?r:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":j&&O?O(""[Symbol.iterator]()):r,"%Symbol%":j?Symbol:r,"%SyntaxError%":u,"%ThrowTypeError%":S,"%TypedArray%":T,"%TypeError%":l,"%Uint8Array%":"undefined"===typeof Uint8Array?r:Uint8Array,"%Uint8ClampedArray%":"undefined"===typeof Uint8ClampedArray?r:Uint8ClampedArray,"%Uint16Array%":"undefined"===typeof Uint16Array?r:Uint16Array,"%Uint32Array%":"undefined"===typeof Uint32Array?r:Uint32Array,"%URIError%":f,"%WeakMap%":"undefined"===typeof WeakMap?r:WeakMap,"%WeakRef%":"undefined"===typeof WeakRef?r:WeakRef,"%WeakSet%":"undefined"===typeof WeakSet?r:WeakSet,"%Function.prototype.call%":D,"%Function.prototype.apply%":P,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":A,"%Math.abs%":p,"%Math.floor%":d,"%Math.max%":y,"%Math.min%":m,"%Math.pow%":g,"%Math.round%":h,"%Math.sign%":b,"%Reflect.getPrototypeOf%":N};if(O)try{null.error}catch(H){var V=O(O(H));C["%Error.prototype%"]=V}var I=function e(t){var n;if("%AsyncFunction%"===t)n=w("async function () {}");else if("%GeneratorFunction%"===t)n=w("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=w("async function* () {}");else if("%AsyncGenerator%"===t){var r=e("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&O&&(n=O(o.prototype))}return C[t]=n,n},F={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=n(9138),B=n(8554),_=M.call(D,Array.prototype.concat),U=M.call(P,Array.prototype.splice),q=M.call(D,String.prototype.replace),$=M.call(D,String.prototype.slice),z=M.call(D,RegExp.prototype.exec),L=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,K=/\\(\\)?/g,G=function(e){var t=$(e,0,1),n=$(e,-1);if("%"===t&&"%"!==n)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var r=[];return q(e,L,(function(e,t,n,o){r[r.length]=n?q(o,K,"$1"):t||e})),r},W=function(e,t){var n,r=e;if(B(F,r)&&(n=F[r],r="%"+n[0]+"%"),B(C,r)){var o=C[r];if(o===R&&(o=I(r)),"undefined"===typeof o&&!t)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!==typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!==typeof t)throw new l('"allowMissing" argument must be a boolean');if(null===z(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=G(e),r=n.length>0?n[0]:"",o=W("%"+r+"%",t),a=o.name,i=o.value,c=!1,s=o.alias;s&&(r=s[0],U(n,_([0,1],s)));for(var f=1,p=!0;f<n.length;f+=1){var d=n[f],y=$(d,0,1),m=$(d,-1);if(('"'===y||"'"===y||"`"===y||'"'===m||"'"===m||"`"===m)&&y!==m)throw new u("property names with quotes must have matching quotes");if("constructor"!==d&&p||(c=!0),r+="."+d,a="%"+r+"%",B(C,a))i=C[a];else if(null!=i){if(!(d in i)){if(!t)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(x&&f+1>=n.length){var g=x(i,d);p=!!g,i=p&&"get"in g&&!("originalValue"in g.get)?g.get:i[d]}else p=B(i,d),i=i[d];p&&!c&&(C[a]=i)}}return i}},686:function(e,t,n){"use strict";var r=n(4940),o=n(5731),a=n(3468),i=n(9336);e.exports=function(e,t,n){if(!e||"object"!==typeof e&&"function"!==typeof e)throw new a("`obj` must be an object or a function`");if("string"!==typeof t&&"symbol"!==typeof t)throw new a("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!==typeof arguments[3]&&null!==arguments[3])throw new a("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!==typeof arguments[4]&&null!==arguments[4])throw new a("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!==typeof arguments[5]&&null!==arguments[5])throw new a("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!==typeof arguments[6])throw new a("`loose`, if provided, must be a boolean");var c=arguments.length>3?arguments[3]:null,s=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,l=arguments.length>6&&arguments[6],f=!!i&&i(e,t);if(r)r(e,t,{configurable:null===u&&f?f.configurable:!u,enumerable:null===c&&f?f.enumerable:!c,value:n,writable:null===s&&f?f.writable:!s});else{if(!l&&(c||s||u))throw new o("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=n}}},705:function(e,t,n){"use strict";var r=n(9617),o=Object.prototype.toString,a=Object.prototype.hasOwnProperty,i=function(e,t,n){for(var r=0,o=e.length;r<o;r++)a.call(e,r)&&(null==n?t(e[r],r,e):t.call(n,e[r],r,e))},c=function(e,t,n){for(var r=0,o=e.length;r<o;r++)null==n?t(e.charAt(r),r,e):t.call(n,e.charAt(r),r,e)},s=function(e,t,n){for(var r in e)a.call(e,r)&&(null==n?t(e[r],r,e):t.call(n,e[r],r,e))};function u(e){return"[object Array]"===o.call(e)}e.exports=function(e,t,n){if(!r(t))throw new TypeError("iterator must be a function");var o;arguments.length>=3&&(o=n),u(e)?i(e,t,o):"string"===typeof e?c(e,t,o):s(e,t,o)}},935:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r="undefined"!==typeof t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,a){"string"===typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var c=0;c<this.length;c++){var s=this[c][0];null!=s&&(i[s]=!0)}for(var u=0;u<e.length;u++){var l=[].concat(e[u]);r&&i[l[0]]||("undefined"!==typeof a&&("undefined"===typeof l[5]||(l[1]="@layer".concat(l[5].length>0?" ".concat(l[5]):""," {").concat(l[1],"}")),l[5]=a),n&&(l[2]?(l[1]="@media ".concat(l[2]," {").concat(l[1],"}"),l[2]=n):l[2]=n),o&&(l[4]?(l[1]="@supports (".concat(l[4],") {").concat(l[1],"}"),l[4]=o):l[4]="".concat(o)),t.push(l))}},t}},968:function(e){"use strict";var t=Object.prototype.toString;e.exports=function(e){var n=t.call(e),r="[object Arguments]"===n;return r||(r="[object Array]"!==n&&null!==e&&"object"===typeof e&&"number"===typeof e.length&&e.length>=0&&"[object Function]"===t.call(e.callee)),r}},1155:function(e){"use strict";e.exports=RangeError},1292:function(e){"use strict";e.exports=Object.getOwnPropertyDescriptor},1319:function(e){"use strict";e.exports=Math.round},1342:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,i(r.key),r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e){var t=c(e,"string");return"symbol"===r(t)?t:String(t)}function c(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function f(e){var t=y();return function(){var n,r=m(e);if(t){var o=m(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return p(this,n)}}function p(e,t){if(t&&("object"===r(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return d(e)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}var g,h,b={};function v(e,t,n){function r(e,n,r){return"string"===typeof t?t:t(e,n,r)}n||(n=Error);var o=function(t){u(o,t);var n=f(o);function o(t,a,i){var c;return s(this,o),c=n.call(this,r(t,a,i)),c.code=e,c}return a(o)}(n);b[e]=o}function w(e,t){if(Array.isArray(e)){var n=e.length;return e=e.map((function(e){return String(e)})),n>2?"one of ".concat(t," ").concat(e.slice(0,n-1).join(", "),", or ")+e[n-1]:2===n?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}return"of ".concat(t," ").concat(String(e))}function x(e,t,n){return e.substr(!n||n<0?0:+n,t.length)===t}function E(e,t,n){return(void 0===n||n>e.length)&&(n=e.length),e.substring(n-t.length,n)===t}function k(e,t,n){return"number"!==typeof n&&(n=0),!(n+t.length>e.length)&&-1!==e.indexOf(t,n)}v("ERR_AMBIGUOUS_ARGUMENT",'The "%s" argument is ambiguous. %s',TypeError),v("ERR_INVALID_ARG_TYPE",(function(e,t,o){var a,i;if(void 0===g&&(g=n(6093)),g("string"===typeof e,"'name' must be a string"),"string"===typeof t&&x(t,"not ")?(a="must not be",t=t.replace(/^not /,"")):a="must be",E(e," argument"))i="The ".concat(e," ").concat(a," ").concat(w(t,"type"));else{var c=k(e,".")?"property":"argument";i='The "'.concat(e,'" ').concat(c," ").concat(a," ").concat(w(t,"type"))}return i+=". Received type ".concat(r(o)),i}),TypeError),v("ERR_INVALID_ARG_VALUE",(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"is invalid";void 0===h&&(h=n(9208));var o=h.inspect(t);return o.length>128&&(o="".concat(o.slice(0,128),"...")),"The argument '".concat(e,"' ").concat(r,". Received ").concat(o)}),TypeError,RangeError),v("ERR_INVALID_RETURN_VALUE",(function(e,t,n){var o;return o=n&&n.constructor&&n.constructor.name?"instance of ".concat(n.constructor.name):"type ".concat(r(n)),"Expected ".concat(e,' to be returned from the "').concat(t,'"')+" function but got ".concat(o,".")}),TypeError),v("ERR_MISSING_ARGS",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];void 0===g&&(g=n(6093)),g(t.length>0,"At least one arg needs to be specified");var o="The ",a=t.length;switch(t=t.map((function(e){return'"'.concat(e,'"')})),a){case 1:o+="".concat(t[0]," argument");break;case 2:o+="".concat(t[0]," and ").concat(t[1]," arguments");break;default:o+=t.slice(0,a-1).join(", "),o+=", and ".concat(t[a-1]," arguments");break}return"".concat(o," must be specified")}),TypeError),e.exports.codes=b},1531:function(e,t,n){"use strict";var r=n(5387),o=n(2625),a=n(2730),i=n(5943);function c(e){return e.call.bind(e)}var s="undefined"!==typeof BigInt,u="undefined"!==typeof Symbol,l=c(Object.prototype.toString),f=c(Number.prototype.valueOf),p=c(String.prototype.valueOf),d=c(Boolean.prototype.valueOf);if(s)var y=c(BigInt.prototype.valueOf);if(u)var m=c(Symbol.prototype.valueOf);function g(e,t){if("object"!==typeof e)return!1;try{return t(e),!0}catch(n){return!1}}function h(e){return"undefined"!==typeof Promise&&e instanceof Promise||null!==e&&"object"===typeof e&&"function"===typeof e.then&&"function"===typeof e.catch}function b(e){return"undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):i(e)||q(e)}function v(e){return"Uint8Array"===a(e)}function w(e){return"Uint8ClampedArray"===a(e)}function x(e){return"Uint16Array"===a(e)}function E(e){return"Uint32Array"===a(e)}function k(e){return"Int8Array"===a(e)}function S(e){return"Int16Array"===a(e)}function j(e){return"Int32Array"===a(e)}function O(e){return"Float32Array"===a(e)}function A(e){return"Float64Array"===a(e)}function N(e){return"BigInt64Array"===a(e)}function P(e){return"BigUint64Array"===a(e)}function D(e){return"[object Map]"===l(e)}function R(e){return"undefined"!==typeof Map&&(D.working?D(e):e instanceof Map)}function T(e){return"[object Set]"===l(e)}function C(e){return"undefined"!==typeof Set&&(T.working?T(e):e instanceof Set)}function V(e){return"[object WeakMap]"===l(e)}function I(e){return"undefined"!==typeof WeakMap&&(V.working?V(e):e instanceof WeakMap)}function F(e){return"[object WeakSet]"===l(e)}function M(e){return F(e)}function B(e){return"[object ArrayBuffer]"===l(e)}function _(e){return"undefined"!==typeof ArrayBuffer&&(B.working?B(e):e instanceof ArrayBuffer)}function U(e){return"[object DataView]"===l(e)}function q(e){return"undefined"!==typeof DataView&&(U.working?U(e):e instanceof DataView)}t.isArgumentsObject=r,t.isGeneratorFunction=o,t.isTypedArray=i,t.isPromise=h,t.isArrayBufferView=b,t.isUint8Array=v,t.isUint8ClampedArray=w,t.isUint16Array=x,t.isUint32Array=E,t.isInt8Array=k,t.isInt16Array=S,t.isInt32Array=j,t.isFloat32Array=O,t.isFloat64Array=A,t.isBigInt64Array=N,t.isBigUint64Array=P,D.working="undefined"!==typeof Map&&D(new Map),t.isMap=R,T.working="undefined"!==typeof Set&&T(new Set),t.isSet=C,V.working="undefined"!==typeof WeakMap&&V(new WeakMap),t.isWeakMap=I,F.working="undefined"!==typeof WeakSet&&F(new WeakSet),t.isWeakSet=M,B.working="undefined"!==typeof ArrayBuffer&&B(new ArrayBuffer),t.isArrayBuffer=_,U.working="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof DataView&&U(new DataView(new ArrayBuffer(1),0,1)),t.isDataView=q;var $="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:void 0;function z(e){return"[object SharedArrayBuffer]"===l(e)}function L(e){return"undefined"!==typeof $&&("undefined"===typeof z.working&&(z.working=z(new $)),z.working?z(e):e instanceof $)}function K(e){return"[object AsyncFunction]"===l(e)}function G(e){return"[object Map Iterator]"===l(e)}function W(e){return"[object Set Iterator]"===l(e)}function H(e){return"[object Generator]"===l(e)}function J(e){return"[object WebAssembly.Module]"===l(e)}function Y(e){return g(e,f)}function X(e){return g(e,p)}function Z(e){return g(e,d)}function Q(e){return s&&g(e,y)}function ee(e){return u&&g(e,m)}function te(e){return Y(e)||X(e)||Z(e)||Q(e)||ee(e)}function ne(e){return"undefined"!==typeof Uint8Array&&(_(e)||L(e))}t.isSharedArrayBuffer=L,t.isAsyncFunction=K,t.isMapIterator=G,t.isSetIterator=W,t.isGeneratorObject=H,t.isWebAssemblyCompiledModule=J,t.isNumberObject=Y,t.isStringObject=X,t.isBooleanObject=Z,t.isBigIntObject=Q,t.isSymbolObject=ee,t.isBoxedPrimitive=te,t.isAnyArrayBuffer=ne,["isProxy","isExternal","isModuleNamespaceObject"].forEach((function(e){Object.defineProperty(t,e,{enumerable:!1,value:function(){throw new Error(e+" is not supported in userland")}})}))},1591:function(e,t,n){"use strict";var r=n(8006);e.exports=function(){return Number.isNaN&&Number.isNaN(NaN)&&!Number.isNaN("a")?Number.isNaN:r}},1641:function(e,t,n){"use strict";var r=n(1857),o=n(1591);e.exports=function(){var e=o();return r(Number,{isNaN:e},{isNaN:function(){return Number.isNaN!==e}}),e}},1857:function(e,t,n){"use strict";var r=n(9228),o="function"===typeof Symbol&&"symbol"===typeof Symbol("foo"),a=Object.prototype.toString,i=Array.prototype.concat,c=n(686),s=function(e){return"function"===typeof e&&"[object Function]"===a.call(e)},u=n(7239)(),l=function(e,t,n,r){if(t in e)if(!0===r){if(e[t]===n)return}else if(!s(r)||!r())return;u?c(e,t,n,!0):c(e,t,n)},f=function(e,t){var n=arguments.length>2?arguments[2]:{},a=r(t);o&&(a=i.call(a,Object.getOwnPropertySymbols(t)));for(var c=0;c<a.length;c+=1)l(e,a[c],t[a[c]],n[a[c]])};f.supportsDescriptors=!!u,e.exports=f},1913:function(e,t,n){"use strict";var r=n(2908);e.exports=function(){return r()&&!!Symbol.toStringTag}},1937:function(e,t,n){"use strict";var r=n(2372);e.exports=function(){return"function"===typeof Object.is?Object.is:r}},2012:function(e,t,n){"use strict";var r=n(9138),o=n(6095),a=n(8165);e.exports=function(){return a(r,o,arguments)}},2140:function(e){"use strict";e.exports=URIError},2372:function(e){"use strict";var t=function(e){return e!==e};e.exports=function(e,n){return 0===e&&0===n?1/e===1/n:e===n||!(!t(e)||!t(n))}},2387:function(e){"use strict";e.exports=Math.min},2422:function(e){"use strict";e.exports=Number.isNaN||function(e){return e!==e}},2625:function(e,t,n){"use strict";var r,o=n(4607),a=n(6132),i=a(/^\s*(?:function)?\*/),c=n(1913)(),s=n(6369),u=o("Object.prototype.toString"),l=o("Function.prototype.toString"),f=function(){if(!c)return!1;try{return Function("return function*() {}")()}catch(e){}};e.exports=function(e){if("function"!==typeof e)return!1;if(i(l(e)))return!0;if(!c){var t=u(e);return"[object GeneratorFunction]"===t}if(!s)return!1;if("undefined"===typeof r){var n=f();r=!!n&&s(n)}return s(e)===r}},2662:function(e,t,n){"use strict";n.r(t);var r=n(6758),o=n.n(r),a=n(935),i=n.n(a),c=i()(o());c.push([e.id,".kubedoom-dashboard[data-v-471e0416]{padding:20px;max-width:1200px;margin:0 auto}.kubedoom-dashboard .header[data-v-471e0416]{text-align:center;margin-bottom:40px}.kubedoom-dashboard .header .title[data-v-471e0416]{font-size:3rem;color:#ff6b6b;margin-bottom:10px}.kubedoom-dashboard .header .title .icon[data-v-471e0416]{margin-right:15px}.kubedoom-dashboard .header .subtitle[data-v-471e0416]{font-size:1.2rem;color:#666}.kubedoom-dashboard .content[data-v-471e0416]{display:grid;grid-template-columns:1fr 400px;gap:40px}@media(max-width:768px){.kubedoom-dashboard .content[data-v-471e0416]{grid-template-columns:1fr}}.kubedoom-dashboard .info-section .info-card[data-v-471e0416]{background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:20px;margin-bottom:20px}.kubedoom-dashboard .info-section .info-card h3[data-v-471e0416]{color:#333;margin-bottom:15px}.kubedoom-dashboard .info-section .info-card ul[data-v-471e0416]{margin:0;padding-left:20px}.kubedoom-dashboard .info-section .info-card .warning[data-v-471e0416]{background:#fff3cd;border:1px solid #ffeaa7;border-radius:4px;padding:15px;color:#856404}.kubedoom-dashboard .info-section .info-card .warning .icon[data-v-471e0416]{margin-right:8px;color:#f39c12}.kubedoom-dashboard .actions[data-v-471e0416]{background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:20px;height:-moz-fit-content;height:fit-content}.kubedoom-dashboard .actions .cluster-selector[data-v-471e0416],.kubedoom-dashboard .actions .namespace-selector[data-v-471e0416]{margin-bottom:20px}.kubedoom-dashboard .actions .cluster-selector label[data-v-471e0416],.kubedoom-dashboard .actions .namespace-selector label[data-v-471e0416]{display:block;margin-bottom:5px;font-weight:600}.kubedoom-dashboard .actions .cluster-selector select[data-v-471e0416],.kubedoom-dashboard .actions .namespace-selector select[data-v-471e0416]{width:100%;padding:8px 12px;border:1px solid #ddd;border-radius:4px;font-size:14px}.kubedoom-dashboard .actions .pod-info[data-v-471e0416]{margin-bottom:30px;padding:15px;background:#f8f9fa;border-radius:4px}.kubedoom-dashboard .actions .pod-info h4[data-v-471e0416]{margin-bottom:15px;color:#333}.kubedoom-dashboard .actions .pod-info .stats .stat[data-v-471e0416]{display:flex;justify-content:space-between;margin-bottom:8px}.kubedoom-dashboard .actions .pod-info .stats .stat .label[data-v-471e0416]{font-weight:500}.kubedoom-dashboard .actions .pod-info .stats .stat .value[data-v-471e0416]{font-weight:600;color:#007bff}.kubedoom-dashboard .actions .game-controls .btn[data-v-471e0416]{width:100%;margin-bottom:10px;padding:12px;font-size:16px;font-weight:600;border-radius:6px;border:none;cursor:pointer;transition:all .2s}.kubedoom-dashboard .actions .game-controls .btn .icon[data-v-471e0416]{margin-right:8px}.kubedoom-dashboard .actions .game-controls .btn.btn-primary[data-v-471e0416]{background:#ff6b6b;color:#fff}.kubedoom-dashboard .actions .game-controls .btn.btn-primary[data-v-471e0416]:hover:not(:disabled){background:#ff5252}.kubedoom-dashboard .actions .game-controls .btn.btn-primary[data-v-471e0416]:disabled{background:#ccc;cursor:not-allowed}.kubedoom-dashboard .actions .game-controls .btn.btn-secondary[data-v-471e0416]{background:#6c757d;color:#fff}.kubedoom-dashboard .actions .game-controls .btn.btn-secondary[data-v-471e0416]:hover:not(:disabled){background:#5a6268}.kubedoom-dashboard .actions .game-controls .btn.btn-secondary[data-v-471e0416]:disabled{background:#ccc;cursor:not-allowed}",""]),t["default"]=c},2672:function(e,t,n){"use strict";var r,o=n(4607),a=n(1913)(),i=n(8554),c=n(9336);if(a){var s=o("RegExp.prototype.exec"),u={},l=function(){throw u},f={toString:l,valueOf:l};"symbol"===typeof Symbol.toPrimitive&&(f[Symbol.toPrimitive]=l),r=function(e){if(!e||"object"!==typeof e)return!1;var t=c(e,"lastIndex"),n=t&&i(t,"value");if(!n)return!1;try{s(e,f)}catch(r){return r===u}}}else{var p=o("Object.prototype.toString"),d="[object RegExp]";r=function(e){return!(!e||"object"!==typeof e&&"function"!==typeof e)&&p(e)===d}}e.exports=r},2730:function(e,t,n){"use strict";var r=n(705),o=n(4834),a=n(8498),i=n(4607),c=n(9336),s=n(6369),u=i("Object.prototype.toString"),l=n(1913)(),f="undefined"===typeof globalThis?n.g:globalThis,p=o(),d=i("String.prototype.slice"),y=i("Array.prototype.indexOf",!0)||function(e,t){for(var n=0;n<e.length;n+=1)if(e[n]===t)return n;return-1},m={__proto__:null};r(p,l&&c&&s?function(e){var t=new f[e];if(Symbol.toStringTag in t&&s){var n=s(t),r=c(n,Symbol.toStringTag);if(!r&&n){var o=s(n);r=c(o,Symbol.toStringTag)}m["$"+e]=a(r.get)}}:function(e){var t=new f[e],n=t.slice||t.set;n&&(m["$"+e]=a(n))});var g=function(e){var t=!1;return r(m,(function(n,r){if(!t)try{"$"+n(e)===r&&(t=d(r,1))}catch(o){}})),t},h=function(e){var t=!1;return r(m,(function(n,r){if(!t)try{n(e),t=d(r,1)}catch(o){}})),t};e.exports=function(e){if(!e||"object"!==typeof e)return!1;if(!l){var t=d(u(e),8,-1);return y(p,t)>-1?t:"Object"===t&&h(e)}return c?g(e):null}},2908:function(e){"use strict";e.exports=function(){if("function"!==typeof Symbol||"function"!==typeof Object.getOwnPropertySymbols)return!1;if("symbol"===typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),n=Object(t);if("string"===typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;var r=42;for(var o in e[t]=r,e)return!1;if("function"===typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"===typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var a=Object.getOwnPropertySymbols(e);if(1!==a.length||a[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"===typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(i.value!==r||!0!==i.enumerable)return!1}return!0}},3100:function(e,t,n){"use strict";function r(e,t){const n="kubedoom",r="_",{product:o}=e.DSL(t,n);o({icon:"icon-kubedoom",inStore:"management",weight:100,to:{name:`${n}-c-cluster-dashboard`,path:`/${n}/c/:cluster/dashboard`,params:{product:n,cluster:r,pkg:n}}})}n.r(t),n.d(t,{init:function(){return r}})},3225:function(e,t,n){"use strict";var r=n(5164),o=function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),n={},r=0;r<t.length;++r)n[t[r]]=t[r];var o=Object.assign({},n),a="";for(var i in o)a+=i;return e!==a},a=function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1};e.exports=function(){return Object.assign?o()||a()?r:Object.assign:r}},3468:function(e){"use strict";e.exports=TypeError},3558:function(e,t,n){"use strict";var r="undefined"!==typeof Symbol&&Symbol,o=n(2908);e.exports=function(){return"function"===typeof r&&("function"===typeof Symbol&&("symbol"===typeof r("foo")&&("symbol"===typeof Symbol("bar")&&o())))}},4246:function(e,t,n){var r=n(2662);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var o=n(4825).A;o("049e2472",r,!0,{sourceMap:!1,shadowMode:!1})},4364:function(e,t,n){var r=n(9208),o=n(6093);function a(){return(new Date).getTime()}var i,c=Array.prototype.slice,s={};i="undefined"!==typeof n.g&&n.g.console?n.g.console:"undefined"!==typeof window&&window.console?window.console:{};for(var u=[[y,"log"],[m,"info"],[g,"warn"],[h,"error"],[b,"time"],[v,"timeEnd"],[w,"trace"],[x,"dir"],[E,"assert"]],l=0;l<u.length;l++){var f=u[l],p=f[0],d=f[1];i[d]||(i[d]=p)}function y(){}function m(){i.log.apply(i,arguments)}function g(){i.log.apply(i,arguments)}function h(){i.warn.apply(i,arguments)}function b(e){s[e]=a()}function v(e){var t=s[e];if(!t)throw new Error("No such label: "+e);delete s[e];var n=a()-t;i.log(e+": "+n+"ms")}function w(){var e=new Error;e.name="Trace",e.message=r.format.apply(null,arguments),i.error(e.stack)}function x(e){i.log(r.inspect(e)+"\n")}function E(e){if(!e){var t=c.call(arguments,1);o.ok(!1,r.format.apply(null,t))}}e.exports=i},4531:function(e){"use strict";e.exports=Function.prototype.call},4607:function(e,t,n){"use strict";var r=n(528),o=n(9903),a=o([r("%String.prototype.indexOf%")]);e.exports=function(e,t){var n=r(e,!!t);return"function"===typeof n&&a(e,".prototype.")>-1?o([n]):n}},4825:function(e,t,n){"use strict";function r(e,t){for(var n=[],r={},o=0;o<t.length;o++){var a=t[o],i=a[0],c=a[1],s=a[2],u=a[3],l={id:e+":"+o,css:c,media:s,sourceMap:u};r[i]?r[i].parts.push(l):n.push(r[i]={id:i,parts:[l]})}return n}n.d(t,{A:function(){return y}});var o="undefined"!==typeof document;if("undefined"!==typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},i=o&&(document.head||document.getElementsByTagName("head")[0]),c=null,s=0,u=!1,l=function(){},f=null,p="data-vue-ssr-id",d="undefined"!==typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function y(e,t,n,o){u=n,f=o||{};var i=r(e,t);return m(i),function(t){for(var n=[],o=0;o<i.length;o++){var c=i[o],s=a[c.id];s.refs--,n.push(s)}t?(i=r(e,t),m(i)):i=[];for(o=0;o<n.length;o++){s=n[o];if(0===s.refs){for(var u=0;u<s.parts.length;u++)s.parts[u]();delete a[s.id]}}}}function m(e){for(var t=0;t<e.length;t++){var n=e[t],r=a[n.id];if(r){r.refs++;for(var o=0;o<r.parts.length;o++)r.parts[o](n.parts[o]);for(;o<n.parts.length;o++)r.parts.push(h(n.parts[o]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var i=[];for(o=0;o<n.parts.length;o++)i.push(h(n.parts[o]));a[n.id]={id:n.id,refs:1,parts:i}}}}function g(){var e=document.createElement("style");return e.type="text/css",i.appendChild(e),e}function h(e){var t,n,r=document.querySelector("style["+p+'~="'+e.id+'"]');if(r){if(u)return l;r.parentNode.removeChild(r)}if(d){var o=s++;r=c||(c=g()),t=v.bind(null,r,o,!1),n=v.bind(null,r,o,!0)}else r=g(),t=w.bind(null,r),n=function(){r.parentNode.removeChild(r)};return t(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;t(e=r)}else n()}}var b=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function v(e,t,n,r){var o=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=b(t,o);else{var a=document.createTextNode(o),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(a,i[t]):e.appendChild(a)}}function w(e,t){var n=t.css,r=t.media,o=t.sourceMap;if(r&&e.setAttribute("media",r),f.ssrId&&e.setAttribute(p,t.id),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{while(e.firstChild)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},4834:function(e,t,n){"use strict";var r=n(9501),o="undefined"===typeof globalThis?n.g:globalThis;e.exports=function(){for(var e=[],t=0;t<r.length;t++)"function"===typeof o[r[t]]&&(e[e.length]=r[t]);return e}},4940:function(e){"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(n){t=!1}e.exports=t},4943:function(e){"use strict";e.exports=ReferenceError},5087:function(e,t,n){"use strict";var r=n(1937),o=n(1857);e.exports=function(){var e=r();return o(Object,{is:e},{is:function(){return Object.is!==e}}),e}},5164:function(e,t,n){"use strict";var r=n(9228),o=n(2908)(),a=n(4607),i=n(9629),c=a("Array.prototype.push"),s=a("Object.prototype.propertyIsEnumerable"),u=o?i.getOwnPropertySymbols:null;e.exports=function(e,t){if(null==e)throw new TypeError("target must be an object");var n=i(e);if(1===arguments.length)return n;for(var a=1;a<arguments.length;++a){var l=i(arguments[a]),f=r(l),p=o&&(i.getOwnPropertySymbols||u);if(p)for(var d=p(l),y=0;y<d.length;++y){var m=d[y];s(l,m)&&c(f,m)}for(var g=0;g<f.length;++g){var h=f[g];if(s(l,h)){var b=l[h];n[h]=b}}}return n}},5272:function(e){e.exports=function(e){return e&&"object"===typeof e&&"function"===typeof e.copy&&"function"===typeof e.fill&&"function"===typeof e.readUInt8}},5387:function(e,t,n){"use strict";var r=n(1913)(),o=n(4607),a=o("Object.prototype.toString"),i=function(e){return!(r&&e&&"object"===typeof e&&Symbol.toStringTag in e)&&"[object Arguments]"===a(e)},c=function(e){return!!i(e)||null!==e&&"object"===typeof e&&"length"in e&&"number"===typeof e.length&&e.length>=0&&"[object Array]"!==a(e)&&"callee"in e&&"[object Function]"===a(e.callee)},s=function(){return i(arguments)}();i.isLegacyArguments=c,e.exports=s?i:c},5615:function(e){"function"===typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},5656:function(e,t,n){"use strict";function r(e,t){return s(e)||c(e,t)||a(e,t)||o()}function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(e,t){if(e){if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function c(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],s=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);s=!0);}catch(e){u=!0,o=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}function s(e){if(Array.isArray(e))return e}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}var l=void 0!==/a/g.flags,f=function(e){var t=[];return e.forEach((function(e){return t.push(e)})),t},p=function(e){var t=[];return e.forEach((function(e,n){return t.push([n,e])})),t},d=Object.is?Object.is:n(5968),y=Object.getOwnPropertySymbols?Object.getOwnPropertySymbols:function(){return[]},m=Number.isNaN?Number.isNaN:n(7838);function g(e){return e.call.bind(e)}var h=g(Object.prototype.hasOwnProperty),b=g(Object.prototype.propertyIsEnumerable),v=g(Object.prototype.toString),w=n(9208).types,x=w.isAnyArrayBuffer,E=w.isArrayBufferView,k=w.isDate,S=w.isMap,j=w.isRegExp,O=w.isSet,A=w.isNativeError,N=w.isBoxedPrimitive,P=w.isNumberObject,D=w.isStringObject,R=w.isBooleanObject,T=w.isBigIntObject,C=w.isSymbolObject,V=w.isFloat32Array,I=w.isFloat64Array;function F(e){if(0===e.length||e.length>10)return!0;for(var t=0;t<e.length;t++){var n=e.charCodeAt(t);if(n<48||n>57)return!0}return 10===e.length&&e>=Math.pow(2,32)}function M(e){return Object.keys(e).filter(F).concat(y(e).filter(Object.prototype.propertyIsEnumerable.bind(e)))}
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */function B(e,t){if(e===t)return 0;for(var n=e.length,r=t.length,o=0,a=Math.min(n,r);o<a;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0}var _=void 0,U=!0,q=!1,$=0,z=1,L=2,K=3;function G(e,t){return l?e.source===t.source&&e.flags===t.flags:RegExp.prototype.toString.call(e)===RegExp.prototype.toString.call(t)}function W(e,t){if(e.byteLength!==t.byteLength)return!1;for(var n=0;n<e.byteLength;n++)if(e[n]!==t[n])return!1;return!0}function H(e,t){return e.byteLength===t.byteLength&&0===B(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),new Uint8Array(t.buffer,t.byteOffset,t.byteLength))}function J(e,t){return e.byteLength===t.byteLength&&0===B(new Uint8Array(e),new Uint8Array(t))}function Y(e,t){return P(e)?P(t)&&d(Number.prototype.valueOf.call(e),Number.prototype.valueOf.call(t)):D(e)?D(t)&&String.prototype.valueOf.call(e)===String.prototype.valueOf.call(t):R(e)?R(t)&&Boolean.prototype.valueOf.call(e)===Boolean.prototype.valueOf.call(t):T(e)?T(t)&&BigInt.prototype.valueOf.call(e)===BigInt.prototype.valueOf.call(t):C(t)&&Symbol.prototype.valueOf.call(e)===Symbol.prototype.valueOf.call(t)}function X(e,t,n,r){if(e===t)return 0!==e||(!n||d(e,t));if(n){if("object"!==u(e))return"number"===typeof e&&m(e)&&m(t);if("object"!==u(t)||null===e||null===t)return!1;if(Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1}else{if(null===e||"object"!==u(e))return(null===t||"object"!==u(t))&&e==t;if(null===t||"object"!==u(t))return!1}var o=v(e),a=v(t);if(o!==a)return!1;if(Array.isArray(e)){if(e.length!==t.length)return!1;var i=M(e,_),c=M(t,_);return i.length===c.length&&Q(e,t,n,r,z,i)}if("[object Object]"===o&&(!S(e)&&S(t)||!O(e)&&O(t)))return!1;if(k(e)){if(!k(t)||Date.prototype.getTime.call(e)!==Date.prototype.getTime.call(t))return!1}else if(j(e)){if(!j(t)||!G(e,t))return!1}else if(A(e)||e instanceof Error){if(e.message!==t.message||e.name!==t.name)return!1}else{if(E(e)){if(n||!V(e)&&!I(e)){if(!H(e,t))return!1}else if(!W(e,t))return!1;var s=M(e,_),l=M(t,_);return s.length===l.length&&Q(e,t,n,r,$,s)}if(O(e))return!(!O(t)||e.size!==t.size)&&Q(e,t,n,r,L);if(S(e))return!(!S(t)||e.size!==t.size)&&Q(e,t,n,r,K);if(x(e)){if(!J(e,t))return!1}else if(N(e)&&!Y(e,t))return!1}return Q(e,t,n,r,$)}function Z(e,t){return t.filter((function(t){return b(e,t)}))}function Q(e,t,n,r,o,a){if(5===arguments.length){a=Object.keys(e);var i=Object.keys(t);if(a.length!==i.length)return!1}for(var c=0;c<a.length;c++)if(!h(t,a[c]))return!1;if(n&&5===arguments.length){var s=y(e);if(0!==s.length){var u=0;for(c=0;c<s.length;c++){var l=s[c];if(b(e,l)){if(!b(t,l))return!1;a.push(l),u++}else if(b(t,l))return!1}var f=y(t);if(s.length!==f.length&&Z(t,f).length!==u)return!1}else{var p=y(t);if(0!==p.length&&0!==Z(t,p).length)return!1}}if(0===a.length&&(o===$||o===z&&0===e.length||0===e.size))return!0;if(void 0===r)r={val1:new Map,val2:new Map,position:0};else{var d=r.val1.get(e);if(void 0!==d){var m=r.val2.get(t);if(void 0!==m)return d===m}r.position++}r.val1.set(e,r.position),r.val2.set(t,r.position);var g=ce(e,t,n,a,r,o);return r.val1.delete(e),r.val2.delete(t),g}function ee(e,t,n,r){for(var o=f(e),a=0;a<o.length;a++){var i=o[a];if(X(t,i,n,r))return e.delete(i),!0}return!1}function te(e){switch(u(e)){case"undefined":return null;case"object":return;case"symbol":return!1;case"string":e=+e;case"number":if(m(e))return!1}return!0}function ne(e,t,n){var r=te(n);return null!=r?r:t.has(r)&&!e.has(r)}function re(e,t,n,r,o){var a=te(n);if(null!=a)return a;var i=t.get(a);return!(void 0===i&&!t.has(a)||!X(r,i,!1,o))&&(!e.has(a)&&X(r,i,!1,o))}function oe(e,t,n,r){for(var o=null,a=f(e),i=0;i<a.length;i++){var c=a[i];if("object"===u(c)&&null!==c)null===o&&(o=new Set),o.add(c);else if(!t.has(c)){if(n)return!1;if(!ne(e,t,c))return!1;null===o&&(o=new Set),o.add(c)}}if(null!==o){for(var s=f(t),l=0;l<s.length;l++){var p=s[l];if("object"===u(p)&&null!==p){if(!ee(o,p,n,r))return!1}else if(!n&&!e.has(p)&&!ee(o,p,n,r))return!1}return 0===o.size}return!0}function ae(e,t,n,r,o,a){for(var i=f(e),c=0;c<i.length;c++){var s=i[c];if(X(n,s,o,a)&&X(r,t.get(s),o,a))return e.delete(s),!0}return!1}function ie(e,t,n,o){for(var a=null,i=p(e),c=0;c<i.length;c++){var s=r(i[c],2),l=s[0],f=s[1];if("object"===u(l)&&null!==l)null===a&&(a=new Set),a.add(l);else{var d=t.get(l);if(void 0===d&&!t.has(l)||!X(f,d,n,o)){if(n)return!1;if(!re(e,t,l,f,o))return!1;null===a&&(a=new Set),a.add(l)}}}if(null!==a){for(var y=p(t),m=0;m<y.length;m++){var g=r(y[m],2),h=g[0],b=g[1];if("object"===u(h)&&null!==h){if(!ae(a,e,h,b,n,o))return!1}else if(!n&&(!e.has(h)||!X(e.get(h),b,!1,o))&&!ae(a,e,h,b,!1,o))return!1}return 0===a.size}return!0}function ce(e,t,n,r,o,a){var i=0;if(a===L){if(!oe(e,t,n,o))return!1}else if(a===K){if(!ie(e,t,n,o))return!1}else if(a===z)for(;i<e.length;i++){if(!h(e,i)){if(h(t,i))return!1;for(var c=Object.keys(e);i<c.length;i++){var s=c[i];if(!h(t,s)||!X(e[s],t[s],n,o))return!1}return c.length===Object.keys(t).length}if(!h(t,i)||!X(e[i],t[i],n,o))return!1}for(i=0;i<r.length;i++){var u=r[i];if(!X(e[u],t[u],n,o))return!1}return!0}function se(e,t){return X(e,t,q)}function ue(e,t){return X(e,t,U)}e.exports={isDeepEqual:se,isDeepStrictEqual:ue}},5731:function(e){"use strict";e.exports=SyntaxError},5865:function(e){"use strict";e.exports=Math.pow},5943:function(e,t,n){"use strict";var r=n(2730);e.exports=function(e){return!!r(e)}},5968:function(e,t,n){"use strict";var r=n(1857),o=n(8498),a=n(2372),i=n(1937),c=n(5087),s=o(i(),Object);r(s,{getPolyfill:i,implementation:a,shim:c}),e.exports=s},6093:function(e,t,n){"use strict";var r=n(9907),o=n(4364);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,s(r.key),r)}}function c(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var t=u(e,"string");return"symbol"===a(t)?t:String(t)}function u(e,t){if("object"!==a(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var f,p,d=n(1342),y=d.codes,m=y.ERR_AMBIGUOUS_ARGUMENT,g=y.ERR_INVALID_ARG_TYPE,h=y.ERR_INVALID_ARG_VALUE,b=y.ERR_INVALID_RETURN_VALUE,v=y.ERR_MISSING_ARGS,w=n(9801),x=n(9208),E=x.inspect,k=n(9208).types,S=k.isPromise,j=k.isRegExp,O=n(3225)(),A=n(1937)(),N=n(9818)("RegExp.prototype.test");new Map;function P(){var e=n(5656);f=e.isDeepEqual,p=e.isDeepStrictEqual}var D=!1,R=e.exports=F,T={};function C(e){if(e.message instanceof Error)throw e.message;throw new w(e)}function V(e,t,n,a,i){var c,s=arguments.length;if(0===s)c="Failed";else if(1===s)n=e,e=void 0;else{if(!1===D){D=!0;var u=r.emitWarning?r.emitWarning:o.warn.bind(o);u("assert.fail() with more than one argument is deprecated. Please use assert.strictEqual() instead or only pass a message.","DeprecationWarning","DEP0094")}2===s&&(a="!=")}if(n instanceof Error)throw n;var l={actual:e,expected:t,operator:void 0===a?"fail":a,stackStartFn:i||V};void 0!==n&&(l.message=n);var f=new w(l);throw c&&(f.message=c,f.generatedMessage=!0),f}function I(e,t,n,r){if(!n){var o=!1;if(0===t)o=!0,r="No value argument passed to `assert.ok()`";else if(r instanceof Error)throw r;var a=new w({actual:n,expected:!0,message:r,operator:"==",stackStartFn:e});throw a.generatedMessage=o,a}}function F(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];I.apply(void 0,[F,t.length].concat(t))}function M(e,t,n){if(arguments.length<2)throw new v("actual","expected");void 0===f&&P(),p(e,t)&&C({actual:e,expected:t,message:n,operator:"notDeepStrictEqual",stackStartFn:M})}R.fail=V,R.AssertionError=w,R.ok=F,R.equal=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");t!=n&&C({actual:t,expected:n,message:r,operator:"==",stackStartFn:e})},R.notEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");t==n&&C({actual:t,expected:n,message:r,operator:"!=",stackStartFn:e})},R.deepEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");void 0===f&&P(),f(t,n)||C({actual:t,expected:n,message:r,operator:"deepEqual",stackStartFn:e})},R.notDeepEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");void 0===f&&P(),f(t,n)&&C({actual:t,expected:n,message:r,operator:"notDeepEqual",stackStartFn:e})},R.deepStrictEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");void 0===f&&P(),p(t,n)||C({actual:t,expected:n,message:r,operator:"deepStrictEqual",stackStartFn:e})},R.notDeepStrictEqual=M,R.strictEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");A(t,n)||C({actual:t,expected:n,message:r,operator:"strictEqual",stackStartFn:e})},R.notStrictEqual=function e(t,n,r){if(arguments.length<2)throw new v("actual","expected");A(t,n)&&C({actual:t,expected:n,message:r,operator:"notStrictEqual",stackStartFn:e})};var B=c((function e(t,n,r){var o=this;l(this,e),n.forEach((function(e){e in t&&(void 0!==r&&"string"===typeof r[e]&&j(t[e])&&N(t[e],r[e])?o[e]=r[e]:o[e]=t[e])}))}));function _(e,t,n,r,o,a){if(!(n in e)||!p(e[n],t[n])){if(!r){var i=new B(e,o),c=new B(t,o,e),s=new w({actual:i,expected:c,operator:"deepStrictEqual",stackStartFn:a});throw s.actual=e,s.expected=t,s.operator=a.name,s}C({actual:e,expected:t,message:r,operator:a.name,stackStartFn:a})}}function U(e,t,n,r){if("function"!==typeof t){if(j(t))return N(t,e);if(2===arguments.length)throw new g("expected",["Function","RegExp"],t);if("object"!==a(e)||null===e){var o=new w({actual:e,expected:t,message:n,operator:"deepStrictEqual",stackStartFn:r});throw o.operator=r.name,o}var i=Object.keys(t);if(t instanceof Error)i.push("name","message");else if(0===i.length)throw new h("error",t,"may not be an empty object");return void 0===f&&P(),i.forEach((function(o){"string"===typeof e[o]&&j(t[o])&&N(t[o],e[o])||_(e,t,o,n,i,r)})),!0}return void 0!==t.prototype&&e instanceof t||!Error.isPrototypeOf(t)&&!0===t.call({},e)}function q(e){if("function"!==typeof e)throw new g("fn","Function",e);try{e()}catch(t){return t}return T}function $(e){return S(e)||null!==e&&"object"===a(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function z(e){return Promise.resolve().then((function(){var t;if("function"===typeof e){if(t=e(),!$(t))throw new b("instance of Promise","promiseFn",t)}else{if(!$(e))throw new g("promiseFn",["Function","Promise"],e);t=e}return Promise.resolve().then((function(){return t})).then((function(){return T})).catch((function(e){return e}))}))}function L(e,t,n,r){if("string"===typeof n){if(4===arguments.length)throw new g("error",["Object","Error","Function","RegExp"],n);if("object"===a(t)&&null!==t){if(t.message===n)throw new m("error/message",'The error message "'.concat(t.message,'" is identical to the message.'))}else if(t===n)throw new m("error/message",'The error "'.concat(t,'" is identical to the message.'));r=n,n=void 0}else if(null!=n&&"object"!==a(n)&&"function"!==typeof n)throw new g("error",["Object","Error","Function","RegExp"],n);if(t===T){var o="";n&&n.name&&(o+=" (".concat(n.name,")")),o+=r?": ".concat(r):".";var i="rejects"===e.name?"rejection":"exception";C({actual:void 0,expected:n,operator:e.name,message:"Missing expected ".concat(i).concat(o),stackStartFn:e})}if(n&&!U(t,n,r,e))throw t}function K(e,t,n,r){if(t!==T){if("string"===typeof n&&(r=n,n=void 0),!n||U(t,n)){var o=r?": ".concat(r):".",a="doesNotReject"===e.name?"rejection":"exception";C({actual:t,expected:n,operator:e.name,message:"Got unwanted ".concat(a).concat(o,"\n")+'Actual message: "'.concat(t&&t.message,'"'),stackStartFn:e})}throw t}}function G(e,t,n,r,o){if(!j(t))throw new g("regexp","RegExp",t);var i="match"===o;if("string"!==typeof e||N(t,e)!==i){if(n instanceof Error)throw n;var c=!n;n=n||("string"!==typeof e?'The "string" argument must be of type string. Received type '+"".concat(a(e)," (").concat(E(e),")"):(i?"The input did not match the regular expression ":"The input was expected to not match the regular expression ")+"".concat(E(t),". Input:\n\n").concat(E(e),"\n"));var s=new w({actual:e,expected:t,message:n,operator:o,stackStartFn:r});throw s.generatedMessage=c,s}}function W(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];I.apply(void 0,[W,t.length].concat(t))}R.throws=function e(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];L.apply(void 0,[e,q(t)].concat(r))},R.rejects=function e(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return z(t).then((function(t){return L.apply(void 0,[e,t].concat(r))}))},R.doesNotThrow=function e(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];K.apply(void 0,[e,q(t)].concat(r))},R.doesNotReject=function e(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return z(t).then((function(t){return K.apply(void 0,[e,t].concat(r))}))},R.ifError=function e(t){if(null!==t&&void 0!==t){var n="ifError got unwanted exception: ";"object"===a(t)&&"string"===typeof t.message?0===t.message.length&&t.constructor?n+=t.constructor.name:n+=t.message:n+=E(t);var r=new w({actual:t,expected:null,operator:"ifError",message:n,stackStartFn:e}),o=t.stack;if("string"===typeof o){var i=o.split("\n");i.shift();for(var c=r.stack.split("\n"),s=0;s<i.length;s++){var u=c.indexOf(i[s]);if(-1!==u){c=c.slice(0,u);break}}r.stack="".concat(c.join("\n"),"\n").concat(i.join("\n"))}throw r}},R.match=function e(t,n,r){G(t,n,r,e,"match")},R.doesNotMatch=function e(t,n,r){G(t,n,r,e,"doesNotMatch")},R.strict=O(W,R,{equal:R.strictEqual,deepEqual:R.deepStrictEqual,notEqual:R.notStrictEqual,notDeepEqual:R.notDeepStrictEqual}),R.strict.strict=R.strict},6095:function(e){"use strict";e.exports=Function.prototype.apply},6108:function(e,t,n){"use strict";var r=n(528),o=n(686),a=n(7239)(),i=n(9336),c=n(3468),s=r("%Math.floor%");e.exports=function(e,t){if("function"!==typeof e)throw new c("`fn` is not a function");if("number"!==typeof t||t<0||t>4294967295||s(t)!==t)throw new c("`length` must be a positive 32-bit integer");var n=arguments.length>2&&!!arguments[2],r=!0,u=!0;if("length"in e&&i){var l=i(e,"length");l&&!l.configurable&&(r=!1),l&&!l.writable&&(u=!1)}return(r||u||!n)&&(a?o(e,"length",t,!0,!0):o(e,"length",t)),e}},6132:function(e,t,n){"use strict";var r=n(4607),o=n(2672),a=r("RegExp.prototype.exec"),i=n(3468);e.exports=function(e){if(!o(e))throw new i("`regex` must be a RegExp");return function(t){return null!==a(e,t)}}},6369:function(e,t,n){"use strict";var r=n(7859),o=n(7345),a=n(6423);e.exports=r?function(e){return r(e)}:o?function(e){if(!e||"object"!==typeof e&&"function"!==typeof e)throw new TypeError("getProto: not an object");return o(e)}:a?function(e){return a(e)}:null},6381:function(e,t,n){var r=n(6989);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var o=n(4825).A;o("4cc6ea3b",r,!0,{sourceMap:!1,shadowMode:!1})},6423:function(e,t,n){"use strict";var r,o=n(9903),a=n(9336);try{r=[].__proto__===Array.prototype}catch(u){if(!u||"object"!==typeof u||!("code"in u)||"ERR_PROTO_ACCESS"!==u.code)throw u}var i=!!r&&a&&a(Object.prototype,"__proto__"),c=Object,s=c.getPrototypeOf;e.exports=i&&"function"===typeof i.get?o([i.get]):"function"===typeof s&&function(e){return s(null==e?e:c(e))}},6758:function(e){"use strict";e.exports=function(e){return e[1]}},6882:function(e,t,n){"use strict";var r=n(2422);e.exports=function(e){return r(e)||0===e?e:e<0?-1:1}},6989:function(e,t,n){"use strict";n.r(t);var r=n(6758),o=n.n(r),a=n(935),i=n.n(a),c=i()(o());c.push([e.id,".kubedoom-game[data-v-6d118c02]{height:100vh;display:flex;flex-direction:column;background:#1a1a1a;color:#fff}.kubedoom-game .game-header[data-v-6d118c02]{display:flex;justify-content:space-between;align-items:center;padding:15px 20px;background:#2d2d2d;border-bottom:1px solid #444}.kubedoom-game .game-header .header-center[data-v-6d118c02]{text-align:center}.kubedoom-game .game-header .header-center h2[data-v-6d118c02]{margin:0 0 5px 0;color:#ff6b6b}.kubedoom-game .game-header .header-center .status[data-v-6d118c02]{font-size:14px}.kubedoom-game .game-header .header-center .status.connected[data-v-6d118c02]{color:#28a745}.kubedoom-game .game-header .header-center .status.connecting[data-v-6d118c02]{color:#ffc107}.kubedoom-game .game-header .header-center .status.error[data-v-6d118c02]{color:#dc3545}.kubedoom-game .game-header .header-center .status .icon[data-v-6d118c02]{margin-right:5px}.kubedoom-game .game-header .btn[data-v-6d118c02]{padding:8px 16px;border:none;border-radius:4px;cursor:pointer;font-weight:500}.kubedoom-game .game-header .btn.btn-secondary[data-v-6d118c02]{background:#6c757d;color:#fff}.kubedoom-game .game-header .btn.btn-danger[data-v-6d118c02]{background:#dc3545;color:#fff}.kubedoom-game .game-header .btn .icon[data-v-6d118c02]{margin-right:5px}.kubedoom-game .game-content[data-v-6d118c02]{flex:1;display:grid;grid-template-columns:1fr 300px;gap:20px;padding:20px}.kubedoom-game .game-content .game-area[data-v-6d118c02]{display:flex;flex-direction:column;gap:20px}.kubedoom-game .game-content .game-area .vnc-container[data-v-6d118c02]{position:relative;background:#000;border:2px solid #444;border-radius:8px;overflow:hidden;height:600px}.kubedoom-game .game-content .game-area .vnc-container .connection-overlay[data-v-6d118c02]{position:absolute;top:0;left:0;right:0;bottom:0;display:flex;flex-direction:column;justify-content:center;align-items:center;background:rgba(0,0,0,.8);z-index:10}.kubedoom-game .game-content .game-area .vnc-container .connection-overlay .spinner[data-v-6d118c02]{width:40px;height:40px;border:4px solid #333;border-top:4px solid #ff6b6b;border-radius:50%;animation:spin-6d118c02 1s linear infinite;margin-bottom:20px}.kubedoom-game .game-content .game-area .vnc-container .connection-overlay p[data-v-6d118c02]{color:#ccc;font-size:16px}.kubedoom-game .game-content .game-area .vnc-container canvas[data-v-6d118c02]{width:100%;height:100%;cursor:crosshair}.kubedoom-game .game-content .game-area .game-controls[data-v-6d118c02]{display:grid;grid-template-columns:1fr 1fr;gap:20px}.kubedoom-game .game-content .game-area .game-controls .control-group[data-v-6d118c02]{background:#2d2d2d;padding:15px;border-radius:8px}.kubedoom-game .game-content .game-area .game-controls .control-group h4[data-v-6d118c02]{margin:0 0 15px 0;color:#ff6b6b}.kubedoom-game .game-content .game-area .game-controls .control-group .controls-grid[data-v-6d118c02]{display:grid;grid-template-columns:1fr 1fr;gap:10px}.kubedoom-game .game-content .game-area .game-controls .control-group .controls-grid .control-item[data-v-6d118c02]{display:flex;justify-content:space-between;align-items:center;padding:5px 0}.kubedoom-game .game-content .game-area .game-controls .control-group .controls-grid .control-item .key[data-v-6d118c02]{background:#444;padding:2px 8px;border-radius:4px;font-family:monospace;font-size:12px}.kubedoom-game .game-content .game-area .game-controls .control-group .controls-grid .control-item .desc[data-v-6d118c02]{font-size:12px;color:#ccc}.kubedoom-game .game-content .game-area .game-controls .control-group .cheat-buttons[data-v-6d118c02]{display:flex;flex-direction:column;gap:8px}.kubedoom-game .game-content .game-area .game-controls .control-group .cheat-buttons .btn[data-v-6d118c02]{padding:6px 12px;background:#444;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:12px}.kubedoom-game .game-content .game-area .game-controls .control-group .cheat-buttons .btn[data-v-6d118c02]:hover{background:#555}.kubedoom-game .game-content .pod-monitor[data-v-6d118c02]{background:#2d2d2d;padding:20px;border-radius:8px;height:-moz-fit-content;height:fit-content}.kubedoom-game .game-content .pod-monitor h3[data-v-6d118c02]{margin:0 0 20px 0;color:#ff6b6b}.kubedoom-game .game-content .pod-monitor .monitor-stats[data-v-6d118c02]{margin-bottom:25px}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat[data-v-6d118c02]{display:flex;justify-content:space-between;margin-bottom:10px}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat .label[data-v-6d118c02]{color:#ccc}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat .value[data-v-6d118c02]{font-weight:600}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat .value.killed[data-v-6d118c02]{color:#dc3545}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat .value.remaining[data-v-6d118c02]{color:#28a745}.kubedoom-game .game-content .pod-monitor .monitor-stats .stat .value.respawned[data-v-6d118c02]{color:#17a2b8}.kubedoom-game .game-content .pod-monitor .recent-kills[data-v-6d118c02]{margin-bottom:25px}.kubedoom-game .game-content .pod-monitor .recent-kills h4[data-v-6d118c02]{margin:0 0 15px 0;color:#ffc107}.kubedoom-game .game-content .pod-monitor .recent-kills .kill-list[data-v-6d118c02]{max-height:200px;overflow-y:auto}.kubedoom-game .game-content .pod-monitor .recent-kills .kill-list .kill-item[data-v-6d118c02]{display:flex;flex-direction:column;padding:8px;margin-bottom:8px;background:#1a1a1a;border-radius:4px;font-size:12px}.kubedoom-game .game-content .pod-monitor .recent-kills .kill-list .kill-item .pod-name[data-v-6d118c02]{font-weight:600;color:#ff6b6b}.kubedoom-game .game-content .pod-monitor .recent-kills .kill-list .kill-item .namespace[data-v-6d118c02]{color:#17a2b8}.kubedoom-game .game-content .pod-monitor .recent-kills .kill-list .kill-item .time[data-v-6d118c02]{color:#6c757d}.kubedoom-game .game-content .pod-monitor .cluster-health h4[data-v-6d118c02]{margin:0 0 15px 0;color:#28a745}.kubedoom-game .game-content .pod-monitor .cluster-health .health-indicator[data-v-6d118c02]{padding:10px;border-radius:4px;text-align:center;font-weight:500}.kubedoom-game .game-content .pod-monitor .cluster-health .health-indicator.healthy[data-v-6d118c02]{background:rgba(40,167,69,.2);color:#28a745}.kubedoom-game .game-content .pod-monitor .cluster-health .health-indicator.warning[data-v-6d118c02]{background:rgba(255,193,7,.2);color:#ffc107}.kubedoom-game .game-content .pod-monitor .cluster-health .health-indicator.critical[data-v-6d118c02]{background:rgba(220,53,69,.2);color:#dc3545}.kubedoom-game .game-content .pod-monitor .cluster-health .health-indicator .icon[data-v-6d118c02]{margin-right:5px}@keyframes spin-6d118c02{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",""]),t["default"]=c},7196:function(e){"use strict";e.exports="undefined"!==typeof Reflect&&Reflect&&Reflect.apply},7239:function(e,t,n){"use strict";var r=n(4940),o=function(){return!!r};o.hasArrayLengthDefineBug=function(){if(!r)return null;try{return 1!==r([],"length",{value:1}).length}catch(e){return!0}},e.exports=o},7345:function(e,t,n){"use strict";var r=n(9629);e.exports=r.getPrototypeOf||null},7433:function(e,t){"use strict";t.A=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n}},7838:function(e,t,n){"use strict";var r=n(8498),o=n(1857),a=n(8006),i=n(1591),c=n(1641),s=r(i(),Number);o(s,{getPolyfill:i,implementation:a,shim:c}),e.exports=s},7859:function(e){"use strict";e.exports="undefined"!==typeof Reflect&&Reflect.getPrototypeOf||null},8006:function(e){"use strict";e.exports=function(e){return e!==e}},8129:function(e){"use strict";e.exports=Math.max},8160:function(e,t,n){"use strict";var r;if(!Object.keys){var o=Object.prototype.hasOwnProperty,a=Object.prototype.toString,i=n(968),c=Object.prototype.propertyIsEnumerable,s=!c.call({toString:null},"toString"),u=c.call((function(){}),"prototype"),l=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],f=function(e){var t=e.constructor;return t&&t.prototype===e},p={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},d=function(){if("undefined"===typeof window)return!1;for(var e in window)try{if(!p["$"+e]&&o.call(window,e)&&null!==window[e]&&"object"===typeof window[e])try{f(window[e])}catch(t){return!0}}catch(t){return!0}return!1}(),y=function(e){if("undefined"===typeof window||!d)return f(e);try{return f(e)}catch(t){return!1}};r=function(e){var t=null!==e&&"object"===typeof e,n="[object Function]"===a.call(e),r=i(e),c=t&&"[object String]"===a.call(e),f=[];if(!t&&!n&&!r)throw new TypeError("Object.keys called on a non-object");var p=u&&n;if(c&&e.length>0&&!o.call(e,0))for(var d=0;d<e.length;++d)f.push(String(d));if(r&&e.length>0)for(var m=0;m<e.length;++m)f.push(String(m));else for(var g in e)p&&"prototype"===g||!o.call(e,g)||f.push(String(g));if(s)for(var h=y(e),b=0;b<l.length;++b)h&&"constructor"===l[b]||!o.call(e,l[b])||f.push(l[b]);return f}}e.exports=r},8165:function(e,t,n){"use strict";var r=n(9138),o=n(6095),a=n(4531),i=n(7196);e.exports=i||r.call(a,o)},8330:function(e){"use strict";e.exports=JSON.parse('{"name":"kubedoom-extension","description":"KubeDoom Extension - Play Doom to kill Kubernetes pods through Rancher UI","version":"0.1.0","private":false,"rancher":{"annotations":{"catalog.cattle.io/rancher-version":">= 2.10.0","catalog.cattle.io/ui-extensions-version":">= 3.0.0 < 4.0.0"}},"scripts":{},"engines":{"node":">=16"},"devDependencies":{"@vue/cli-plugin-babel":"~5.0.0","@vue/cli-service":"~5.0.0","@vue/cli-plugin-typescript":"~5.0.0"},"browserslist":["> 1%","last 2 versions","not dead"]}')},8449:function(e){"use strict";e.exports=Math.floor},8479:function(e){"use strict";e.exports=Math.abs},8498:function(e,t,n){"use strict";var r=n(6108),o=n(4940),a=n(9903),i=n(2012);e.exports=function(e){var t=a(arguments),n=e.length-(arguments.length-1);return r(t,1+(n>0?n:0),!0)},o?o(e.exports,"apply",{value:i}):e.exports.apply=i},8554:function(e,t,n){"use strict";var r=Function.prototype.call,o=Object.prototype.hasOwnProperty,a=n(9138);e.exports=a.call(r,o)},8794:function(e){"use strict";var t="Function.prototype.bind called on incompatible ",n=Object.prototype.toString,r=Math.max,o="[object Function]",a=function(e,t){for(var n=[],r=0;r<e.length;r+=1)n[r]=e[r];for(var o=0;o<t.length;o+=1)n[o+e.length]=t[o];return n},i=function(e,t){for(var n=[],r=t||0,o=0;r<e.length;r+=1,o+=1)n[o]=e[r];return n},c=function(e,t){for(var n="",r=0;r<e.length;r+=1)n+=e[r],r+1<e.length&&(n+=t);return n};e.exports=function(e){var s=this;if("function"!==typeof s||n.apply(s)!==o)throw new TypeError(t+s);for(var u,l=i(arguments,1),f=function(){if(this instanceof u){var t=s.apply(this,a(l,arguments));return Object(t)===t?t:this}return s.apply(e,a(l,arguments))},p=r(0,s.length-l.length),d=[],y=0;y<p;y++)d[y]="$"+y;if(u=Function("binder","return function ("+c(d,",")+"){ return binder.apply(this,arguments); }")(f),s.prototype){var m=function(){};m.prototype=s.prototype,u.prototype=new m,m.prototype=null}return u}},9110:function(e){"use strict";e.exports=EvalError},9138:function(e,t,n){"use strict";var r=n(8794);e.exports=Function.prototype.bind||r},9208:function(e,t,n){var r=n(9907),o=n(4364),a=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++)n[t[r]]=Object.getOwnPropertyDescriptor(e,t[r]);return n},i=/%[sdj%]/g;t.format=function(e){if(!j(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(l(arguments[n]));return t.join(" ")}n=1;for(var r=arguments,o=r.length,a=String(e).replace(i,(function(e){if("%%"===e)return"%";if(n>=o)return e;switch(e){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(t){return"[Circular]"}default:return e}})),c=r[n];n<o;c=r[++n])E(c)||!P(c)?a+=" "+c:a+=" "+l(c);return a},t.deprecate=function(e,n){if("undefined"!==typeof r&&!0===r.noDeprecation)return e;if("undefined"===typeof r)return function(){return t.deprecate(e,n).apply(this,arguments)};var a=!1;function i(){if(!a){if(r.throwDeprecation)throw new Error(n);r.traceDeprecation?o.trace(n):o.error(n),a=!0}return e.apply(this,arguments)}return i};var c={},s=/^$/;if({NODE_ENV:"production",BASE_URL:"/"}.NODE_DEBUG){var u={NODE_ENV:"production",BASE_URL:"/"}.NODE_DEBUG;u=u.replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^").toUpperCase(),s=new RegExp("^"+u+"$","i")}function l(e,n){var r={seen:[],stylize:p};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),x(n)?r.showHidden=n:n&&t._extend(r,n),A(r.showHidden)&&(r.showHidden=!1),A(r.depth)&&(r.depth=2),A(r.colors)&&(r.colors=!1),A(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=f),y(r,e,r.depth)}function f(e,t){var n=l.styles[t];return n?"["+l.colors[n][0]+"m"+e+"["+l.colors[n][1]+"m":e}function p(e,t){return e}function d(e){var t={};return e.forEach((function(e,n){t[e]=!0})),t}function y(e,n,r){if(e.customInspect&&n&&T(n.inspect)&&n.inspect!==t.inspect&&(!n.constructor||n.constructor.prototype!==n)){var o=n.inspect(r,e);return j(o)||(o=y(e,o,r)),o}var a=m(e,n);if(a)return a;var i=Object.keys(n),c=d(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(n)),R(n)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return g(n);if(0===i.length){if(T(n)){var s=n.name?": "+n.name:"";return e.stylize("[Function"+s+"]","special")}if(N(n))return e.stylize(RegExp.prototype.toString.call(n),"regexp");if(D(n))return e.stylize(Date.prototype.toString.call(n),"date");if(R(n))return g(n)}var u,l="",f=!1,p=["{","}"];if(w(n)&&(f=!0,p=["[","]"]),T(n)){var x=n.name?": "+n.name:"";l=" [Function"+x+"]"}return N(n)&&(l=" "+RegExp.prototype.toString.call(n)),D(n)&&(l=" "+Date.prototype.toUTCString.call(n)),R(n)&&(l=" "+g(n)),0!==i.length||f&&0!=n.length?r<0?N(n)?e.stylize(RegExp.prototype.toString.call(n),"regexp"):e.stylize("[Object]","special"):(e.seen.push(n),u=f?h(e,n,r,c,i):i.map((function(t){return b(e,n,r,c,t,f)})),e.seen.pop(),v(u,l,p)):p[0]+l+p[1]}function m(e,t){if(A(t))return e.stylize("undefined","undefined");if(j(t)){var n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}return S(t)?e.stylize(""+t,"number"):x(t)?e.stylize(""+t,"boolean"):E(t)?e.stylize("null","null"):void 0}function g(e){return"["+Error.prototype.toString.call(e)+"]"}function h(e,t,n,r,o){for(var a=[],i=0,c=t.length;i<c;++i)B(t,String(i))?a.push(b(e,t,n,r,String(i),!0)):a.push("");return o.forEach((function(o){o.match(/^\d+$/)||a.push(b(e,t,n,r,o,!0))})),a}function b(e,t,n,r,o,a){var i,c,s;if(s=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]},s.get?c=s.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):s.set&&(c=e.stylize("[Setter]","special")),B(r,o)||(i="["+o+"]"),c||(e.seen.indexOf(s.value)<0?(c=E(n)?y(e,s.value,null):y(e,s.value,n-1),c.indexOf("\n")>-1&&(c=a?c.split("\n").map((function(e){return"  "+e})).join("\n").slice(2):"\n"+c.split("\n").map((function(e){return"   "+e})).join("\n"))):c=e.stylize("[Circular]","special")),A(i)){if(a&&o.match(/^\d+$/))return c;i=JSON.stringify(""+o),i.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(i=i.slice(1,-1),i=e.stylize(i,"name")):(i=i.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),i=e.stylize(i,"string"))}return i+": "+c}function v(e,t,n){var r=e.reduce((function(e,t){return t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return r>60?n[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+n[1]:n[0]+t+" "+e.join(", ")+" "+n[1]}function w(e){return Array.isArray(e)}function x(e){return"boolean"===typeof e}function E(e){return null===e}function k(e){return null==e}function S(e){return"number"===typeof e}function j(e){return"string"===typeof e}function O(e){return"symbol"===typeof e}function A(e){return void 0===e}function N(e){return P(e)&&"[object RegExp]"===V(e)}function P(e){return"object"===typeof e&&null!==e}function D(e){return P(e)&&"[object Date]"===V(e)}function R(e){return P(e)&&("[object Error]"===V(e)||e instanceof Error)}function T(e){return"function"===typeof e}function C(e){return null===e||"boolean"===typeof e||"number"===typeof e||"string"===typeof e||"symbol"===typeof e||"undefined"===typeof e}function V(e){return Object.prototype.toString.call(e)}function I(e){return e<10?"0"+e.toString(10):e.toString(10)}t.debuglog=function(e){if(e=e.toUpperCase(),!c[e])if(s.test(e)){var n=r.pid;c[e]=function(){var r=t.format.apply(t,arguments);o.error("%s %d: %s",e,n,r)}}else c[e]=function(){};return c[e]},t.inspect=l,l.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},l.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},t.types=n(1531),t.isArray=w,t.isBoolean=x,t.isNull=E,t.isNullOrUndefined=k,t.isNumber=S,t.isString=j,t.isSymbol=O,t.isUndefined=A,t.isRegExp=N,t.types.isRegExp=N,t.isObject=P,t.isDate=D,t.types.isDate=D,t.isError=R,t.types.isNativeError=R,t.isFunction=T,t.isPrimitive=C,t.isBuffer=n(5272);var F=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function M(){var e=new Date,t=[I(e.getHours()),I(e.getMinutes()),I(e.getSeconds())].join(":");return[e.getDate(),F[e.getMonth()],t].join(" ")}function B(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){o.log("%s - %s",M(),t.format.apply(t,arguments))},t.inherits=n(5615),t._extend=function(e,t){if(!t||!P(t))return e;var n=Object.keys(t),r=n.length;while(r--)e[n[r]]=t[n[r]];return e};var _="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function U(e,t){if(!e){var n=new Error("Promise was rejected with a falsy value");n.reason=e,e=n}return t(e)}function q(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function t(){for(var t=[],n=0;n<arguments.length;n++)t.push(arguments[n]);var o=t.pop();if("function"!==typeof o)throw new TypeError("The last argument must be of type Function");var a=this,i=function(){return o.apply(a,arguments)};e.apply(this,t).then((function(e){r.nextTick(i.bind(null,null,e))}),(function(e){r.nextTick(U.bind(null,e,i))}))}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Object.defineProperties(t,a(e)),t}t.promisify=function(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');if(_&&e[_]){var t=e[_];if("function"!==typeof t)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,_,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,n,r=new Promise((function(e,r){t=e,n=r})),o=[],a=0;a<arguments.length;a++)o.push(arguments[a]);o.push((function(e,r){e?n(e):t(r)}));try{e.apply(this,o)}catch(i){n(i)}return r}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),_&&Object.defineProperty(t,_,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,a(e))},t.promisify.custom=_,t.callbackify=q},9228:function(e,t,n){"use strict";var r=Array.prototype.slice,o=n(968),a=Object.keys,i=a?function(e){return a(e)}:n(8160),c=Object.keys;i.shim=function(){if(Object.keys){var e=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);e||(Object.keys=function(e){return o(e)?c(r.call(e)):c(e)})}else Object.keys=i;return Object.keys||i},e.exports=i},9274:function(t){"use strict";t.exports=e},9336:function(e,t,n){"use strict";var r=n(1292);if(r)try{r([],"length")}catch(o){r=null}e.exports=r},9501:function(e){"use strict";e.exports=["Float16Array","Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"]},9617:function(e){"use strict";var t,n,r=Function.prototype.toString,o="object"===typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"===typeof o&&"function"===typeof Object.defineProperty)try{t=Object.defineProperty({},"length",{get:function(){throw n}}),n={},o((function(){throw 42}),null,t)}catch(v){v!==n&&(o=null)}else o=null;var a=/^\s*class\b/,i=function(e){try{var t=r.call(e);return a.test(t)}catch(n){return!1}},c=function(e){try{return!i(e)&&(r.call(e),!0)}catch(t){return!1}},s=Object.prototype.toString,u="[object Object]",l="[object Function]",f="[object GeneratorFunction]",p="[object HTMLAllCollection]",d="[object HTML document.all class]",y="[object HTMLCollection]",m="function"===typeof Symbol&&!!Symbol.toStringTag,g=!(0 in[,]),h=function(){return!1};if("object"===typeof document){var b=document.all;s.call(b)===s.call(document.all)&&(h=function(e){if((g||!e)&&("undefined"===typeof e||"object"===typeof e))try{var t=s.call(e);return(t===p||t===d||t===y||t===u)&&null==e("")}catch(n){}return!1})}e.exports=o?function(e){if(h(e))return!0;if(!e)return!1;if("function"!==typeof e&&"object"!==typeof e)return!1;try{o(e,null,t)}catch(r){if(r!==n)return!1}return!i(e)&&c(e)}:function(e){if(h(e))return!0;if(!e)return!1;if("function"!==typeof e&&"object"!==typeof e)return!1;if(m)return c(e);if(i(e))return!1;var t=s.call(e);return!(t!==l&&t!==f&&!/^\[object HTML/.test(t))&&c(e)}},9629:function(e){"use strict";e.exports=Object},9801:function(e,t,n){"use strict";var r=n(9907);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e,t,n){return t=l(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function u(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e){var t=f(e,"string");return"symbol"===E(t)?t:String(t)}function f(e,t){if("object"!==E(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function p(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&w(e,t)}function d(e){var t=b();return function(){var n,r=x(e);if(t){var o=x(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return y(this,n)}}function y(e,t){if(t&&("object"===E(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return m(e)}function m(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function g(e){var t="function"===typeof Map?new Map:void 0;return g=function(e){if(null===e||!v(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return h(e,arguments,x(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),w(n,e)},g(e)}function h(e,t,n){return h=b()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);var o=Function.bind.apply(e,r),a=new o;return n&&w(a,n.prototype),a},h.apply(null,arguments)}function b(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function v(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function w(e,t){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},w(e,t)}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}var k=n(9208),S=k.inspect,j=n(1342),O=j.codes.ERR_INVALID_ARG_TYPE;function A(e,t,n){return(void 0===n||n>e.length)&&(n=e.length),e.substring(n-t.length,n)===t}function N(e,t){if(t=Math.floor(t),0==e.length||0==t)return"";var n=e.length*t;t=Math.floor(Math.log(t)/Math.log(2));while(t)e+=e,t--;return e+=e.substring(0,n-e.length),e}var P="",D="",R="",T="",C={deepStrictEqual:"Expected values to be strictly deep-equal:",strictEqual:"Expected values to be strictly equal:",strictEqualObject:'Expected "actual" to be reference-equal to "expected":',deepEqual:"Expected values to be loosely deep-equal:",equal:"Expected values to be loosely equal:",notDeepStrictEqual:'Expected "actual" not to be strictly deep-equal to:',notStrictEqual:'Expected "actual" to be strictly unequal to:',notStrictEqualObject:'Expected "actual" not to be reference-equal to "expected":',notDeepEqual:'Expected "actual" not to be loosely deep-equal to:',notEqual:'Expected "actual" to be loosely unequal to:',notIdentical:"Values identical but not reference-equal:"},V=10;function I(e){var t=Object.keys(e),n=Object.create(Object.getPrototypeOf(e));return t.forEach((function(t){n[t]=e[t]})),Object.defineProperty(n,"message",{value:e.message}),n}function F(e){return S(e,{compact:!1,customInspect:!1,depth:1e3,maxArrayLength:1/0,showHidden:!1,breakLength:1/0,showProxy:!1,sorted:!0,getters:!0})}function M(e,t,n){var o="",a="",i=0,c="",s=!1,u=F(e),l=u.split("\n"),f=F(t).split("\n"),p=0,d="";if("strictEqual"===n&&"object"===E(e)&&"object"===E(t)&&null!==e&&null!==t&&(n="strictEqualObject"),1===l.length&&1===f.length&&l[0]!==f[0]){var y=l[0].length+f[0].length;if(y<=V){if(("object"!==E(e)||null===e)&&("object"!==E(t)||null===t)&&(0!==e||0!==t))return"".concat(C[n],"\n\n")+"".concat(l[0]," !== ").concat(f[0],"\n")}else if("strictEqualObject"!==n){var m=r.stderr&&r.stderr.isTTY?r.stderr.columns:80;if(y<m){while(l[0][p]===f[0][p])p++;p>2&&(d="\n  ".concat(N(" ",p),"^"),p=0)}}}var g=l[l.length-1],h=f[f.length-1];while(g===h){if(p++<2?c="\n  ".concat(g).concat(c):o=g,l.pop(),f.pop(),0===l.length||0===f.length)break;g=l[l.length-1],h=f[f.length-1]}var b=Math.max(l.length,f.length);if(0===b){var v=u.split("\n");if(v.length>30){v[26]="".concat(P,"...").concat(T);while(v.length>27)v.pop()}return"".concat(C.notIdentical,"\n\n").concat(v.join("\n"),"\n")}p>3&&(c="\n".concat(P,"...").concat(T).concat(c),s=!0),""!==o&&(c="\n  ".concat(o).concat(c),o="");var w=0,x=C[n]+"\n".concat(D,"+ actual").concat(T," ").concat(R,"- expected").concat(T),k=" ".concat(P,"...").concat(T," Lines skipped");for(p=0;p<b;p++){var S=p-i;if(l.length<p+1)S>1&&p>2&&(S>4?(a+="\n".concat(P,"...").concat(T),s=!0):S>3&&(a+="\n  ".concat(f[p-2]),w++),a+="\n  ".concat(f[p-1]),w++),i=p,o+="\n".concat(R,"-").concat(T," ").concat(f[p]),w++;else if(f.length<p+1)S>1&&p>2&&(S>4?(a+="\n".concat(P,"...").concat(T),s=!0):S>3&&(a+="\n  ".concat(l[p-2]),w++),a+="\n  ".concat(l[p-1]),w++),i=p,a+="\n".concat(D,"+").concat(T," ").concat(l[p]),w++;else{var j=f[p],O=l[p],I=O!==j&&(!A(O,",")||O.slice(0,-1)!==j);I&&A(j,",")&&j.slice(0,-1)===O&&(I=!1,O+=","),I?(S>1&&p>2&&(S>4?(a+="\n".concat(P,"...").concat(T),s=!0):S>3&&(a+="\n  ".concat(l[p-2]),w++),a+="\n  ".concat(l[p-1]),w++),i=p,a+="\n".concat(D,"+").concat(T," ").concat(O),o+="\n".concat(R,"-").concat(T," ").concat(j),w+=2):(a+=o,o="",1!==S&&0!==p||(a+="\n  ".concat(O),w++))}if(w>20&&p<b-2)return"".concat(x).concat(k,"\n").concat(a,"\n").concat(P,"...").concat(T).concat(o,"\n")+"".concat(P,"...").concat(T)}return"".concat(x).concat(s?k:"","\n").concat(a).concat(o).concat(c).concat(d)}var B=function(e,t){p(o,e);var n=d(o);function o(e){var t;if(c(this,o),"object"!==E(e)||null===e)throw new O("options","Object",e);var a=e.message,i=e.operator,s=e.stackStartFn,u=e.actual,l=e.expected,f=Error.stackTraceLimit;if(Error.stackTraceLimit=0,null!=a)t=n.call(this,String(a));else if(r.stderr&&r.stderr.isTTY&&(r.stderr&&r.stderr.getColorDepth&&1!==r.stderr.getColorDepth()?(P="[34m",D="[32m",T="[39m",R="[31m"):(P="",D="",T="",R="")),"object"===E(u)&&null!==u&&"object"===E(l)&&null!==l&&"stack"in u&&u instanceof Error&&"stack"in l&&l instanceof Error&&(u=I(u),l=I(l)),"deepStrictEqual"===i||"strictEqual"===i)t=n.call(this,M(u,l,i));else if("notDeepStrictEqual"===i||"notStrictEqual"===i){var p=C[i],d=F(u).split("\n");if("notStrictEqual"===i&&"object"===E(u)&&null!==u&&(p=C.notStrictEqualObject),d.length>30){d[26]="".concat(P,"...").concat(T);while(d.length>27)d.pop()}t=1===d.length?n.call(this,"".concat(p," ").concat(d[0])):n.call(this,"".concat(p,"\n\n").concat(d.join("\n"),"\n"))}else{var g=F(u),h="",b=C[i];"notDeepEqual"===i||"notEqual"===i?(g="".concat(C[i],"\n\n").concat(g),g.length>1024&&(g="".concat(g.slice(0,1021),"..."))):(h="".concat(F(l)),g.length>512&&(g="".concat(g.slice(0,509),"...")),h.length>512&&(h="".concat(h.slice(0,509),"...")),"deepEqual"===i||"equal"===i?g="".concat(b,"\n\n").concat(g,"\n\nshould equal\n\n"):h=" ".concat(i," ").concat(h)),t=n.call(this,"".concat(g).concat(h))}return Error.stackTraceLimit=f,t.generatedMessage=!a,Object.defineProperty(m(t),"name",{value:"AssertionError [ERR_ASSERTION]",enumerable:!1,writable:!0,configurable:!0}),t.code="ERR_ASSERTION",t.actual=u,t.expected=l,t.operator=i,Error.captureStackTrace&&Error.captureStackTrace(m(t),s),t.stack,t.name="AssertionError",y(t)}return u(o,[{key:"toString",value:function(){return"".concat(this.name," [").concat(this.code,"]: ").concat(this.message)}},{key:t,value:function(e,t){return S(this,a(a({},t),{},{customInspect:!1,depth:0}))}}]),o}(g(Error),S.custom);e.exports=B},9818:function(e,t,n){"use strict";var r=n(528),o=n(8498),a=o(r("String.prototype.indexOf"));e.exports=function(e,t){var n=r(e,!!t);return"function"===typeof n&&a(e,".prototype.")>-1?o(n):n}},9838:function(e){"use strict";e.exports=Error},9903:function(e,t,n){"use strict";var r=n(9138),o=n(3468),a=n(4531),i=n(8165);e.exports=function(e){if(e.length<1||"function"!==typeof e[0])throw new o("a function is required");return i(r,a,e)}},9907:function(e){var t,n,r=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function i(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}function c(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(function(){try{t="function"===typeof setTimeout?setTimeout:o}catch(e){t=o}try{n="function"===typeof clearTimeout?clearTimeout:a}catch(e){n=a}})();var s,u=[],l=!1,f=-1;function p(){l&&s&&(l=!1,s.length?u=s.concat(u):f=-1,u.length&&d())}function d(){if(!l){var e=i(p);l=!0;var t=u.length;while(t){s=u,u=[];while(++f<t)s&&s[f].run();f=-1,t=u.length}s=null,l=!1,c(e)}}function y(e,t){this.fun=e,this.array=t}function m(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new y(e,t)),1!==u.length||l||i(d)},y.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=m,r.addListener=m,r.once=m,r.off=m,r.removeListener=m,r.removeAllListeners=m,r.emit=m,r.prependListener=m,r.prependOnceListener=m,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var a=n[e]={id:e,exports:{}};return t[e](a,a.exports,r),a.exports}!function(){r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,{a:t}),t}}(),function(){r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}}(),function(){r.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){r.p=""}();var o={};return function(){"use strict";if(r.r(o),r.d(o,{default:function(){return de}}),"undefined"!==typeof window){var e=window.document.currentScript,t=e&&e.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);t&&(r.p=t[1])}function n(e){}var a=r(9274);const i={class:"kubedoom-dashboard"},c={class:"content"},s={class:"actions"},u={class:"cluster-selector"},l=["value"],f={key:0,class:"namespace-selector"},p=["value"],d={key:1,class:"pod-info"},y={class:"stats"},m={class:"stat"},g={class:"value"},h={class:"stat"},b={class:"value"},v={class:"stat"},w={class:"value"},x={class:"game-controls"},E=["disabled"],k=["disabled"];function S(e,t,n,r,o,S){return(0,a.openBlock)(),(0,a.createElementBlock)("div",i,[t[16]||(t[16]=(0,a.createElementVNode)("div",{class:"header"},[(0,a.createElementVNode)("h1",{class:"title"},[(0,a.createElementVNode)("i",{class:"icon icon-kubedoom"}),(0,a.createTextVNode)(" KubeDoom ")]),(0,a.createElementVNode)("p",{class:"subtitle"}," Kill Kubernetes pods by playing Doom! ")],-1)),(0,a.createElementVNode)("div",c,[t[15]||(t[15]=(0,a.createStaticVNode)('<div class="info-section" data-v-471e0416><div class="info-card" data-v-471e0416><h3 data-v-471e0416>What is KubeDoom?</h3><p data-v-471e0416> KubeDoom is a fun way to test the resilience of your Kubernetes cluster by playing the classic game Doom. Each enemy in the game represents a pod in your cluster, and killing them will actually delete the corresponding pod. </p></div><div class="info-card" data-v-471e0416><h3 data-v-471e0416>How it works</h3><ul data-v-471e0416><li data-v-471e0416>Each pod in your cluster appears as an enemy in Doom</li><li data-v-471e0416>Shooting enemies kills the corresponding pods</li><li data-v-471e0416>Watch how your applications handle pod failures</li><li data-v-471e0416>Test your cluster&#39;s resilience and recovery mechanisms</li></ul></div><div class="info-card" data-v-471e0416><h3 data-v-471e0416>Safety Notice</h3><div class="warning" data-v-471e0416><i class="icon icon-warning" data-v-471e0416></i><strong data-v-471e0416>Warning:</strong> This will actually delete pods in your cluster. Only use this in development or testing environments! </div></div></div>',1)),(0,a.createElementVNode)("div",s,[(0,a.createElementVNode)("div",u,[t[6]||(t[6]=(0,a.createElementVNode)("label",null,"Select Cluster:",-1)),(0,a.withDirectives)((0,a.createElementVNode)("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>o.selectedCluster=e),onChange:t[1]||(t[1]=(...e)=>S.onClusterChange&&S.onClusterChange(...e))},[t[5]||(t[5]=(0,a.createElementVNode)("option",{value:""},"Choose a cluster...",-1)),((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(o.clusters,(e=>((0,a.openBlock)(),(0,a.createElementBlock)("option",{key:e.id,value:e.id},(0,a.toDisplayString)(e.nameDisplay),9,l)))),128))],544),[[a.vModelSelect,o.selectedCluster]])]),o.selectedCluster?((0,a.openBlock)(),(0,a.createElementBlock)("div",f,[t[8]||(t[8]=(0,a.createElementVNode)("label",null,"Target Namespace (optional):",-1)),(0,a.withDirectives)((0,a.createElementVNode)("select",{"onUpdate:modelValue":t[2]||(t[2]=e=>o.selectedNamespace=e)},[t[7]||(t[7]=(0,a.createElementVNode)("option",{value:""},"All namespaces",-1)),((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(o.namespaces,(e=>((0,a.openBlock)(),(0,a.createElementBlock)("option",{key:e,value:e},(0,a.toDisplayString)(e),9,p)))),128))],512),[[a.vModelSelect,o.selectedNamespace]])])):(0,a.createCommentVNode)("",!0),o.selectedCluster?((0,a.openBlock)(),(0,a.createElementBlock)("div",d,[t[12]||(t[12]=(0,a.createElementVNode)("h4",null,"Cluster Status",-1)),(0,a.createElementVNode)("div",y,[(0,a.createElementVNode)("div",m,[t[9]||(t[9]=(0,a.createElementVNode)("span",{class:"label"},"Total Pods:",-1)),(0,a.createElementVNode)("span",g,(0,a.toDisplayString)(o.podCount),1)]),(0,a.createElementVNode)("div",h,[t[10]||(t[10]=(0,a.createElementVNode)("span",{class:"label"},"Running Pods:",-1)),(0,a.createElementVNode)("span",b,(0,a.toDisplayString)(o.runningPods),1)]),(0,a.createElementVNode)("div",v,[t[11]||(t[11]=(0,a.createElementVNode)("span",{class:"label"},"Target Namespace:",-1)),(0,a.createElementVNode)("span",w,(0,a.toDisplayString)(o.selectedNamespace||"All"),1)])])])):(0,a.createCommentVNode)("",!0),(0,a.createElementVNode)("div",x,[(0,a.createElementVNode)("button",{class:"btn btn-primary btn-lg",disabled:!o.selectedCluster||o.isDeploying,onClick:t[3]||(t[3]=(...e)=>S.startKubeDoom&&S.startKubeDoom(...e))},[t[13]||(t[13]=(0,a.createElementVNode)("i",{class:"icon icon-play"},null,-1)),(0,a.createTextVNode)(" "+(0,a.toDisplayString)(o.isDeploying?"Deploying KubeDoom...":"Start KubeDoom"),1)],8,E),(0,a.createElementVNode)("button",{class:"btn btn-secondary",disabled:!o.kubeDoomDeployed,onClick:t[4]||(t[4]=(...e)=>S.stopKubeDoom&&S.stopKubeDoom(...e))},t[14]||(t[14]=[(0,a.createElementVNode)("i",{class:"icon icon-stop"},null,-1),(0,a.createTextVNode)(" Stop KubeDoom ")]),8,k)])])])])}var j=r(4364),O={name:"KubeDoomDashboard",data(){return{selectedCluster:"",selectedNamespace:"",clusters:[],namespaces:[],podCount:0,runningPods:0,isDeploying:!1,kubeDoomDeployed:!1}},async mounted(){await this.loadClusters()},methods:{async loadClusters(){try{const e=await this.$store.dispatch("management/findAll",{type:"cluster"});this.clusters=e.filter((e=>e.isReady))}catch(e){j.error("Failed to load clusters:",e),this.$store.dispatch("growl/error",{title:"Error",message:"Failed to load clusters"})}},async onClusterChange(){if(!this.selectedCluster)return this.namespaces=[],this.podCount=0,void(this.runningPods=0);try{await this.loadNamespaces(),await this.loadPodStats()}catch(e){j.error("Failed to load cluster data:",e)}},async loadNamespaces(){try{const e=await this.$store.dispatch("cluster/findAll",{type:"namespace",clusterId:this.selectedCluster});this.namespaces=e.map((e=>e.metadata.name))}catch(e){j.error("Failed to load namespaces:",e)}},async loadPodStats(){try{const e=await this.$store.dispatch("cluster/findAll",{type:"pod",clusterId:this.selectedCluster});this.podCount=e.length,this.runningPods=e.filter((e=>"Running"===e.status?.phase)).length}catch(e){j.error("Failed to load pod stats:",e)}},async startKubeDoom(){this.isDeploying=!0;try{await this.deployKubeDoom(),this.$store.dispatch("growl/success",{title:"Success",message:"KubeDoom deployed successfully!"}),this.$router.push({name:"kubedoom-c-cluster-game",params:{cluster:this.selectedCluster}})}catch(e){j.error("Failed to deploy KubeDoom:",e),this.$store.dispatch("growl/error",{title:"Error",message:"Failed to deploy KubeDoom: "+e.message})}finally{this.isDeploying=!1}},async deployKubeDoom(){await new Promise((e=>setTimeout(e,2e3))),this.kubeDoomDeployed=!0},async stopKubeDoom(){try{await this.removeKubeDoom(),this.kubeDoomDeployed=!1,this.$store.dispatch("growl/success",{title:"Success",message:"KubeDoom stopped successfully!"})}catch(e){j.error("Failed to stop KubeDoom:",e),this.$store.dispatch("growl/error",{title:"Error",message:"Failed to stop KubeDoom: "+e.message})}},async removeKubeDoom(){await new Promise((e=>setTimeout(e,1e3)))}}},A=(r(4246),r(7433));const N=(0,A.A)(O,[["render",S],["__scopeId","data-v-471e0416"]]);var P=N;const D={class:"kubedoom-game"},R={class:"game-header"},T={class:"header-left"},C={class:"header-center"},V={class:"header-right"},I={class:"game-content"},F={class:"game-area"},M={class:"vnc-container",ref:"vncContainer"},B={key:0,class:"connection-overlay"},_={ref:"vncCanvas"},U={class:"game-controls"},q={class:"control-group"},$={class:"cheat-buttons"},z={class:"pod-monitor"},L={class:"monitor-stats"},K={class:"stat"},G={class:"value killed"},W={class:"stat"},H={class:"value remaining"},J={class:"stat"},Y={class:"value respawned"},X={key:0,class:"recent-kills"},Z={class:"kill-list"},Q={class:"pod-name"},ee={class:"namespace"},te={class:"time"},ne={class:"cluster-health"};function re(e,t,n,r,o,i){return(0,a.openBlock)(),(0,a.createElementBlock)("div",D,[(0,a.createElementVNode)("div",R,[(0,a.createElementVNode)("div",T,[(0,a.createElementVNode)("button",{class:"btn btn-secondary",onClick:t[0]||(t[0]=(...e)=>i.goBack&&i.goBack(...e))},t[5]||(t[5]=[(0,a.createElementVNode)("i",{class:"icon icon-arrow-left"},null,-1),(0,a.createTextVNode)(" Back to Dashboard ")]))]),(0,a.createElementVNode)("div",C,[(0,a.createElementVNode)("h2",null,"KubeDoom - "+(0,a.toDisplayString)(o.clusterName),1),(0,a.createElementVNode)("div",{class:(0,a.normalizeClass)(["status",o.connectionStatus])},[(0,a.createElementVNode)("i",{class:(0,a.normalizeClass)(["icon",i.statusIcon])},null,2),(0,a.createTextVNode)(" "+(0,a.toDisplayString)(i.statusText),1)],2)]),(0,a.createElementVNode)("div",V,[(0,a.createElementVNode)("button",{class:"btn btn-danger",onClick:t[1]||(t[1]=(...e)=>i.emergencyStop&&i.emergencyStop(...e))},t[6]||(t[6]=[(0,a.createElementVNode)("i",{class:"icon icon-stop"},null,-1),(0,a.createTextVNode)(" Emergency Stop ")]))])]),(0,a.createElementVNode)("div",I,[(0,a.createElementVNode)("div",F,[(0,a.createElementVNode)("div",M,[o.connected?(0,a.createCommentVNode)("",!0):((0,a.openBlock)(),(0,a.createElementBlock)("div",B,[t[7]||(t[7]=(0,a.createElementVNode)("div",{class:"spinner"},null,-1)),(0,a.createElementVNode)("p",null,(0,a.toDisplayString)(o.connectionMessage),1)])),(0,a.withDirectives)((0,a.createElementVNode)("canvas",_,null,512),[[a.vShow,o.connected]])],512),(0,a.createElementVNode)("div",U,[t[9]||(t[9]=(0,a.createStaticVNode)('<div class="control-group" data-v-6d118c02><h4 data-v-6d118c02>Game Controls</h4><div class="controls-grid" data-v-6d118c02><div class="control-item" data-v-6d118c02><span class="key" data-v-6d118c02>WASD</span><span class="desc" data-v-6d118c02>Move</span></div><div class="control-item" data-v-6d118c02><span class="key" data-v-6d118c02>Mouse</span><span class="desc" data-v-6d118c02>Look around</span></div><div class="control-item" data-v-6d118c02><span class="key" data-v-6d118c02>Ctrl</span><span class="desc" data-v-6d118c02>Fire</span></div><div class="control-item" data-v-6d118c02><span class="key" data-v-6d118c02>Space</span><span class="desc" data-v-6d118c02>Open doors</span></div><div class="control-item" data-v-6d118c02><span class="key" data-v-6d118c02>ESC</span><span class="desc" data-v-6d118c02>Pause</span></div></div></div>',1)),(0,a.createElementVNode)("div",q,[t[8]||(t[8]=(0,a.createElementVNode)("h4",null,"Cheats",-1)),(0,a.createElementVNode)("div",$,[(0,a.createElementVNode)("button",{class:"btn btn-sm",onClick:t[2]||(t[2]=e=>i.sendCheat("idkfa"))}," All Weapons "),(0,a.createElementVNode)("button",{class:"btn btn-sm",onClick:t[3]||(t[3]=e=>i.sendCheat("iddqd"))}," God Mode "),(0,a.createElementVNode)("button",{class:"btn btn-sm",onClick:t[4]||(t[4]=e=>i.sendCheat("idspispopd"))}," No Clip ")])])])]),(0,a.createElementVNode)("div",z,[t[15]||(t[15]=(0,a.createElementVNode)("h3",null,"Pod Monitor",-1)),(0,a.createElementVNode)("div",L,[(0,a.createElementVNode)("div",K,[t[10]||(t[10]=(0,a.createElementVNode)("span",{class:"label"},"Pods Killed:",-1)),(0,a.createElementVNode)("span",G,(0,a.toDisplayString)(o.podsKilled),1)]),(0,a.createElementVNode)("div",W,[t[11]||(t[11]=(0,a.createElementVNode)("span",{class:"label"},"Pods Remaining:",-1)),(0,a.createElementVNode)("span",H,(0,a.toDisplayString)(o.podsRemaining),1)]),(0,a.createElementVNode)("div",J,[t[12]||(t[12]=(0,a.createElementVNode)("span",{class:"label"},"Pods Respawned:",-1)),(0,a.createElementVNode)("span",Y,(0,a.toDisplayString)(o.podsRespawned),1)])]),o.recentKills.length>0?((0,a.openBlock)(),(0,a.createElementBlock)("div",X,[t[13]||(t[13]=(0,a.createElementVNode)("h4",null,"Recent Pod Kills",-1)),(0,a.createElementVNode)("div",Z,[((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(o.recentKills,(e=>((0,a.openBlock)(),(0,a.createElementBlock)("div",{key:e.id,class:"kill-item"},[(0,a.createElementVNode)("span",Q,(0,a.toDisplayString)(e.podName),1),(0,a.createElementVNode)("span",ee,(0,a.toDisplayString)(e.namespace),1),(0,a.createElementVNode)("span",te,(0,a.toDisplayString)(i.formatTime(e.timestamp)),1)])))),128))])])):(0,a.createCommentVNode)("",!0),(0,a.createElementVNode)("div",ne,[t[14]||(t[14]=(0,a.createElementVNode)("h4",null,"Cluster Health",-1)),(0,a.createElementVNode)("div",{class:(0,a.normalizeClass)(["health-indicator",o.clusterHealth.status])},[(0,a.createElementVNode)("i",{class:(0,a.normalizeClass)(["icon",o.clusterHealth.icon])},null,2),(0,a.createTextVNode)(" "+(0,a.toDisplayString)(o.clusterHealth.message),1)],2)])])])])}var oe=r(4364),ae={name:"KubeDoomGame",data(){return{connected:!1,connectionStatus:"connecting",connectionMessage:"Connecting to KubeDoom...",vncClient:null,clusterName:"",podsKilled:0,podsRemaining:0,podsRespawned:0,recentKills:[],clusterHealth:{status:"healthy",icon:"icon-checkmark",message:"Cluster is healthy"},monitoringInterval:null}},computed:{statusIcon(){switch(this.connectionStatus){case"connected":return"icon-checkmark";case"connecting":return"icon-spinner";case"error":return"icon-error";default:return"icon-info"}},statusText(){switch(this.connectionStatus){case"connected":return"Connected to KubeDoom";case"connecting":return"Connecting...";case"error":return"Connection failed";default:return"Unknown status"}}},async mounted(){this.clusterName=this.$route.params.cluster||"Unknown",await this.initializeVNC(),this.startMonitoring()},beforeUnmount(){this.cleanup()},methods:{async initializeVNC(){try{this.connectionMessage="Initializing VNC connection...",await this.simulateVNCConnection()}catch(e){oe.error("Failed to initialize VNC:",e),this.connectionStatus="error",this.connectionMessage="Failed to connect to KubeDoom"}},async simulateVNCConnection(){await new Promise((e=>setTimeout(e,2e3))),this.connected=!0,this.connectionStatus="connected",this.initializeCanvas()},initializeCanvas(){const e=this.$refs.vncCanvas;if(!e)return;e.width=800,e.height=600;const t=e.getContext("2d");t.fillStyle="#000",t.fillRect(0,0,e.width,e.height),t.fillStyle="#ff0000",t.font="24px Arial",t.textAlign="center",t.fillText("KubeDoom Game Screen",e.width/2,e.height/2),t.fillText("(VNC Connection Placeholder)",e.width/2,e.height/2+30),e.addEventListener("click",this.handleCanvasClick)},handleCanvasClick(){this.simulatePodKill()},simulatePodKill(){const e=["nginx-deployment-abc123","redis-master-def456","postgres-ghi789","api-server-jkl012","worker-mno345"],t=["default","kube-system","monitoring","ingress"],n={id:Date.now(),podName:e[Math.floor(Math.random()*e.length)],namespace:t[Math.floor(Math.random()*t.length)],timestamp:new Date};this.recentKills.unshift(n),this.recentKills.length>10&&this.recentKills.pop(),this.podsKilled++,this.podsRemaining=Math.max(0,this.podsRemaining-1),this.updateClusterHealth()},updateClusterHealth(){this.podsKilled>20?this.clusterHealth={status:"critical",icon:"icon-error",message:"Cluster under heavy stress!"}:this.podsKilled>10?this.clusterHealth={status:"warning",icon:"icon-warning",message:"Cluster experiencing some stress"}:this.clusterHealth={status:"healthy",icon:"icon-checkmark",message:"Cluster is healthy"}},sendCheat(e){oe.log("Sending cheat:",e),this.$store.dispatch("growl/info",{title:"Cheat Activated",message:`Cheat code "${e}" sent to game`})},startMonitoring(){this.podsRemaining=50,this.monitoringInterval=setInterval((()=>{Math.random()<.1&&this.podsRemaining<50&&(this.podsRemaining++,this.podsRespawned++)}),5e3)},formatTime(e){return e.toLocaleTimeString()},goBack(){this.$router.push({name:"kubedoom-c-cluster-dashboard",params:{cluster:this.$route.params.cluster}})},async emergencyStop(){confirm("Are you sure you want to stop KubeDoom? This will end the game session.")&&(await this.cleanup(),this.goBack())},cleanup(){this.monitoringInterval&&clearInterval(this.monitoringInterval),this.vncClient&&(this.vncClient=null);const e=this.$refs.vncCanvas;e&&e.removeEventListener("click",this.handleCanvasClick)}}};r(6381);const ie=(0,A.A)(ae,[["render",re],["__scopeId","data-v-6d118c02"]]);var ce=ie;const se="_",ue="kubedoom",le=[{name:`${ue}-c-cluster-dashboard`,path:`/${ue}/c/:cluster/dashboard`,component:P,meta:{product:ue,cluster:se,pkg:ue}},{name:`${ue}-c-cluster-game`,path:`/${ue}/c/:cluster/game`,component:ce,meta:{product:ue,cluster:se,pkg:ue}}];var fe=le;function pe(e){n(e),e.metadata=r(8330),e.addProduct(r(3100)),e.addRoutes(fe)}var de=pe}(),o}()}));
//# sourceMappingURL=kubedoom-extension-0.1.0.umd.min.js.map