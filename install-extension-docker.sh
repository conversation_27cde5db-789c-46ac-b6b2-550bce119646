#!/bin/bash

# Install KubeDoom Extension using Docker exec

echo "🎮 Installing KubeDoom Extension via Docker"
echo "==========================================="

# Check if rancher container is running
if ! docker ps | grep -q rancher-server; then
    echo "❌ Rancher server container not found. Please start it first."
    exit 1
fi

echo "✅ Found Rancher server container"

# Create the UIExtension YAML
cat > kubedoom-uiextension.yaml << 'EOF'
apiVersion: management.cattle.io/v3
kind: UIExtension
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-extension
spec:
  plugin:
    name: kubedoom-extension
    version: 0.1.0
    endpoint: "http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    noCache: false
    metadata:
      displayName: "KubeDoom"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
EOF

# Copy the YAML into the rancher container and apply it
echo "📦 Creating UIExtension resource..."
docker cp kubedoom-uiextension.yaml rancher-server:/tmp/kubedoom-uiextension.yaml

# Apply the UIExtension using kubectl inside the rancher container
docker exec rancher-server kubectl apply -f /tmp/kubedoom-uiextension.yaml

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ KubeDoom Extension installed successfully!"
    echo ""
    echo "📋 Installation Summary:"
    echo "  ✅ UIExtension: kubedoom-extension created in cattle-ui-plugin-system"
    echo "  ✅ Extension endpoint: http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Refresh your Rancher UI (F5 or Ctrl+R)"
    echo "2. Look for 'KubeDoom' in the main navigation menu"
    echo "3. If it doesn't appear, wait a few minutes and refresh again"
    echo ""
    echo "🔍 Check status:"
    echo "  docker exec rancher-server kubectl get uiextensions -n cattle-ui-plugin-system"
else
    echo "❌ Failed to install UIExtension"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "1. Check if cattle-ui-plugin-system namespace exists:"
    echo "   docker exec rancher-server kubectl get namespace cattle-ui-plugin-system"
    echo ""
    echo "2. Create namespace if it doesn't exist:"
    echo "   docker exec rancher-server kubectl create namespace cattle-ui-plugin-system"
    echo ""
    echo "3. Try installing again"
fi

# Clean up
rm -f kubedoom-uiextension.yaml

echo ""
echo "🎮 Extension installation complete!"
