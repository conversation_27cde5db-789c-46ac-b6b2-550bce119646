# Dockerfile for KubeDoom Extension Catalog
FROM nginx:alpine

# Copy extension files
COPY kubedoom-extension/dist-pkg/ /usr/share/nginx/html/

# Create index.yaml for Helm repository
RUN echo 'apiVersion: v1' > /usr/share/nginx/html/index.yaml && \
    echo 'entries:' >> /usr/share/nginx/html/index.yaml && \
    echo '  kubedoom-extension:' >> /usr/share/nginx/html/index.yaml && \
    echo '  - name: kubedoom-extension' >> /usr/share/nginx/html/index.yaml && \
    echo '    version: 0.1.0' >> /usr/share/nginx/html/index.yaml && \
    echo '    description: KubeDoom Extension - Play Doom to kill Kubernetes pods' >> /usr/share/nginx/html/index.yaml && \
    echo '    urls:' >> /usr/share/nginx/html/index.yaml && \
    echo '    - kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js' >> /usr/share/nginx/html/index.yaml

# Nginx configuration for CORS
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '        index index.html index.htm;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Origin *;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Headers "Content-Type";' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

EXPOSE 80
