#!/bin/bash

# <PERSON><PERSON><PERSON>oom Extension Installation Script v2 (Clean Cache)

echo "🎮 Installing KubeDoom Extension v0.1.1 (Clean Version)"
echo "======================================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Create the UIPlugin YAML with version 0.1.1
cat > kubedoom-v2.yaml << 'EOF'
apiVersion: catalog.cattle.io/v1
kind: UIPlugin
metadata:
  name: kubedoom-v2
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom
  annotations:
    catalog.cattle.io/display-name: "KubeDoom v2"
    catalog.cattle.io/description: "Play Doom to kill Kubernetes pods and test cluster resilience"
    catalog.cattle.io/ui-extensions-version: ">= 3.0.0 < 4.0.0"
spec:
  plugin:
    name: kubedoom
    version: 0.1.1
    endpoint: "http://extension-server:8000/kubedoom-0.1.1"
    noCache: true
    metadata:
      displayName: "KubeDoom v2"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
      catalog.cattle.io/kube-version: ">= 1.16.0-0"
      catalog.cattle.io/rancher-version: ">= 2.10.0-0"
      catalog.cattle.io/ui-extensions-version: ">= 3.0.0 < 4.0.0"
EOF

print_step "Installing KubeDoom v2 UIPlugin..."
docker cp kubedoom-v2.yaml rancher-server:/tmp/kubedoom-v2.yaml
docker exec rancher-server kubectl apply -f /tmp/kubedoom-v2.yaml

if [ $? -eq 0 ]; then
    print_success "KubeDoom v2 Extension installed successfully!"
    echo ""
    echo "📋 Installation Summary:"
    echo "  ✅ Extension Name: kubedoom-v2"
    echo "  ✅ Version: 0.1.1"
    echo "  ✅ Endpoint: http://extension-server:8000/kubedoom-0.1.1"
    echo "  ✅ Cache: Disabled (noCache: true)"
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Refresh your Rancher UI (F5 or Ctrl+R)"
    echo "2. Look for 'KubeDoom v2' in the main navigation menu"
    echo "3. Click on KubeDoom v2 to access the extension"
fi

# Clean up
rm -f kubedoom-v2.yaml

echo ""
echo "🎮 KubeDoom v2 Extension installation complete!"
