#!/usr/bin/env python3
"""
Simple HTTP server to serve the KubeDoom extension for Rancher installation
"""

import http.server
import socketserver
import os
import json
from pathlib import Path

PORT = 8000
EXTENSION_DIR = os.environ.get("EXTENSION_DIR", "kubedoom-extension/dist-pkg")

class ExtensionHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=EXTENSION_DIR, **kwargs)

    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def create_extension_catalog():
    """Create catalog files for the extension repository"""

    # Create proper Helm chart structure
    chart_dir = Path(EXTENSION_DIR) / "charts" / "kubedoom-extension"
    chart_dir.mkdir(parents=True, exist_ok=True)

    # Create Chart.yaml
    chart_yaml = """apiVersion: v2
name: kubedoom-extension
description: KubeDoom Extension - Play Doom to kill Kubernetes pods
type: application
version: 0.1.0
appVersion: "0.1.0"
home: https://github.com/storax/kubedoom
sources:
  - https://github.com/storax/kubedoom
maintainers:
  - name: KubeDoom Extension
    email: <EMAIL>
annotations:
  catalog.cattle.io/display-name: "KubeDoom Extension"
  catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
  catalog.cattle.io/type: "rancher-ui-extension"
  catalog.cattle.io/ui-extensions-version: ">=1.0.0"
"""

    with open(chart_dir / "Chart.yaml", 'w') as f:
        f.write(chart_yaml)

    # Create values.yaml
    values_yaml = """# Default values for kubedoom-extension
extension:
  enabled: true
  name: kubedoom-extension
  version: 0.1.0
"""

    with open(chart_dir / "values.yaml", 'w') as f:
        f.write(values_yaml)

    # Create templates directory and extension template
    templates_dir = chart_dir / "templates"
    templates_dir.mkdir(exist_ok=True)

    # Create extension template
    extension_template = """apiVersion: management.cattle.io/v3
kind: UIExtension
metadata:
  name: {{ .Values.extension.name }}
  namespace: cattle-ui-plugin-system
spec:
  plugin:
    name: {{ .Values.extension.name }}
    version: {{ .Values.extension.version }}
    endpoint: "kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    noCache: false
"""

    with open(templates_dir / "extension.yaml", 'w') as f:
        f.write(extension_template)

    # Create index.yaml for Helm repository
    index_yaml = """apiVersion: v1
entries:
  kubedoom-extension:
  - name: kubedoom-extension
    version: 0.1.0
    description: KubeDoom Extension - Play Doom to kill Kubernetes pods
    home: https://github.com/storax/kubedoom
    sources:
    - https://github.com/storax/kubedoom
    maintainers:
    - name: KubeDoom Extension
      email: <EMAIL>
    urls:
    - charts/kubedoom-extension-0.1.0.tgz
    created: "2025-05-26T19:00:00Z"
    digest: "sha256:1234567890abcdef"
    annotations:
      catalog.cattle.io/display-name: "KubeDoom Extension"
      catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
      catalog.cattle.io/type: "rancher-ui-extension"
      catalog.cattle.io/ui-extensions-version: ">=1.0.0"
generated: "2025-05-26T19:00:00Z"
"""

    index_path = Path(EXTENSION_DIR) / "index.yaml"
    with open(index_path, 'w') as f:
        f.write(index_yaml)

    # Package the chart as a .tgz file
    import tarfile

    chart_tgz_path = Path(EXTENSION_DIR) / "charts" / "kubedoom-extension-0.1.0.tgz"

    with tarfile.open(chart_tgz_path, "w:gz") as tar:
        tar.add(chart_dir, arcname="kubedoom-extension")

    print(f"Created Helm chart at {chart_dir}")
    print(f"Created chart package at {chart_tgz_path}")
    print(f"Created index.yaml at {index_path}")

def main():
    # Create catalog file
    create_extension_catalog()

    # Start HTTP server
    with socketserver.TCPServer(("", PORT), ExtensionHandler) as httpd:
        print(f"🎮 KubeDoom Extension Server")
        print(f"=============================")
        print(f"Serving extensions at: http://localhost:{PORT}")
        print(f"Extension catalog: http://localhost:{PORT}/catalog.json")
        print(f"Extension package: http://localhost:{PORT}/kubedoom-extension-0.1.0/")
        print(f"")
        print(f"To install in Rancher:")
        print(f"1. Go to Extensions in Rancher UI")
        print(f"2. Add Repository: http://localhost:{PORT}")
        print(f"3. Install KubeDoom Extension")
        print(f"")
        print(f"Press Ctrl+C to stop the server")

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
