#!/usr/bin/env python3
"""
Simple HTTP server to serve the KubeDoom extension for Rancher installation
"""

import http.server
import socketserver
import os
import json
from pathlib import Path

PORT = 8000
EXTENSION_DIR = "kubedoom-extension/dist-pkg"

class ExtensionHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=EXTENSION_DIR, **kwargs)
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def create_extension_catalog():
    """Create a catalog.json file for the extension repository"""
    catalog = {
        "entries": {
            "kubedoom-extension": {
                "0.1.0": {
                    "annotations": {
                        "catalog.cattle.io/display-name": "KubeDoom Extension",
                        "catalog.cattle.io/description": "Play Doom to kill Kubernetes pods through Rancher UI",
                        "catalog.cattle.io/type": "rancher-ui-extension"
                    },
                    "spec": {
                        "displayName": "KubeDoom Extension",
                        "description": "Play Doom to kill Kubernetes pods and test cluster resilience",
                        "version": "0.1.0",
                        "appVersion": "0.1.0",
                        "urls": [
                            f"http://localhost:{PORT}/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
                        ]
                    }
                }
            }
        }
    }
    
    catalog_path = Path(EXTENSION_DIR) / "catalog.json"
    with open(catalog_path, 'w') as f:
        json.dump(catalog, f, indent=2)
    
    print(f"Created catalog.json at {catalog_path}")

def main():
    # Create catalog file
    create_extension_catalog()
    
    # Start HTTP server
    with socketserver.TCPServer(("", PORT), ExtensionHandler) as httpd:
        print(f"🎮 KubeDoom Extension Server")
        print(f"=============================")
        print(f"Serving extensions at: http://localhost:{PORT}")
        print(f"Extension catalog: http://localhost:{PORT}/catalog.json")
        print(f"Extension package: http://localhost:{PORT}/kubedoom-extension-0.1.0/")
        print(f"")
        print(f"To install in Rancher:")
        print(f"1. Go to Extensions in Rancher UI")
        print(f"2. Add Repository: http://localhost:{PORT}")
        print(f"3. Install KubeDoom Extension")
        print(f"")
        print(f"Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
