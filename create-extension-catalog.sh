#!/bin/bash

# Create a proper extension catalog for Rancher 2.11

echo "🎮 Creating KubeDoom Extension Catalog"
echo "======================================"

# Create catalog structure
mkdir -p catalog/extensions/kubedoom-extension/0.1.0

# Copy extension files
cp -r kubedoom-extension/dist-pkg/kubedoom-extension-0.1.0/* catalog/extensions/kubedoom-extension/0.1.0/

# Create catalog metadata
cat > catalog/catalog-info.yaml << 'EOF'
apiVersion: catalog.cattle.io/v1
kind: ClusterRepo
metadata:
  name: kubedoom-extensions
spec:
  gitRepo: ""
  gitBranch: ""
  url: ""
EOF

# Create extension metadata
cat > catalog/extensions/kubedoom-extension/metadata.yaml << 'EOF'
apiVersion: catalog.cattle.io/v1
kind: App
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
spec:
  chart:
    metadata:
      name: kubedoom-extension
      version: 0.1.0
      description: "KubeDoom Extension - Play Doom to kill Kubernetes pods"
      annotations:
        catalog.cattle.io/display-name: "KubeDoom Extension"
        catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
        catalog.cattle.io/type: "rancher-ui-extension"
        catalog.cattle.io/ui-extensions-version: ">=1.0.0"
  projectId: ""
  targetNamespace: cattle-ui-plugin-system
  values:
    plugin:
      name: kubedoom-extension
      version: 0.1.0
      endpoint: kubedoom-extension-0.1.0.umd.min.js
      noCache: false
EOF

# Create Dockerfile for catalog image
cat > Dockerfile.extension-catalog << 'EOF'
FROM nginx:alpine

# Copy catalog files
COPY catalog/ /usr/share/nginx/html/

# Create nginx config for proper CORS and content serving
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/conf.d/default.conf && \
    echo '    server_name localhost;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '        index index.html index.htm;' >> /etc/nginx/conf.d/default.conf && \
    echo '        try_files $uri $uri/ =404;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Origin *;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Headers "Content-Type";' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

EXPOSE 80
EOF

# Build the catalog image
echo "Building extension catalog image..."
docker build -f Dockerfile.extension-catalog -t kubedoom-extension-catalog:latest .

echo ""
echo "✅ Extension catalog created successfully!"
echo ""
echo "📦 Catalog Image: kubedoom-extension-catalog:latest"
echo ""
echo "🎯 To use in Rancher:"
echo "1. Go to Extensions → Import Extension Catalog"
echo "2. Catalog Image Reference: kubedoom-extension-catalog:latest"
echo "3. Click Import"
echo ""
echo "📁 Catalog files created in: ./catalog/"
