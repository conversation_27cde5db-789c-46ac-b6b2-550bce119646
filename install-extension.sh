#!/bin/bash

# KubeDoom Extension Installation Script
# This script helps install the KubeDoom extension into Rancher

set -e

echo "🎮 KubeDoom Extension Installation"
echo "=================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Rancher is accessible
check_rancher() {
    print_step "Checking if Rancher is accessible..."
    
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost | grep -q "200\|302"; then
        print_success "Rancher is accessible at https://localhost"
        return 0
    else
        print_error "Rancher is not accessible. Please wait for it to start up."
        return 1
    fi
}

# Method 1: Manual installation instructions
show_manual_installation() {
    echo ""
    print_step "Manual Installation Method"
    echo "=========================="
    echo ""
    echo "1. Open Rancher UI: https://localhost"
    echo "2. Login with username: admin, password: admin123"
    echo "3. Complete the initial setup wizard"
    echo "4. Navigate to 'Extensions' in the main menu"
    echo "5. Click 'Manage Repositories'"
    echo "6. Add a new repository or install from local file"
    echo ""
    echo "Extension files are located at:"
    echo "  📁 kubedoom-extension/dist-pkg/kubedoom-extension-0.1.0/"
    echo "  📄 Main file: kubedoom-extension-0.1.0.umd.min.js"
    echo "  📄 Package info: package.json"
    echo ""
}

# Method 2: Repository server
start_extension_server() {
    print_step "Starting Extension Repository Server..."
    
    if command -v python3 &> /dev/null; then
        print_info "Starting Python HTTP server for extension repository..."
        echo ""
        echo "The server will serve the extension at: http://localhost:8000"
        echo "You can add this as a repository in Rancher Extensions."
        echo ""
        echo "Press Ctrl+C to stop the server when done."
        echo ""
        
        python3 serve-extension.py
    else
        print_error "Python3 not found. Using manual installation method instead."
        show_manual_installation
    fi
}

# Method 3: Direct file copy (if volume mount works)
copy_extension_files() {
    print_step "Checking if extension can be copied directly..."
    
    # Check if the Rancher container has the plugins directory mounted
    if docker exec rancher-server ls /usr/share/rancher/ui-plugins/ &> /dev/null; then
        print_info "Rancher plugins directory is accessible"
        
        # Copy extension files
        if docker cp kubedoom-extension/dist-pkg/kubedoom-extension-0.1.0 rancher-server:/usr/share/rancher/ui-plugins/; then
            print_success "Extension files copied to Rancher container"
            print_info "Restart Rancher to load the extension: docker-compose restart rancher"
            return 0
        else
            print_error "Failed to copy extension files"
            return 1
        fi
    else
        print_info "Direct file copy not available, using other methods"
        return 1
    fi
}

# Main installation process
main() {
    case "${1:-auto}" in
        "check")
            check_rancher
            ;;
        "manual")
            show_manual_installation
            ;;
        "server")
            start_extension_server
            ;;
        "copy")
            copy_extension_files
            ;;
        "auto")
            print_step "Auto-detecting best installation method..."
            
            # First check if Rancher is ready
            if ! check_rancher; then
                echo ""
                print_info "Waiting for Rancher to start up..."
                print_info "You can check status with: docker-compose logs -f rancher"
                echo ""
                show_manual_installation
                exit 1
            fi
            
            echo ""
            print_step "Choose installation method:"
            echo "1. Manual installation via Rancher UI (recommended)"
            echo "2. Start extension repository server"
            echo "3. Direct file copy (if available)"
            echo ""
            read -p "Enter choice (1-3): " choice
            
            case $choice in
                1)
                    show_manual_installation
                    ;;
                2)
                    start_extension_server
                    ;;
                3)
                    copy_extension_files || show_manual_installation
                    ;;
                *)
                    print_error "Invalid choice. Showing manual installation."
                    show_manual_installation
                    ;;
            esac
            ;;
        "help")
            echo "Usage: $0 [method]"
            echo ""
            echo "Methods:"
            echo "  auto     - Auto-detect best method (default)"
            echo "  check    - Check if Rancher is accessible"
            echo "  manual   - Show manual installation instructions"
            echo "  server   - Start extension repository server"
            echo "  copy     - Try direct file copy"
            echo "  help     - Show this help"
            ;;
        *)
            print_error "Unknown method: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
