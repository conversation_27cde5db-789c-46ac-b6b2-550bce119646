# Manual KubeDoom Extension Installation
# Apply this directly in Rancher or with kubectl

apiVersion: v1
kind: Namespace
metadata:
  name: cattle-ui-plugin-system
  labels:
    name: cattle-ui-plugin-system
---
apiVersion: management.cattle.io/v3
kind: UIExtension
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-extension
  annotations:
    meta.helm.sh/release-name: kubedoom-extension
    meta.helm.sh/release-namespace: cattle-ui-plugin-system
spec:
  plugin:
    name: kubedoom-extension
    version: 0.1.0
    endpoint: "http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    noCache: false
    metadata:
      displayName: "KubeDoom Extension"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
