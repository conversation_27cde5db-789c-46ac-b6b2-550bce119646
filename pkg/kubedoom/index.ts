import { importTypes } from '@rancher/auto-import';
import { IPlugin, ActionLocation, CardLocation } from '@shell/core/types';

// Init the package
export default function(plugin: IPlugin): void {
  // Auto-import model, detail, edit from the folders
  importTypes(plugin);

  // Provide plugin metadata from package.json
  plugin.metadata = require('./package.json');

  // Load a product
  plugin.addProduct(require('./product'));

  // Add main KubeDoom route
  plugin.addRoute({
    name: 'kubedoom',
    path: '/kubedoom',
    component: () => import('./pages/KubeDoomPage.vue')
  });

  // Add KubeDoom dashboard route
  plugin.addRoute({
    name: 'kubedoom-dashboard',
    path: '/kubedoom/dashboard',
    component: () => import('./pages/KubeDoomDashboard.vue')
  });

  // Add cluster dashboard card
  plugin.addCard(
    CardLocation.CLUSTER_DASHBOARD_CARD,
    { cluster: ['local'] },
    {
      label: 'KubeDoom Status',
      labelKey: 'kubedoom.card.title',
      component: () => import('./components/KubeDoomCard.vue')
    }
  );

  // Add header action for quick access
  plugin.addAction(
    ActionLocation.HEADER,
    {},
    {
      tooltipKey: 'kubedoom.header.tooltip',
      tooltip: 'Open KubeDoom VNC',
      icon: 'icon-pipeline',
      invoke() {
        window.open('http://localhost:8080', '_blank');
      }
    }
  );
}
