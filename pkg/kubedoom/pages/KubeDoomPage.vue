<template>
  <div class="kubedoom-page">
    <!-- Header Section -->
    <div class="kubedoom-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="kubedoom-title">
            <i class="icon icon-pipeline"></i>
            KubeDoom
          </h1>
          <p class="kubedoom-subtitle">
            Play Doom to kill Kubernetes pods and test cluster resilience
          </p>
        </div>
        <div class="action-buttons">
          <button class="btn role-primary" @click="openVNC">
            <i class="icon icon-external-link"></i>
            Play KubeDoom
          </button>
          <button class="btn role-secondary" @click="refreshStatus">
            <i class="icon icon-refresh"></i>
            Refresh Status
          </button>
        </div>
      </div>
    </div>

    <!-- Status Cards -->
    <div class="status-grid">
      <div class="status-card">
        <div class="card-header">
          <h3><i class="icon icon-dot text-success"></i> VNC Server</h3>
        </div>
        <div class="card-content">
          <p class="status-text">{{ vncStatus }}</p>
          <a href="http://localhost:8080" target="_blank" class="link">
            http://localhost:8080
          </a>
        </div>
      </div>

      <div class="status-card">
        <div class="card-header">
          <h3><i class="icon icon-dot text-info"></i> Target Pods</h3>
        </div>
        <div class="card-content">
          <p class="status-text">{{ podCount }} pods available</p>
          <p class="small-text">Namespace: kubedoom-demo</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-header">
          <h3><i class="icon icon-dot text-warning"></i> Game Status</h3>
        </div>
        <div class="card-content">
          <p class="status-text">{{ gameStatus }}</p>
          <p class="small-text">Ready to destroy pods!</p>
        </div>
      </div>
    </div>

    <!-- Instructions Section -->
    <div class="instructions-section">
      <h2>🎮 How to Play KubeDoom</h2>
      <div class="instructions-grid">
        <div class="instruction-card">
          <h4>1. Start the Game</h4>
          <p>Click "Play KubeDoom" to open the VNC interface in a new tab.</p>
        </div>
        <div class="instruction-card">
          <h4>2. Game Controls</h4>
          <ul>
            <li><strong>WASD</strong> - Move around</li>
            <li><strong>Mouse</strong> - Look around</li>
            <li><strong>Ctrl</strong> - Fire (kill pods)</li>
            <li><strong>Space</strong> - Open doors</li>
          </ul>
        </div>
        <div class="instruction-card">
          <h4>3. Watch the Chaos</h4>
          <p>Each demon you kill represents a Kubernetes pod. Watch them get recreated automatically!</p>
        </div>
      </div>
    </div>

    <!-- Pod Monitor Section -->
    <div class="monitor-section">
      <h2>📊 Live Pod Monitor</h2>
      <div class="monitor-controls">
        <button class="btn role-secondary" @click="toggleMonitoring">
          {{ isMonitoring ? 'Stop' : 'Start' }} Monitoring
        </button>
        <span class="monitor-status">
          {{ isMonitoring ? 'Monitoring active' : 'Monitoring stopped' }}
        </span>
      </div>
      
      <div class="pod-list" v-if="pods.length > 0">
        <div v-for="pod in pods" :key="pod.name" class="pod-item">
          <div class="pod-info">
            <span class="pod-name">{{ pod.name }}</span>
            <span class="pod-namespace">{{ pod.namespace }}</span>
          </div>
          <div class="pod-status">
            <span :class="['status-badge', pod.status.toLowerCase()]">
              {{ pod.status }}
            </span>
            <span class="pod-age">{{ pod.age }}</span>
          </div>
        </div>
      </div>
      
      <div v-else class="no-pods">
        <p>No pods found in kubedoom-demo namespace</p>
        <button class="btn role-primary" @click="deployDemoPods">
          Deploy Demo Pods
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomPage',
  
  data() {
    return {
      vncStatus: 'Checking...',
      podCount: 0,
      gameStatus: 'Ready',
      isMonitoring: false,
      pods: [],
      monitoringInterval: null
    };
  },

  mounted() {
    this.checkVNCStatus();
    this.loadPods();
  },

  beforeDestroy() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
  },

  methods: {
    openVNC() {
      window.open('http://localhost:8080', '_blank');
    },

    async checkVNCStatus() {
      try {
        const response = await fetch('http://localhost:8080');
        this.vncStatus = response.ok ? 'Online' : 'Offline';
      } catch (error) {
        this.vncStatus = 'Offline';
      }
    },

    async loadPods() {
      // Simulate loading pods - in a real extension, this would use the Rancher API
      this.pods = [
        { name: 'nginx-demo-1', namespace: 'kubedoom-demo', status: 'Running', age: '5m' },
        { name: 'nginx-demo-2', namespace: 'kubedoom-demo', status: 'Running', age: '5m' },
        { name: 'redis-demo-1', namespace: 'kubedoom-demo', status: 'Running', age: '4m' },
        { name: 'busybox-demo-1', namespace: 'kubedoom-demo', status: 'Running', age: '3m' }
      ];
      this.podCount = this.pods.length;
    },

    refreshStatus() {
      this.checkVNCStatus();
      this.loadPods();
    },

    toggleMonitoring() {
      if (this.isMonitoring) {
        clearInterval(this.monitoringInterval);
        this.isMonitoring = false;
      } else {
        this.monitoringInterval = setInterval(() => {
          this.loadPods();
        }, 2000);
        this.isMonitoring = true;
      }
    },

    deployDemoPods() {
      // In a real extension, this would trigger pod deployment
      this.$store.dispatch('growl/success', {
        title: 'Demo Pods',
        message: 'Demo pods deployment initiated. Check your cluster!'
      });
    }
  }
};
</script>

<style scoped>
.kubedoom-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.kubedoom-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  color: white;
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kubedoom-title {
  font-size: 2.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.kubedoom-subtitle {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.status-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header h3 {
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.small-text {
  color: #6c757d;
  margin: 0;
}

.instructions-section, .monitor-section {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 30px;
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.instruction-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.monitor-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.pod-list {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.pod-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e5e9;
}

.pod-item:last-child {
  border-bottom: none;
}

.pod-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.pod-name {
  font-weight: 600;
}

.pod-namespace {
  color: #6c757d;
  font-size: 0.9rem;
}

.pod-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.running {
  background: #d4edda;
  color: #155724;
}

.no-pods {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}
</style>
