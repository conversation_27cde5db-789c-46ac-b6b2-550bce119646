<template>
  <div class="kubedoom-dashboard">
    <div class="dashboard-header">
      <h1>KubeDoom Dashboard</h1>
      <p>Monitor your cluster's chaos in real-time</p>
    </div>

    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-value">{{ totalKills }}</div>
        <div class="metric-label">Total Pods Killed</div>
        <div class="metric-trend">+{{ recentKills }} in last hour</div>
      </div>

      <div class="metric-card">
        <div class="metric-value">{{ activeTargets }}</div>
        <div class="metric-label">Active Targets</div>
        <div class="metric-trend">{{ targetNamespaces }} namespaces</div>
      </div>

      <div class="metric-card">
        <div class="metric-value">{{ uptime }}</div>
        <div class="metric-label">Game Uptime</div>
        <div class="metric-trend">{{ gameStatus }}</div>
      </div>

      <div class="metric-card">
        <div class="metric-value">{{ recoveryTime }}</div>
        <div class="metric-label">Avg Recovery Time</div>
        <div class="metric-trend">{{ recoveryTrend }}</div>
      </div>
    </div>

    <div class="charts-section">
      <div class="chart-card">
        <h3>Pod Kill Activity</h3>
        <div class="chart-placeholder">
          <div class="activity-timeline">
            <div v-for="(event, index) in killEvents" :key="index" class="timeline-event">
              <div class="event-time">{{ event.time }}</div>
              <div class="event-description">{{ event.description }}</div>
              <div class="event-status">{{ event.status }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-card">
        <h3>Cluster Health</h3>
        <div class="health-indicators">
          <div class="health-item">
            <span class="health-label">Nodes</span>
            <span class="health-value healthy">{{ nodeCount }} Healthy</span>
          </div>
          <div class="health-item">
            <span class="health-label">Pods</span>
            <span class="health-value healthy">{{ podCount }} Running</span>
          </div>
          <div class="health-item">
            <span class="health-label">Services</span>
            <span class="health-value healthy">{{ serviceCount }} Active</span>
          </div>
          <div class="health-item">
            <span class="health-label">Deployments</span>
            <span class="health-value healthy">{{ deploymentCount }} Ready</span>
          </div>
        </div>
      </div>
    </div>

    <div class="actions-section">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <button class="btn role-primary" @click="openVNC">
          <i class="icon icon-external-link"></i>
          Launch Game
        </button>
        <button class="btn role-secondary" @click="deployTargets">
          <i class="icon icon-plus"></i>
          Deploy Targets
        </button>
        <button class="btn role-secondary" @click="resetGame">
          <i class="icon icon-refresh"></i>
          Reset Game
        </button>
        <button class="btn role-tertiary" @click="exportLogs">
          <i class="icon icon-download"></i>
          Export Logs
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomDashboard',
  
  data() {
    return {
      totalKills: 42,
      recentKills: 7,
      activeTargets: 12,
      targetNamespaces: 3,
      uptime: '2h 34m',
      gameStatus: 'Active',
      recoveryTime: '15s',
      recoveryTrend: 'Improving',
      nodeCount: 1,
      podCount: 15,
      serviceCount: 8,
      deploymentCount: 4,
      killEvents: [
        { time: '14:32', description: 'nginx-demo-1 eliminated', status: 'Recovered' },
        { time: '14:28', description: 'redis-demo-2 destroyed', status: 'Recovered' },
        { time: '14:25', description: 'busybox-demo-1 terminated', status: 'Recovered' },
        { time: '14:20', description: 'nginx-demo-3 killed', status: 'Recovered' },
        { time: '14:15', description: 'redis-demo-1 eliminated', status: 'Recovered' }
      ]
    };
  },

  mounted() {
    this.startRealTimeUpdates();
  },

  beforeDestroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  },

  methods: {
    openVNC() {
      window.open('http://localhost:8080', '_blank');
    },

    deployTargets() {
      this.$store.dispatch('growl/info', {
        title: 'Deploying Targets',
        message: 'New target pods are being deployed...'
      });
    },

    resetGame() {
      this.$store.dispatch('growl/warning', {
        title: 'Game Reset',
        message: 'KubeDoom game has been reset'
      });
    },

    exportLogs() {
      this.$store.dispatch('growl/success', {
        title: 'Export Started',
        message: 'Game logs are being exported...'
      });
    },

    startRealTimeUpdates() {
      this.updateInterval = setInterval(() => {
        // Simulate real-time updates
        if (Math.random() > 0.7) {
          this.totalKills += 1;
          this.recentKills += 1;
        }
      }, 5000);
    }
  }
};
</script>

<style scoped>
.kubedoom-dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.dashboard-header p {
  font-size: 1.2rem;
  color: #6c757d;
  margin: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.metric-label {
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 0.9rem;
  color: #28a745;
  font-weight: 500;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.activity-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.timeline-event {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f1f3f4;
}

.timeline-event:last-child {
  border-bottom: none;
}

.event-time {
  font-weight: 600;
  color: #6c757d;
  min-width: 60px;
}

.event-description {
  flex: 1;
  margin: 0 15px;
}

.event-status {
  color: #28a745;
  font-size: 0.9rem;
  font-weight: 500;
}

.health-indicators {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.health-label {
  font-weight: 600;
  color: #2c3e50;
}

.health-value.healthy {
  color: #28a745;
  font-weight: 600;
}

.actions-section {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.actions-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
