import { IPlugin } from '@shell/core/types';
import { Product } from './types/product';

export const NAME = 'kubedoom';

export function init($plugin: IPlugin, store: any) {
  const { product } = $plugin.DSL(store, Product.name);

  // registering a cluster-level product
  product({
    inStore: 'management',
    icon: 'icon-kubedoom',
    label: 'KubeDoom',
    removable: false,
    showClusterSwitcher: true,
    category: 'global',
    to: {
      name: 'kubedoom',
      params: { cluster: 'local' }
    }
  } as any);
}
