import { IPlugin } from '@shell/core/types';

export const NAME = 'kubedoom';

export function init($plugin: IPlugin, store: any) {
  const { product } = $plugin.DSL(store, NAME);

  // registering a cluster-level product
  product({
    inStore: 'management',
    icon: 'icon-kubedoom',
    label: 'KubeDoom',
    removable: false,
    showClusterSwitcher: true,
    category: 'global',
    to: {
      name: 'kubedoom',
      params: { cluster: 'local' }
    }
  } as any);
}
