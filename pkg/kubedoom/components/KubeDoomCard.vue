<template>
  <div class="kubedoom-card">
    <div class="card-header">
      <div class="title-section">
        <h3 class="card-title">
          <i class="icon icon-pipeline"></i>
          KubeDoom
        </h3>
        <div class="status-indicator" :class="statusClass">
          {{ gameStatus }}
        </div>
      </div>
      <button class="btn role-primary btn-sm" @click="openGame">
        <i class="icon icon-external-link"></i>
        Play
      </button>
    </div>

    <div class="card-content">
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-value">{{ targetPods }}</div>
          <div class="stat-label">Target Pods</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ killCount }}</div>
          <div class="stat-label">Kills Today</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ uptime }}</div>
          <div class="stat-label">Uptime</div>
        </div>
      </div>

      <div class="recent-activity">
        <h4>Recent Activity</h4>
        <div class="activity-list">
          <div v-for="(activity, index) in recentActivities" :key="index" class="activity-item">
            <div class="activity-icon">
              <i class="icon icon-dot" :class="activity.iconClass"></i>
            </div>
            <div class="activity-content">
              <div class="activity-text">{{ activity.text }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <button class="btn role-secondary btn-sm" @click="openDashboard">
          <i class="icon icon-dashboard"></i>
          Dashboard
        </button>
        <button class="btn role-tertiary btn-sm" @click="deployTargets">
          <i class="icon icon-plus"></i>
          Deploy Targets
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomCard',
  
  data() {
    return {
      gameStatus: 'Active',
      targetPods: 12,
      killCount: 8,
      uptime: '2h 15m',
      recentActivities: [
        {
          text: 'nginx-demo-1 eliminated',
          time: '2 min ago',
          iconClass: 'text-error'
        },
        {
          text: 'Pod recovered successfully',
          time: '2 min ago',
          iconClass: 'text-success'
        },
        {
          text: 'redis-demo-2 destroyed',
          time: '5 min ago',
          iconClass: 'text-error'
        },
        {
          text: 'New target deployed',
          time: '8 min ago',
          iconClass: 'text-info'
        }
      ]
    };
  },

  computed: {
    statusClass() {
      return {
        'status-active': this.gameStatus === 'Active',
        'status-inactive': this.gameStatus === 'Inactive',
        'status-error': this.gameStatus === 'Error'
      };
    }
  },

  mounted() {
    this.checkGameStatus();
    this.startStatusUpdates();
  },

  beforeDestroy() {
    if (this.statusInterval) {
      clearInterval(this.statusInterval);
    }
  },

  methods: {
    openGame() {
      window.open('http://localhost:8080', '_blank');
    },

    openDashboard() {
      this.$router.push({ name: 'kubedoom-dashboard' });
    },

    deployTargets() {
      this.$store.dispatch('growl/info', {
        title: 'Deploying Targets',
        message: 'New demo pods are being deployed for KubeDoom'
      });
      
      // Simulate target deployment
      setTimeout(() => {
        this.targetPods += 3;
        this.$store.dispatch('growl/success', {
          title: 'Targets Deployed',
          message: '3 new target pods are now available'
        });
      }, 2000);
    },

    async checkGameStatus() {
      try {
        const response = await fetch('http://localhost:8080');
        this.gameStatus = response.ok ? 'Active' : 'Inactive';
      } catch (error) {
        this.gameStatus = 'Inactive';
      }
    },

    startStatusUpdates() {
      this.statusInterval = setInterval(() => {
        this.checkGameStatus();
        
        // Simulate activity updates
        if (Math.random() > 0.8) {
          this.killCount += 1;
          this.addRecentActivity();
        }
      }, 10000);
    },

    addRecentActivity() {
      const activities = [
        'nginx-demo-x eliminated',
        'redis-demo-x destroyed',
        'busybox-demo-x terminated',
        'Pod recovered successfully'
      ];
      
      const newActivity = {
        text: activities[Math.floor(Math.random() * activities.length)],
        time: 'Just now',
        iconClass: Math.random() > 0.5 ? 'text-error' : 'text-success'
      };
      
      this.recentActivities.unshift(newActivity);
      if (this.recentActivities.length > 4) {
        this.recentActivities.pop();
      }
    }
  }
};
</script>

<style scoped>
.kubedoom-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 0.9rem;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  display: inline-block;
}

.status-active {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-inactive {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

.status-error {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.8rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recent-activity {
  flex: 1;
}

.recent-activity h4 {
  margin: 0 0 15px 0;
  font-size: 1rem;
  color: #2c3e50;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 8px 0;
}

.activity-icon {
  margin-top: 2px;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 0.9rem;
  color: #2c3e50;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-time {
  font-size: 0.8rem;
  color: #6c757d;
}

.quick-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.quick-actions .btn {
  flex: 1;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
}
</style>
