{"name": "kubedoom", "description": "Play Doom to kill Kubernetes pods and test cluster resilience", "version": "0.1.0", "private": false, "rancher": {"annotations": {"catalog.cattle.io/kube-version": ">= 1.16.0-0", "catalog.cattle.io/rancher-version": ">= 2.10.0-0", "catalog.cattle.io/ui-extensions-version": ">= 3.0.0 < 4.0.0"}}, "annotations": {"catalog.cattle.io/ui-extensions-version": ">= 3.0.0 < 4.0.0"}, "scripts": {}, "engines": {"node": ">=20"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}