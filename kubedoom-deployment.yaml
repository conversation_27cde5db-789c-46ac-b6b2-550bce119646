apiVersion: v1
kind: ServiceAccount
metadata:
  name: kubedoom
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubedoom
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["list", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubedoom
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubedoom
subjects:
- kind: ServiceAccount
  name: kubedoom
  namespace: default
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubedoom
  namespace: default
  labels:
    app: kubedoom
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubedoom
  template:
    metadata:
      labels:
        app: kubedoom
    spec:
      serviceAccountName: kubedoom
      containers:
      - name: kubedoom
        image: ghcr.io/storax/kubedoom:latest
        ports:
        - containerPort: 5900
          name: vnc
        env:
        - name: NAMESPACE
          value: "default"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: kubedoom
  namespace: default
  labels:
    app: kubedoom
spec:
  selector:
    app: kubedoom
  ports:
  - port: 5900
    targetPort: 5900
    name: vnc
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubedoom-novnc
  namespace: default
  labels:
    app: kubedoom-novnc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubedoom-novnc
  template:
    metadata:
      labels:
        app: kubedoom-novnc
    spec:
      containers:
      - name: novnc
        image: theasp/novnc:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: DISPLAY_WIDTH
          value: "1024"
        - name: DISPLAY_HEIGHT
          value: "768"
        - name: RUN_XTERM
          value: "no"
        - name: VNC_SERVER
          value: "kubedoom:5900"
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 50m
            memory: 64Mi
---
apiVersion: v1
kind: Service
metadata:
  name: kubedoom-novnc
  namespace: default
  labels:
    app: kubedoom-novnc
spec:
  selector:
    app: kubedoom-novnc
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kubedoom-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: "traefik"
spec:
  rules:
  - host: kubedoom.localhost
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kubedoom-novnc
            port:
              number: 8080
