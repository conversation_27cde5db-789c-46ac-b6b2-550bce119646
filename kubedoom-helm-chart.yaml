# KubeDoom Helm Chart Values
# Use this in Rancher Apps when installing KubeDoom

# Basic configuration
replicaCount: 1

image:
  repository: ghcr.io/storax/kubedoom
  tag: latest
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: ClusterIP
  port: 5900
  targetPort: 5900

# VNC configuration
vnc:
  enabled: true
  port: 5900
  password: ""  # Leave empty for no password

# noVNC web interface
novnc:
  enabled: true
  port: 8080
  image:
    repository: theasp/novnc
    tag: latest

# Kubernetes RBAC
rbac:
  create: true

serviceAccount:
  create: true
  name: kubedoom

# Resource limits
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Environment variables
env:
  - name: NAMESPACE
    value: "default"
  - name: KUBECONFIG
    value: "/tmp/config"

# Volume mounts for kubeconfig
volumes:
  - name: kubeconfig
    secret:
      secretName: kubedoom-kubeconfig

volumeMounts:
  - name: kubeconfig
    mountPath: /tmp
    readOnly: true
