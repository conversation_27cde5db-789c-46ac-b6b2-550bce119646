#!/bin/bash

# Create a proper Rancher extension for KubeDoom

echo "🎮 Creating Proper KubeDoom Rancher Extension"
echo "============================================="

# Clean up previous attempts
rm -rf kubedoom-rancher-extension
mkdir -p kubedoom-rancher-extension

cd kubedoom-rancher-extension

# Create package.json for the extension
cat > package.json << 'EOF'
{
  "name": "kubedoom-extension",
  "description": "KubeDoom Extension - Play Doom to kill Kubernetes pods",
  "version": "0.1.0",
  "private": false,
  "rancher": true,
  "engines": {
    "node": ">=16"
  },
  "main": "index.js",
  "files": [
    "**/*"
  ]
}
EOF

# Create the main extension entry point
cat > index.js << 'EOF'
import { importTypes } from '@rancher/auto-import';

// Init the package
export default function(plugin) {
  // Auto-import model, detail, edit from the folders
  importTypes(plugin);

  // Provide plugin metadata from package.json
  plugin.metadata = require('./package.json');

  // Load a product
  plugin.addProduct(require('./config/product'));
}
EOF

# Create config directory and product configuration
mkdir -p config
cat > config/product.js << 'EOF'
export function init($plugin, store) {
  const { product, configureType, headers } = $plugin.DSL(store, $plugin.name);

  // Define the product
  product({
    icon: 'icon-kubedoom',
    inStore: 'management',
    removable: false,
    showClusterSwitcher: true,
    category: 'global'
  });

  // Configure custom types if needed
  configureType('kubedoom.deployment', {
    displayName: 'KubeDoom Deployment',
    isCreatable: true,
    isEditable: true,
    isRemovable: true,
    showAge: true,
    showState: true,
    canYaml: true
  });
}
EOF

# Create models directory
mkdir -p models

# Create pages directory and main page
mkdir -p pages
cat > pages/index.vue << 'EOF'
<template>
  <div class="kubedoom-main">
    <div class="header">
      <h1>
        <i class="icon icon-kubedoom"></i>
        KubeDoom
      </h1>
      <p>Play Doom to kill Kubernetes pods and test cluster resilience</p>
    </div>

    <div class="content">
      <div class="row">
        <div class="col span-6">
          <div class="card">
            <h3>🎮 Start KubeDoom</h3>
            <p>Deploy KubeDoom to your cluster and start playing!</p>
            
            <div class="form-group">
              <label>Select Cluster:</label>
              <select v-model="selectedCluster" class="form-control">
                <option value="">Choose a cluster...</option>
                <option v-for="cluster in clusters" :key="cluster.id" :value="cluster.id">
                  {{ cluster.nameDisplay }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>Select Namespace:</label>
              <select v-model="selectedNamespace" class="form-control">
                <option value="">Choose a namespace...</option>
                <option v-for="ns in namespaces" :key="ns.id" :value="ns.id">
                  {{ ns.nameDisplay }}
                </option>
              </select>
            </div>

            <button 
              class="btn role-primary" 
              :disabled="!selectedCluster || !selectedNamespace"
              @click="deployKubeDoom"
            >
              🚀 Deploy KubeDoom
            </button>
          </div>
        </div>

        <div class="col span-6">
          <div class="card">
            <h3>📊 KubeDoom Status</h3>
            <div v-if="kubeDoomStatus.deployed">
              <p class="text-success">✅ KubeDoom is running!</p>
              <p><strong>VNC URL:</strong> <a :href="kubeDoomStatus.vncUrl" target="_blank">{{ kubeDoomStatus.vncUrl }}</a></p>
              <p><strong>Namespace:</strong> {{ kubeDoomStatus.namespace }}</p>
              <button class="btn role-secondary" @click="openVNC">
                🎮 Play KubeDoom
              </button>
            </div>
            <div v-else>
              <p class="text-muted">KubeDoom is not deployed</p>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-20">
        <div class="col span-12">
          <div class="card">
            <h3>ℹ️ About KubeDoom</h3>
            <p>
              KubeDoom is a tool that allows you to kill Kubernetes pods by playing the classic game Doom.
              It's a fun way to test your cluster's resilience and see how your applications handle pod failures.
            </p>
            <ul>
              <li>🎯 Target pods by shooting them in the game</li>
              <li>🔄 Watch your cluster recover automatically</li>
              <li>📈 Test application resilience and monitoring</li>
              <li>🎮 Have fun while learning about Kubernetes!</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomIndex',

  data() {
    return {
      selectedCluster: '',
      selectedNamespace: '',
      kubeDoomStatus: {
        deployed: false,
        vncUrl: '',
        namespace: ''
      }
    };
  },

  computed: {
    clusters() {
      return this.$store.getters['management/all']('management.cattle.io.cluster') || [];
    },

    namespaces() {
      if (!this.selectedCluster) return [];
      return this.$store.getters['cluster/all']('namespace') || [];
    }
  },

  methods: {
    async deployKubeDoom() {
      try {
        // This would deploy KubeDoom to the selected cluster/namespace
        // For now, we'll simulate the deployment
        this.kubeDoomStatus = {
          deployed: true,
          vncUrl: 'http://localhost:8080',
          namespace: this.selectedNamespace
        };
        
        this.$store.dispatch('growl/success', {
          title: 'Success',
          message: 'KubeDoom deployed successfully!'
        });
      } catch (error) {
        this.$store.dispatch('growl/error', {
          title: 'Error',
          message: `Failed to deploy KubeDoom: ${error.message}`
        });
      }
    },

    openVNC() {
      if (this.kubeDoomStatus.vncUrl) {
        window.open(this.kubeDoomStatus.vncUrl, '_blank');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.kubedoom-main {
  padding: 20px;

  .header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
      
      .icon {
        margin-right: 10px;
      }
    }

    p {
      font-size: 1.2em;
      color: #666;
    }
  }

  .card {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
    }
  }

  .form-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .form-control {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;

    &.role-primary {
      background-color: #007cbb;
      color: white;

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }

    &.role-secondary {
      background-color: #6c757d;
      color: white;
    }
  }

  .text-success {
    color: #28a745;
  }

  .text-muted {
    color: #6c757d;
  }

  .mt-20 {
    margin-top: 20px;
  }
}
</style>
EOF

# Create icon
mkdir -p assets/images/icons
cat > assets/images/icons/kubedoom.svg << 'EOF'
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
</svg>
EOF

echo ""
echo "✅ Proper Rancher extension structure created!"
echo ""
echo "📁 Extension files created in: ./kubedoom-rancher-extension/"
echo ""
echo "🎯 Next steps:"
echo "1. This needs to be built using Rancher's extension development tools"
echo "2. Install Node.js and Yarn"
echo "3. Use 'yarn create @rancher/app' to create a proper development environment"
echo "4. Copy these files into the generated structure"
echo "5. Build and package the extension"
echo ""
echo "📖 For detailed instructions, see:"
echo "   https://rancher.github.io/dashboard/extensions/extensions-getting-started"
