const path = require('path');

module.exports = {
  // Disable host check for development
  devServer: {
    disableHostCheck: true,
    port: 4500
  },

  // Configure webpack for extension building
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@shell': path.resolve(__dirname, 'node_modules/@rancher/shell'),
        '@rancher': path.resolve(__dirname, 'node_modules/@rancher')
      }
    },
    externals: {
      // These will be provided by Rancher
      '@rancher/auto-import': '@rancher/auto-import',
      '@shell/core/types': '@shell/core/types'
    }
  },

  // Build configuration for extensions
  chainWebpack: (config) => {
    // Remove the default entry point
    config.entryPoints.delete('app');

    // Add entry points for each extension
    const extensions = ['kubedoom'];
    
    extensions.forEach(ext => {
      config.entry(ext)
        .add(`./pkg/${ext}/index.ts`)
        .end();
    });

    // Configure output
    config.output
      .filename('[name]/[name]-[version].umd.min.js')
      .library('[name]')
      .libraryTarget('umd');

    // Disable HTML plugin since we're building libraries
    config.plugins.delete('html');
    config.plugins.delete('preload');
    config.plugins.delete('prefetch');
  }
};
