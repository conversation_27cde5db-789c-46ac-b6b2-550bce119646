#!/bin/bash

# Install KubeDoom Extension directly into Rancher

echo "🎮 Installing KubeDoom Extension into Rancher"
echo "============================================="

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl not found. Please install kubectl first."
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl get nodes &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

echo "✅ Connected to Kubernetes cluster"

# Create the cattle-ui-plugin-system namespace if it doesn't exist
echo "📦 Creating cattle-ui-plugin-system namespace..."
kubectl create namespace cattle-ui-plugin-system --dry-run=client -o yaml | kubectl apply -f -

# Create a simple HTML page for KubeDoom
echo "📄 Creating KubeDoom HTML interface..."
cat > kubedoom-interface.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KubeDoom</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007cbb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #005a8b;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 KubeDoom</h1>
            <p>Play Doom to kill Kubernetes pods and test cluster resilience</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🚀 Quick Start</h3>
                <p>KubeDoom is now available in your cluster! Here's how to get started:</p>
                <ol>
                    <li>Access the VNC interface at <strong>http://localhost:8080</strong></li>
                    <li>Use WASD keys to move around</li>
                    <li>Use mouse to look around</li>
                    <li>Press Ctrl to fire and kill pods</li>
                    <li>Watch your cluster recover automatically!</li>
                </ol>
                <a href="http://localhost:8080" target="_blank" class="btn">🎮 Open KubeDoom VNC</a>
            </div>

            <div class="card">
                <h3>📊 Status</h3>
                <div class="status success">
                    ✅ KubeDoom Extension Installed
                </div>
                <div class="status info">
                    ℹ️ VNC Server: http://localhost:8080
                </div>
                <div class="status info">
                    ℹ️ Extension Server: http://extension-server:8000
                </div>
                <p><strong>Namespace:</strong> cattle-ui-plugin-system</p>
                <p><strong>Version:</strong> 0.1.0</p>
            </div>
        </div>

        <div class="card">
            <h3>ℹ️ About KubeDoom</h3>
            <p>
                KubeDoom is a tool that allows you to kill Kubernetes pods by playing the classic game Doom.
                It's a fun way to test your cluster's resilience and see how your applications handle pod failures.
            </p>
            <ul>
                <li>🎯 Target pods by shooting them in the game</li>
                <li>🔄 Watch your cluster recover automatically</li>
                <li>📈 Test application resilience and monitoring</li>
                <li>🎮 Have fun while learning about Kubernetes!</li>
            </ul>
        </div>

        <div class="card">
            <h3>🛠️ Commands</h3>
            <p>Useful commands for managing KubeDoom:</p>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
# Check KubeDoom pods
kubectl get pods -n default | grep kubedoom

# View KubeDoom logs
kubectl logs -n default deployment/kubedoom

# Restart KubeDoom
kubectl rollout restart deployment/kubedoom -n default

# Access VNC directly
kubectl port-forward -n default svc/kubedoom 5900:5900
            </pre>
        </div>
    </div>

    <script>
        // Simple JavaScript to enhance the interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 KubeDoom Extension Loaded');
            
            // Check if VNC is accessible
            fetch('http://localhost:8080')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ VNC Server is accessible');
                    }
                })
                .catch(error => {
                    console.log('⚠️ VNC Server may not be running');
                });
        });
    </script>
</body>
</html>
EOF

# Create a ConfigMap with the HTML content
echo "📦 Creating ConfigMap with KubeDoom interface..."
kubectl create configmap kubedoom-interface \
    --from-file=index.html=kubedoom-interface.html \
    -n cattle-ui-plugin-system \
    --dry-run=client -o yaml | kubectl apply -f -

# Create a simple web server deployment to serve the interface
echo "🌐 Creating web server deployment..."
cat > kubedoom-webserver.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubedoom-webserver
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-webserver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubedoom-webserver
  template:
    metadata:
      labels:
        app: kubedoom-webserver
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: kubedoom-interface
          mountPath: /usr/share/nginx/html
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: kubedoom-interface
        configMap:
          name: kubedoom-interface
---
apiVersion: v1
kind: Service
metadata:
  name: kubedoom-webserver
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-webserver
spec:
  selector:
    app: kubedoom-webserver
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
EOF

kubectl apply -f kubedoom-webserver.yaml

# Create the UIExtension resource
echo "🔌 Creating UIExtension resource..."
cat > kubedoom-uiextension.yaml << 'EOF'
apiVersion: management.cattle.io/v3
kind: UIExtension
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-extension
  annotations:
    meta.helm.sh/release-name: kubedoom-extension
    meta.helm.sh/release-namespace: cattle-ui-plugin-system
spec:
  plugin:
    name: kubedoom-extension
    version: 0.1.0
    endpoint: "http://kubedoom-webserver.cattle-ui-plugin-system.svc.cluster.local"
    noCache: false
    metadata:
      displayName: "KubeDoom"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
EOF

kubectl apply -f kubedoom-uiextension.yaml

echo ""
echo "✅ KubeDoom Extension installed successfully!"
echo ""
echo "📋 Installation Summary:"
echo "  ✅ Namespace: cattle-ui-plugin-system"
echo "  ✅ ConfigMap: kubedoom-interface"
echo "  ✅ Deployment: kubedoom-webserver"
echo "  ✅ Service: kubedoom-webserver"
echo "  ✅ UIExtension: kubedoom-extension"
echo ""
echo "🎯 Next Steps:"
echo "1. Refresh your Rancher UI (F5 or Ctrl+R)"
echo "2. Look for 'KubeDoom' in the main navigation menu"
echo "3. If it doesn't appear, wait a few minutes and refresh again"
echo "4. Check the extension status with: kubectl get uiextensions -n cattle-ui-plugin-system"
echo ""
echo "🔍 Troubleshooting:"
echo "  View logs: kubectl logs -n cattle-ui-plugin-system deployment/kubedoom-webserver"
echo "  Check status: kubectl get all -n cattle-ui-plugin-system"
echo "  Delete extension: kubectl delete uiextension kubedoom-extension -n cattle-ui-plugin-system"

# Clean up temporary files
rm -f kubedoom-interface.html kubedoom-webserver.yaml kubedoom-uiextension.yaml
