#!/bin/bash

# KubeDoom Extension Status Check Script

echo "🎮 KubeDoom Extension - Status Check"
echo "===================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[STATUS]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo ""
print_status "Checking Docker containers..."

# Check Rancher
if docker ps | grep -q rancher-server; then
    print_success "Rancher server is running"
    RANCHER_STATUS="✅"
else
    print_error "Rancher server is not running"
    RANCHER_STATUS="❌"
fi

# Check Extension Server
if docker ps | grep -q extension-server; then
    print_success "Extension server is running"
    EXTENSION_SERVER_STATUS="✅"
else
    print_error "Extension server is not running"
    EXTENSION_SERVER_STATUS="❌"
fi

# Check KubeDoom VNC
if docker ps | grep -q kubedoom; then
    print_success "KubeDoom VNC is running"
    KUBEDOOM_VNC_STATUS="✅"
else
    print_warning "KubeDoom VNC is not running (will be started when needed)"
    KUBEDOOM_VNC_STATUS="⚠️"
fi

echo ""
print_status "Checking services..."

# Check Rancher UI
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost | grep -q "200\|302"; then
    print_success "Rancher UI is accessible at https://localhost"
    RANCHER_UI_STATUS="✅"
else
    print_error "Rancher UI is not accessible"
    RANCHER_UI_STATUS="❌"
fi

# Check Extension Server
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/kubedoom-0.1.0/package.json | grep -q "200"; then
    print_success "Extension is accessible at http://localhost:8000"
    EXTENSION_ACCESS_STATUS="✅"
else
    print_error "Extension is not accessible"
    EXTENSION_ACCESS_STATUS="❌"
fi

# Check VNC
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200"; then
    print_success "KubeDoom VNC is accessible at http://localhost:8080"
    VNC_STATUS="✅"
else
    print_warning "KubeDoom VNC is not accessible (normal if not deployed)"
    VNC_STATUS="⚠️"
fi

echo ""
print_status "Checking KubeDoom extension..."

# Check UIPlugin
if docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system kubedoom &> /dev/null; then
    PLUGIN_STATE=$(docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system kubedoom -o jsonpath='{.status.state}' 2>/dev/null || echo "unknown")
    case $PLUGIN_STATE in
        "cached")
            print_success "KubeDoom extension is installed and cached"
            EXTENSION_STATUS="✅ Cached"
            ;;
        "pending")
            print_warning "KubeDoom extension is pending (loading)"
            EXTENSION_STATUS="⏳ Pending"
            ;;
        "error")
            print_error "KubeDoom extension has an error"
            EXTENSION_STATUS="❌ Error"
            ;;
        *)
            print_warning "KubeDoom extension state: $PLUGIN_STATE"
            EXTENSION_STATUS="⚠️ $PLUGIN_STATE"
            ;;
    esac
else
    print_error "KubeDoom extension is not installed"
    EXTENSION_STATUS="❌ Not Installed"
fi

echo ""
echo "📊 Overall Status Summary:"
echo "=========================="
echo ""
echo "🐳 Docker Services:"
echo "  Rancher Server:     $RANCHER_STATUS"
echo "  Extension Server:   $EXTENSION_SERVER_STATUS"
echo "  KubeDoom VNC:       $KUBEDOOM_VNC_STATUS"
echo ""
echo "🌐 Web Services:"
echo "  Rancher UI:         $RANCHER_UI_STATUS https://localhost"
echo "  Extension Server:   $EXTENSION_ACCESS_STATUS http://localhost:8000"
echo "  KubeDoom VNC:       $VNC_STATUS http://localhost:8080"
echo ""
echo "🎮 KubeDoom Extension:"
echo "  Installation:       $EXTENSION_STATUS"
echo ""

if [[ "$EXTENSION_STATUS" == *"Cached"* ]]; then
    echo "🎯 Ready to Use!"
    echo "==============="
    echo ""
    echo "1. Open Rancher UI: https://localhost"
    echo "2. Login with: admin / admin123"
    echo "3. Look for 'KubeDoom' in the navigation menu"
    echo "4. Click on KubeDoom to start using it"
    echo ""
elif [[ "$EXTENSION_STATUS" == *"Pending"* ]]; then
    echo "⏳ Extension Loading..."
    echo "====================="
    echo ""
    echo "The extension is still loading. Please wait 1-2 minutes and:"
    echo "1. Refresh your Rancher UI (F5)"
    echo "2. Check status again: ./kubedoom-status.sh"
    echo ""
else
    echo "🔧 Troubleshooting Required"
    echo "=========================="
    echo ""
    echo "Some services are not working properly. Try:"
    echo "1. Restart services: docker-compose restart"
    echo "2. Reinstall extension: ./install-kubedoom-final.sh"
    echo "3. Check logs: docker-compose logs"
    echo ""
fi

echo "🛠️ Useful Commands:"
echo "=================="
echo ""
echo "Check extension details:"
echo "  docker exec rancher-server kubectl describe uiplugin kubedoom -n cattle-ui-plugin-system"
echo ""
echo "View extension logs:"
echo "  docker-compose logs extension-server"
echo ""
echo "Restart all services:"
echo "  docker-compose restart"
echo ""
echo "Uninstall extension:"
echo "  docker exec rancher-server kubectl delete uiplugin kubedoom -n cattle-ui-plugin-system"
