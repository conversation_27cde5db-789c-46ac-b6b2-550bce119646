#!/bin/bash

# Kube<PERSON>oom Extension Development Setup Script
# This script sets up a complete development environment for the KubeDoom Rancher extension

set -e

echo "🎮 Setting up KubeDoom Extension Development Environment"
echo "======================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 20+ first."
        exit 1
    fi
    
    # Check Yarn
    if ! command -v yarn &> /dev/null; then
        print_warning "Yarn is not installed. Installing yarn..."
        npm install -g yarn
    fi
    
    print_success "All prerequisites are installed!"
}

# Build the extension
build_extension() {
    print_status "Building KubeDoom extension..."
    
    cd kubedoom-extension
    
    # Install dependencies
    print_status "Installing dependencies..."
    yarn install --ignore-engines
    
    # Build the extension
    print_status "Building extension..."
    yarn build-pkg kubedoom-extension
    
    cd ..
    
    print_success "Extension built successfully!"
}

# Start the development environment
start_environment() {
    print_status "Starting development environment..."
    
    # Create necessary directories
    mkdir -p ./data/rancher
    mkdir -p ./data/k3s
    
    # Start Docker Compose services
    print_status "Starting Docker containers..."
    docker-compose up -d
    
    print_status "Waiting for services to start..."
    sleep 30
    
    # Wait for Rancher to be ready
    print_status "Waiting for Rancher to be ready..."
    while ! curl -k -s https://localhost/ping > /dev/null 2>&1; do
        echo -n "."
        sleep 5
    done
    echo ""
    
    print_success "Development environment is ready!"
}

# Extract kubeconfig from k3s
extract_kubeconfig() {
    print_status "Extracting kubeconfig from k3s..."
    
    # Wait for k3s to generate kubeconfig
    sleep 10
    
    # Copy kubeconfig from k3s container
    docker cp k3s-server:/etc/rancher/k3s/k3s.yaml ./k3s-kubeconfig.yaml
    
    # Update server URL in kubeconfig
    sed -i 's/127.0.0.1/k3s-server/g' ./k3s-kubeconfig.yaml
    
    print_success "Kubeconfig extracted successfully!"
}

# Setup Rancher cluster import
setup_cluster_import() {
    print_status "Setting up cluster import in Rancher..."
    
    # This would typically involve API calls to Rancher
    # For now, we'll provide manual instructions
    
    print_warning "Manual step required:"
    echo "1. Open https://localhost in your browser"
    echo "2. Login with admin/admin123"
    echo "3. Go to Cluster Management"
    echo "4. Click 'Import Existing' cluster"
    echo "5. Choose 'Generic' and follow the instructions"
    echo "6. Use the kubeconfig from ./k3s-kubeconfig.yaml"
}

# Install sample applications for testing
install_sample_apps() {
    print_status "Installing sample applications for KubeDoom testing..."
    
    # Apply sample deployments to k3s
    docker exec k3s-server kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-deployment
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:alpine
        ports:
        - containerPort: 6379
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: busybox-deployment
  namespace: default
spec:
  replicas: 5
  selector:
    matchLabels:
      app: busybox
  template:
    metadata:
      labels:
        app: busybox
    spec:
      containers:
      - name: busybox
        image: busybox
        command: ['sleep', '3600']
EOF

    print_success "Sample applications installed!"
}

# Display access information
show_access_info() {
    echo ""
    echo "🎮 KubeDoom Extension Development Environment Ready!"
    echo "=================================================="
    echo ""
    echo "📊 Rancher UI: https://localhost"
    echo "   Username: admin"
    echo "   Password: admin123"
    echo ""
    echo "🎯 KubeDoom VNC (direct): vnc://localhost:5900"
    echo "🌐 KubeDoom Web VNC: http://localhost:8080"
    echo ""
    echo "⚙️  K3s Cluster API: https://localhost:6443"
    echo "📄 Kubeconfig: ./k3s-kubeconfig.yaml"
    echo ""
    echo "🔧 Development Commands:"
    echo "   - Build extension: cd kubedoom-extension && yarn build-pkg kubedoom-extension"
    echo "   - Watch mode: cd kubedoom-extension && yarn dev"
    echo "   - View logs: docker-compose logs -f rancher"
    echo "   - Stop environment: docker-compose down"
    echo ""
    echo "📝 Next Steps:"
    echo "   1. Open Rancher UI and complete initial setup"
    echo "   2. Import the K3s cluster using the provided kubeconfig"
    echo "   3. Install the KubeDoom extension"
    echo "   4. Navigate to the KubeDoom product in Rancher"
    echo "   5. Start playing Doom to kill pods!"
    echo ""
}

# Cleanup function
cleanup() {
    print_status "Cleaning up development environment..."
    docker-compose down -v
    docker system prune -f
    rm -f ./k3s-kubeconfig.yaml
    print_success "Cleanup completed!"
}

# Main execution
main() {
    case "${1:-setup}" in
        "setup")
            check_prerequisites
            build_extension
            start_environment
            extract_kubeconfig
            install_sample_apps
            setup_cluster_import
            show_access_info
            ;;
        "cleanup")
            cleanup
            ;;
        "build")
            build_extension
            ;;
        "start")
            start_environment
            ;;
        "stop")
            docker-compose down
            ;;
        "logs")
            docker-compose logs -f "${2:-rancher}"
            ;;
        "help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  setup     - Full setup (default)"
            echo "  cleanup   - Clean up environment"
            echo "  build     - Build extension only"
            echo "  start     - Start environment only"
            echo "  stop      - Stop environment"
            echo "  logs      - View logs (specify service name)"
            echo "  help      - Show this help"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
