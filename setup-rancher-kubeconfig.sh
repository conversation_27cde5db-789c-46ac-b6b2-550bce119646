#!/bin/bash

# Setup Rancher KubeConfig for KubeDoom
echo "🔧 Setting up Rancher KubeConfig for KubeDoom"
echo "=============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Wait for Rancher to be ready
print_step "Waiting for Rancher to be ready..."
timeout=300
counter=0
while [ $counter -lt $timeout ]; do
    if docker exec rancher-server kubectl get nodes &> /dev/null; then
        print_success "Rancher is ready!"
        break
    fi
    sleep 5
    counter=$((counter + 5))
    echo -n "."
done

if [ $counter -ge $timeout ]; then
    print_error "Rancher did not become ready within $timeout seconds"
    exit 1
fi

# Extract kubeconfig from Rancher
print_step "Extracting kubeconfig from Rancher..."
docker exec rancher-server kubectl config view --raw > rancher-kubeconfig.yaml

if [ $? -eq 0 ]; then
    print_success "Kubeconfig extracted successfully"
else
    print_error "Failed to extract kubeconfig"
    exit 1
fi

# Update server address to use container name
print_step "Updating server address for container networking..."
sed -i.bak 's|server: https://127.0.0.1:6443|server: https://rancher-server:6443|g' rancher-kubeconfig.yaml

if [ $? -eq 0 ]; then
    print_success "Server address updated"
    rm -f rancher-kubeconfig.yaml.bak
else
    print_error "Failed to update server address"
    exit 1
fi

# Verify the kubeconfig
print_step "Verifying kubeconfig..."
if grep -q "rancher-server:6443" rancher-kubeconfig.yaml; then
    print_success "Kubeconfig is properly configured"
else
    print_error "Kubeconfig verification failed"
    exit 1
fi

print_success "Rancher kubeconfig setup complete!"
echo ""
print_info "📋 Summary:"
echo "  ✅ Kubeconfig file: rancher-kubeconfig.yaml"
echo "  ✅ Server: https://rancher-server:6443"
echo "  ✅ Ready for KubeDoom container"
echo ""
print_info "🎯 Next steps:"
echo "1. Restart KubeDoom container: docker-compose restart kubedoom"
echo "2. Deploy demo applications to Rancher's cluster"
echo "3. Start playing KubeDoom!"
