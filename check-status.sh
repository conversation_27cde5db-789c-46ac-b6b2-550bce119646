#!/bin/bash

# Quick status check for KubeDoom Extension Development Environment

echo "🎮 KubeDoom Extension - Environment Status"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Check Docker containers
echo ""
echo "📦 Docker Containers:"
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "🌐 Service URLs:"

# Check Rancher
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ping | grep -q "200"; then
    echo -e "  ✅ Rancher UI: ${GREEN}https://localhost${NC} (admin/admin123)"
else
    echo -e "  ❌ Rancher UI: ${RED}https://localhost${NC} (not ready)"
fi

# Check noVNC
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200"; then
    echo -e "  ✅ KubeDoom VNC: ${GREEN}http://localhost:8080${NC}"
else
    echo -e "  ❌ KubeDoom VNC: ${RED}http://localhost:8080${NC} (not ready)"
fi

# Check K3s API
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost:6443/version | grep -q "200"; then
    echo -e "  ✅ K3s API: ${GREEN}https://localhost:6443${NC}"
else
    echo -e "  ⏳ K3s API: ${YELLOW}https://localhost:6443${NC} (starting up)"
fi

echo ""
echo "📊 Quick Commands:"
echo "  View logs: docker-compose logs -f [service-name]"
echo "  Restart: docker-compose restart [service-name]"
echo "  Stop all: docker-compose down"
echo "  Rebuild extension: cd kubedoom-extension && yarn build-pkg kubedoom-extension"

echo ""
echo "🎯 Next Steps:"
echo "  1. Wait for all services to be ready (K3s may take a few minutes)"
echo "  2. Open Rancher UI and complete initial setup"
echo "  3. Import the K3s cluster"
echo "  4. Install the KubeDoom extension"
echo "  5. Start playing Doom to kill pods!"
