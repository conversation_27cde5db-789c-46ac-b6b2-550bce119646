#!/bin/bash

# Final KubeDoom Status Check
echo "🎮 KubeDoom Complete Setup - Final Status"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

echo ""
echo "🐳 Docker Services:"

# Check Rancher
if docker ps | grep -q rancher-server; then
    print_success "Rancher server is running"
    RANCHER_STATUS="✅"
else
    print_error "Rancher server is not running"
    RANCHER_STATUS="❌"
fi

# Check Extension Server
if docker ps | grep -q extension-server; then
    print_success "Extension server is running"
    EXTENSION_STATUS="✅"
else
    print_error "Extension server is not running"
    EXTENSION_STATUS="❌"
fi

# Check KubeDoom
if docker ps | grep -q kubedoom-test; then
    print_success "KubeDoom is running"
    KUBEDOOM_STATUS="✅"
else
    print_error "KubeDoom is not running"
    KUBEDOOM_STATUS="❌"
fi

# Check VNC
if docker ps | grep -q novnc-client; then
    print_success "VNC client is running"
    VNC_STATUS="✅"
else
    print_error "VNC client is not running"
    VNC_STATUS="❌"
fi

echo ""
echo "🌐 Web Services:"

# Check Rancher UI
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost | grep -q "200\|302"; then
    print_success "Rancher UI is accessible at https://localhost"
    RANCHER_UI_STATUS="✅"
else
    print_error "Rancher UI is not accessible"
    RANCHER_UI_STATUS="❌"
fi

# Check Extension Server
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/kubedoom-0.1.0/package.json | grep -q "200"; then
    print_success "Extension server is accessible"
    EXTENSION_ACCESS_STATUS="✅"
else
    print_error "Extension server is not accessible"
    EXTENSION_ACCESS_STATUS="❌"
fi

# Check VNC
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200"; then
    print_success "VNC interface is accessible at http://localhost:8080"
    VNC_WEB_STATUS="✅"
else
    print_error "VNC interface is not accessible"
    VNC_WEB_STATUS="❌"
fi

echo ""
echo "🎮 KubeDoom Extension:"

# Check UIPlugin
if docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system kubedoom &> /dev/null; then
    PLUGIN_STATE=$(docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system kubedoom -o jsonpath='{.status.state}' 2>/dev/null || echo "unknown")
    if [ "$PLUGIN_STATE" = "cached" ]; then
        print_success "KubeDoom extension is installed and cached"
        EXTENSION_INSTALLED="✅ Cached"
    else
        print_info "KubeDoom extension state: $PLUGIN_STATE"
        EXTENSION_INSTALLED="⚠️ $PLUGIN_STATE"
    fi
else
    print_error "KubeDoom extension is not installed"
    EXTENSION_INSTALLED="❌ Not Installed"
fi

echo ""
echo "📊 Summary:"
echo "==========="
echo ""
echo "🐳 Docker Services:"
echo "  Rancher Server:     $RANCHER_STATUS"
echo "  Extension Server:   $EXTENSION_STATUS"
echo "  KubeDoom:           $KUBEDOOM_STATUS"
echo "  VNC Client:         $VNC_STATUS"
echo ""
echo "🌐 Web Services:"
echo "  Rancher UI:         $RANCHER_UI_STATUS https://localhost"
echo "  Extension Server:   $EXTENSION_ACCESS_STATUS http://localhost:8000"
echo "  VNC Interface:      $VNC_WEB_STATUS http://localhost:8080"
echo ""
echo "🎮 KubeDoom Extension:"
echo "  Installation:       $EXTENSION_INSTALLED"
echo ""

if [[ "$EXTENSION_INSTALLED" == *"Cached"* ]]; then
    echo "🎉 SUCCESS! KubeDoom is Ready!"
    echo "=============================="
    echo ""
    echo "🎯 How to Use:"
    echo "1. Open Rancher UI: https://localhost"
    echo "   - Login: admin / admin123"
    echo "   - Look for 'KubeDoom' in the navigation menu"
    echo ""
    echo "2. Click on KubeDoom to access the extension"
    echo "   - You'll see a working extension page"
    echo "   - Click '🎮 Play KubeDoom' to open VNC"
    echo ""
    echo "3. Play KubeDoom at: http://localhost:8080"
    echo "   - Use WASD to move, mouse to look, Ctrl to fire"
    echo "   - Shoot demons to kill Kubernetes pods!"
    echo ""
    echo "🔧 Fixed Issues:"
    echo "  ✅ Removed standalone k3s (using Rancher's embedded k3s)"
    echo "  ✅ Fixed JavaScript bundle (no more HTML parsing errors)"
    echo "  ✅ Simplified extension (working Vue components)"
    echo "  ✅ Proper kubeconfig for Rancher's cluster"
    echo ""
else
    echo "⚠️ Extension Not Ready"
    echo "====================="
    echo ""
    echo "The extension is not fully loaded yet. Try:"
    echo "1. Wait 1-2 minutes and refresh Rancher UI"
    echo "2. Check extension logs: docker-compose logs extension-server"
    echo "3. Reinstall if needed: ./install-kubedoom-final.sh"
fi

echo ""
echo "🛠️ Useful Commands:"
echo "=================="
echo ""
echo "Check extension status:"
echo "  docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system"
echo ""
echo "View extension logs:"
echo "  docker-compose logs extension-server"
echo ""
echo "Test extension URL:"
echo "  curl http://localhost:8000/kubedoom-0.1.0/package.json"
echo ""
echo "Restart services:"
echo "  docker-compose restart"
