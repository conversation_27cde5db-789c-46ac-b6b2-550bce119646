services:
  # Rancher Server
  rancher:
    image: rancher/rancher:v2.11.0
    container_name: rancher-server
    hostname: rancher.local
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - rancher-data:/var/lib/rancher
      - ./kubedoom-extension/dist-pkg:/usr/share/rancher/ui-plugins:ro
    environment:
      - CATTLE_BOOTSTRAP_PASSWORD=admin123
      - CATTLE_PASSWORD_MIN_LENGTH=8
    restart: unless-stopped
    privileged: true
    networks:
      - rancher-network



  # Development container for building the extension
  dev-container:
    image: node:20-alpine
    container_name: kubedoom-dev
    working_dir: /workspace
    volumes:
      - .:/workspace
      - node-modules:/workspace/kubedoom-extension/node_modules
    environment:
      - NODE_ENV=development
    command: sh -c "cd kubedoom-extension && yarn install && yarn dev"
    ports:
      - "4500:4500"  # Development server
    networks:
      - rancher-network
    depends_on:
      - rancher

  # Extension repository server
  extension-server:
    image: python:3.9-alpine
    container_name: extension-server
    working_dir: /app
    volumes:
      - ./dist-pkg:/app/extensions
      - ./serve-extension.py:/app/serve-extension.py
    command: python3 serve-extension.py
    ports:
      - "8000:8000"
    networks:
      - rancher-network
    environment:
      - EXTENSION_DIR=/app/extensions

  # KubeDoom container for testing
  kubedoom:
    image: ghcr.io/storax/kubedoom:latest
    container_name: kubedoom-test
    ports:
      - "5900:5900"  # VNC port
    volumes:
      - ./rancher-kubeconfig.yaml:/root/.kube/config:ro
    environment:
      - NAMESPACE=kubedoom-demo
      - KUBECONFIG=/root/.kube/config
    restart: unless-stopped
    networks:
      - rancher-network
    depends_on:
      - rancher

  # noVNC web client for browser-based VNC access
  novnc:
    image: theasp/novnc:latest
    container_name: novnc-client
    ports:
      - "8080:8080"  # noVNC web interface
    environment:
      - DISPLAY_WIDTH=1024
      - DISPLAY_HEIGHT=768
      - VNC_SERVER=kubedoom:5900
    networks:
      - rancher-network
    depends_on:
      - kubedoom

volumes:
  rancher-data:
    driver: local
  node-modules:
    driver: local

networks:
  rancher-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
