services:
  # Rancher Server
  rancher:
    image: rancher/rancher:v2.11-head
    container_name: rancher-server
    hostname: rancher.local
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - rancher-data:/var/lib/rancher
      - ./kubedoom-extension/dist:/usr/share/rancher/ui-plugins/kubedoom-extension:ro
    environment:
      - CATTLE_BOOTSTRAP_PASSWORD=admin123
      - CATTLE_PASSWORD_MIN_LENGTH=8
    restart: unless-stopped
    privileged: true
    networks:
      - rancher-network

  # K3s cluster for testing
  k3s-server:
    image: rancher/k3s:v1.28.5-k3s1
    container_name: k3s-server
    hostname: k3s-server
    ports:
      - "6443:6443"
    volumes:
      - k3s-data:/var/lib/rancher/k3s
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - K3S_CLUSTER_SECRET=mysecret
      - K3S_KUBECONFIG_OUTPUT=/output/kubeconfig.yaml
      - K3S_KUBECONFIG_MODE=666
    command: server --disable=traefik --disable=servicelb --docker
    restart: unless-stopped
    privileged: true
    networks:
      - rancher-network

  # K3s agent node
  k3s-agent:
    image: rancher/k3s:v1.28.5-k3s1
    container_name: k3s-agent
    hostname: k3s-agent
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - K3S_CLUSTER_SECRET=mysecret
      - K3S_URL=https://k3s-server:6443
    command: agent --docker
    restart: unless-stopped
    privileged: true
    depends_on:
      - k3s-server
    networks:
      - rancher-network

  # Development container for building the extension
  dev-container:
    image: node:20-alpine
    container_name: kubedoom-dev
    working_dir: /workspace
    volumes:
      - .:/workspace
      - node-modules:/workspace/kubedoom-extension/node_modules
    environment:
      - NODE_ENV=development
    command: sh -c "cd kubedoom-extension && yarn install && yarn dev"
    ports:
      - "4500:4500"  # Development server
    networks:
      - rancher-network
    depends_on:
      - rancher

  # KubeDoom container for testing
  kubedoom:
    image: ghcr.io/storax/kubedoom:latest
    container_name: kubedoom-test
    ports:
      - "5900:5900"  # VNC port
    environment:
      - NAMESPACE=default
    volumes:
      - ./k3s-kubeconfig.yaml:/root/.kube/config:ro
    restart: unless-stopped
    networks:
      - rancher-network
    depends_on:
      - k3s-server

  # noVNC web client for browser-based VNC access
  novnc:
    image: theasp/novnc:latest
    container_name: novnc-client
    ports:
      - "8080:8080"  # noVNC web interface
    environment:
      - DISPLAY_WIDTH=1024
      - DISPLAY_HEIGHT=768
      - VNC_SERVER=kubedoom:5900
    networks:
      - rancher-network
    depends_on:
      - kubedoom

volumes:
  rancher-data:
    driver: local
  k3s-data:
    driver: local
  node-modules:
    driver: local

networks:
  rancher-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
