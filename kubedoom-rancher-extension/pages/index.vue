<template>
  <div class="kubedoom-main">
    <div class="header">
      <h1>
        <i class="icon icon-kubedoom"></i>
        KubeDoom
      </h1>
      <p>Play Doom to kill Kubernetes pods and test cluster resilience</p>
    </div>

    <div class="content">
      <div class="row">
        <div class="col span-6">
          <div class="card">
            <h3>🎮 Start KubeDoom</h3>
            <p>Deploy KubeDoom to your cluster and start playing!</p>
            
            <div class="form-group">
              <label>Select Cluster:</label>
              <select v-model="selectedCluster" class="form-control">
                <option value="">Choose a cluster...</option>
                <option v-for="cluster in clusters" :key="cluster.id" :value="cluster.id">
                  {{ cluster.nameDisplay }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>Select Namespace:</label>
              <select v-model="selectedNamespace" class="form-control">
                <option value="">Choose a namespace...</option>
                <option v-for="ns in namespaces" :key="ns.id" :value="ns.id">
                  {{ ns.nameDisplay }}
                </option>
              </select>
            </div>

            <button 
              class="btn role-primary" 
              :disabled="!selectedCluster || !selectedNamespace"
              @click="deployKubeDoom"
            >
              🚀 Deploy KubeDoom
            </button>
          </div>
        </div>

        <div class="col span-6">
          <div class="card">
            <h3>📊 KubeDoom Status</h3>
            <div v-if="kubeDoomStatus.deployed">
              <p class="text-success">✅ KubeDoom is running!</p>
              <p><strong>VNC URL:</strong> <a :href="kubeDoomStatus.vncUrl" target="_blank">{{ kubeDoomStatus.vncUrl }}</a></p>
              <p><strong>Namespace:</strong> {{ kubeDoomStatus.namespace }}</p>
              <button class="btn role-secondary" @click="openVNC">
                🎮 Play KubeDoom
              </button>
            </div>
            <div v-else>
              <p class="text-muted">KubeDoom is not deployed</p>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-20">
        <div class="col span-12">
          <div class="card">
            <h3>ℹ️ About KubeDoom</h3>
            <p>
              KubeDoom is a tool that allows you to kill Kubernetes pods by playing the classic game Doom.
              It's a fun way to test your cluster's resilience and see how your applications handle pod failures.
            </p>
            <ul>
              <li>🎯 Target pods by shooting them in the game</li>
              <li>🔄 Watch your cluster recover automatically</li>
              <li>📈 Test application resilience and monitoring</li>
              <li>🎮 Have fun while learning about Kubernetes!</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomIndex',

  data() {
    return {
      selectedCluster: '',
      selectedNamespace: '',
      kubeDoomStatus: {
        deployed: false,
        vncUrl: '',
        namespace: ''
      }
    };
  },

  computed: {
    clusters() {
      return this.$store.getters['management/all']('management.cattle.io.cluster') || [];
    },

    namespaces() {
      if (!this.selectedCluster) return [];
      return this.$store.getters['cluster/all']('namespace') || [];
    }
  },

  methods: {
    async deployKubeDoom() {
      try {
        // This would deploy KubeDoom to the selected cluster/namespace
        // For now, we'll simulate the deployment
        this.kubeDoomStatus = {
          deployed: true,
          vncUrl: 'http://localhost:8080',
          namespace: this.selectedNamespace
        };
        
        this.$store.dispatch('growl/success', {
          title: 'Success',
          message: 'KubeDoom deployed successfully!'
        });
      } catch (error) {
        this.$store.dispatch('growl/error', {
          title: 'Error',
          message: `Failed to deploy KubeDoom: ${error.message}`
        });
      }
    },

    openVNC() {
      if (this.kubeDoomStatus.vncUrl) {
        window.open(this.kubeDoomStatus.vncUrl, '_blank');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.kubedoom-main {
  padding: 20px;

  .header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
      
      .icon {
        margin-right: 10px;
      }
    }

    p {
      font-size: 1.2em;
      color: #666;
    }
  }

  .card {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
    }
  }

  .form-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .form-control {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;

    &.role-primary {
      background-color: #007cbb;
      color: white;

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }

    &.role-secondary {
      background-color: #6c757d;
      color: white;
    }
  }

  .text-success {
    color: #28a745;
  }

  .text-muted {
    color: #6c757d;
  }

  .mt-20 {
    margin-top: 20px;
  }
}
</style>
