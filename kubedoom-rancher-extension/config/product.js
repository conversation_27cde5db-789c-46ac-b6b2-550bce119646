export function init($plugin, store) {
  const { product, configureType, headers } = $plugin.DSL(store, $plugin.name);

  // Define the product
  product({
    icon: 'icon-kubedoom',
    inStore: 'management',
    removable: false,
    showClusterSwitcher: true,
    category: 'global'
  });

  // Configure custom types if needed
  configureType('kubedoom.deployment', {
    displayName: 'KubeDoom Deployment',
    isCreatable: true,
    isEditable: true,
    isRemovable: true,
    showAge: true,
    showState: true,
    canYaml: true
  });
}
