#!/bin/bash

# Deploy Demo Applications to K3s Cluster for KubeDoom

echo "🚀 Deploying Demo Applications to K3s Cluster"
echo "=============================================="

# Deploy nginx pods (easy targets)
echo "Deploying nginx pods..."
cat << 'EOF' | docker exec -i kubedoom-test kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-demo
  namespace: kubedoom-demo
  labels:
    app: nginx-demo
spec:
  replicas: 5
  selector:
    matchLabels:
      app: nginx-demo
  template:
    metadata:
      labels:
        app: nginx-demo
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "32Mi"
            cpu: "50m"
          limits:
            memory: "64Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-demo-service
  namespace: kubedoom-demo
spec:
  selector:
    app: nginx-demo
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF

# Deploy redis pods
echo "Deploying redis pods..."
cat << 'EOF' | docker exec -i kubedoom-test kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-demo
  namespace: kubedoom-demo
  labels:
    app: redis-demo
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-demo
  template:
    metadata:
      labels:
        app: redis-demo
    spec:
      containers:
      - name: redis
        image: redis:alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "32Mi"
            cpu: "50m"
          limits:
            memory: "64Mi"
            cpu: "100m"
EOF

# Deploy busybox pods (for testing)
echo "Deploying busybox pods..."
cat << 'EOF' | docker exec -i kubedoom-test kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: busybox-demo
  namespace: kubedoom-demo
  labels:
    app: busybox-demo
spec:
  replicas: 4
  selector:
    matchLabels:
      app: busybox-demo
  template:
    metadata:
      labels:
        app: busybox-demo
    spec:
      containers:
      - name: busybox
        image: busybox:latest
        command: ['sleep', '3600']
        resources:
          requests:
            memory: "16Mi"
            cpu: "25m"
          limits:
            memory: "32Mi"
            cpu: "50m"
EOF

# Wait for deployments to be ready
echo "Waiting for deployments to be ready..."
docker exec kubedoom-test kubectl wait --for=condition=available --timeout=120s deployment/nginx-demo -n kubedoom-demo
docker exec kubedoom-test kubectl wait --for=condition=available --timeout=120s deployment/redis-demo -n kubedoom-demo
docker exec kubedoom-test kubectl wait --for=condition=available --timeout=120s deployment/busybox-demo -n kubedoom-demo

# Show the deployed pods
echo ""
echo "✅ Demo applications deployed to K3s cluster!"
echo ""
docker exec kubedoom-test kubectl get pods -n kubedoom-demo -o wide

echo ""
echo "🎮 KubeDoom is now ready!"
echo "========================"
echo ""
echo "📊 Summary:"
echo "  ✅ Namespace: kubedoom-demo"
echo "  ✅ Nginx pods: 5 replicas"
echo "  ✅ Redis pods: 3 replicas"
echo "  ✅ Busybox pods: 4 replicas"
echo "  ✅ Total targets: 12 pods"
echo ""
echo "🎮 How to Play:"
echo "1. Open KubeDoom VNC: http://localhost:8080"
echo "2. Play Doom and shoot the demons (pods)"
echo "3. Watch pods get terminated and recreated"
echo "4. Test your cluster's resilience!"
echo ""
echo "🔍 Monitor pods:"
echo "  docker exec kubedoom-test kubectl get pods -n kubedoom-demo"
