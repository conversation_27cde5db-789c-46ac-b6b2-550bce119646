apiVersion: v1
entries:
  kubedoom-extension:
  - name: kubedoom-extension
    version: 0.1.0
    description: KubeDoom Extension - Play Doom to kill Kubernetes pods
    keywords:
    - rancher
    - extension
    - kubedoom
    - kubernetes
    home: https://github.com/storax/kubedoom
    sources:
    - https://github.com/storax/kubedoom
    maintainers:
    - name: KubeDoom Extension
      email: <EMAIL>
    urls:
    - charts/kubedoom-extension-0.1.0.tgz
    created: "2025-05-26T19:15:00Z"
    digest: "sha256:abcdef1234567890"
    annotations:
      catalog.cattle.io/certified: "rancher"
      catalog.cattle.io/namespace: "cattle-ui-plugin-system"
      catalog.cattle.io/release-name: "kubedoom-extension"
      catalog.cattle.io/display-name: "KubeDoom Extension"
      catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
      catalog.cattle.io/type: "rancher-ui-extension"
      catalog.cattle.io/ui-extensions-version: ">=1.0.0"
      catalog.cattle.io/auto-install: "true"
generated: "2025-05-26T19:15:00Z"
