{"name": "kubedoom-ui-extension", "version": "0.1.0", "description": "KubeDoom UI Extension for Rancher", "main": "index.js", "private": true, "scripts": {"build-pkg": "node scripts/build-pkg.js", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "vue": "^2.6.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "typescript": "~4.5.5", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}