#!/bin/bash

# Fix the extension catalog to work with Rancher 2.11

echo "🔧 Fixing KubeDoom Extension Catalog"
echo "===================================="

# Clean up previous attempts
rm -rf catalog-fixed
mkdir -p catalog-fixed

# Create proper Helm chart structure for Rancher extensions
mkdir -p catalog-fixed/charts/kubedoom-extension/templates

# Create Chart.yaml with proper Rancher extension annotations
cat > catalog-fixed/charts/kubedoom-extension/Chart.yaml << 'EOF'
apiVersion: v2
name: kubedoom-extension
description: KubeDoom Extension - Play Doom to kill Kubernetes pods
type: application
version: 0.1.0
appVersion: "0.1.0"
keywords:
  - rancher
  - extension
  - kubedoom
  - kubernetes
home: https://github.com/storax/kubedoom
sources:
  - https://github.com/storax/kubedoom
maintainers:
  - name: KubeDoom Extension
    email: <EMAIL>
annotations:
  catalog.cattle.io/certified: "rancher"
  catalog.cattle.io/namespace: "cattle-ui-plugin-system"
  catalog.cattle.io/release-name: "kubedoom-extension"
  catalog.cattle.io/display-name: "KubeDoom Extension"
  catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
  catalog.cattle.io/type: "rancher-ui-extension"
  catalog.cattle.io/ui-extensions-version: ">=1.0.0"
  catalog.cattle.io/auto-install: "true"
EOF

# Create values.yaml
cat > catalog-fixed/charts/kubedoom-extension/values.yaml << 'EOF'
# Default values for kubedoom-extension
global:
  cattle:
    systemDefaultRegistry: ""

plugin:
  name: kubedoom-extension
  version: 0.1.0
  endpoint: "http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
  noCache: false

image:
  repository: "extension-server"
  tag: "latest"
  pullPolicy: IfNotPresent
EOF

# Create the UIExtension template
cat > catalog-fixed/charts/kubedoom-extension/templates/uiextension.yaml << 'EOF'
apiVersion: management.cattle.io/v3
kind: UIExtension
metadata:
  name: {{ .Values.plugin.name }}
  namespace: cattle-ui-plugin-system
spec:
  plugin:
    name: {{ .Values.plugin.name }}
    version: {{ .Values.plugin.version }}
    endpoint: {{ .Values.plugin.endpoint }}
    noCache: {{ .Values.plugin.noCache }}
    metadata:
      displayName: "KubeDoom Extension"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
EOF

# Create NOTES.txt
cat > catalog-fixed/charts/kubedoom-extension/templates/NOTES.txt << 'EOF'
KubeDoom Extension has been installed!

To access KubeDoom:
1. Look for "KubeDoom" in the main Rancher navigation menu
2. Select your cluster and namespace
3. Click "Start KubeDoom" to begin playing
4. Use WASD to move, mouse to look around, Ctrl to fire

Have fun killing pods with Doom! 🎮💀
EOF

# Package the chart
cd catalog-fixed/charts
tar -czf kubedoom-extension-0.1.0.tgz kubedoom-extension/
cd ../..

# Create index.yaml for the Helm repository
cat > catalog-fixed/index.yaml << 'EOF'
apiVersion: v1
entries:
  kubedoom-extension:
  - name: kubedoom-extension
    version: 0.1.0
    description: KubeDoom Extension - Play Doom to kill Kubernetes pods
    keywords:
    - rancher
    - extension
    - kubedoom
    - kubernetes
    home: https://github.com/storax/kubedoom
    sources:
    - https://github.com/storax/kubedoom
    maintainers:
    - name: KubeDoom Extension
      email: <EMAIL>
    urls:
    - charts/kubedoom-extension-0.1.0.tgz
    created: "2025-05-26T19:15:00Z"
    digest: "sha256:abcdef1234567890"
    annotations:
      catalog.cattle.io/certified: "rancher"
      catalog.cattle.io/namespace: "cattle-ui-plugin-system"
      catalog.cattle.io/release-name: "kubedoom-extension"
      catalog.cattle.io/display-name: "KubeDoom Extension"
      catalog.cattle.io/description: "Play Doom to kill Kubernetes pods through Rancher UI"
      catalog.cattle.io/type: "rancher-ui-extension"
      catalog.cattle.io/ui-extensions-version: ">=1.0.0"
      catalog.cattle.io/auto-install: "true"
generated: "2025-05-26T19:15:00Z"
EOF

# Copy extension assets
mkdir -p catalog-fixed/assets
cp -r kubedoom-extension/dist-pkg/kubedoom-extension-0.1.0/* catalog-fixed/assets/

# Create Dockerfile for the fixed catalog
cat > Dockerfile.fixed-catalog << 'EOF'
FROM nginx:alpine

# Copy all catalog files
COPY catalog-fixed/ /usr/share/nginx/html/

# Create proper nginx configuration
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/conf.d/default.conf && \
    echo '    server_name localhost;' >> /etc/nginx/conf.d/default.conf && \
    echo '    root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    index index.html index.htm;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        try_files $uri $uri/ =404;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Origin *;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Headers "Content-Type";' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {' >> /etc/nginx/conf.d/default.conf && \
    echo '        expires 1y;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Cache-Control "public, immutable";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Access-Control-Allow-Origin *;' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

EXPOSE 80
EOF

# Build the fixed catalog image
echo "Building fixed extension catalog image..."
docker build -f Dockerfile.fixed-catalog -t kubedoom-catalog-fixed:latest .

echo ""
echo "✅ Fixed extension catalog created successfully!"
echo ""
echo "📦 Fixed Catalog Image: kubedoom-catalog-fixed:latest"
echo ""
echo "🎯 To use in Rancher:"
echo "1. Go to Extensions → Import Extension Catalog"
echo "2. Catalog Image Reference: kubedoom-catalog-fixed:latest"
echo "3. Click Import"
echo ""
echo "📁 Fixed catalog files created in: ./catalog-fixed/"
