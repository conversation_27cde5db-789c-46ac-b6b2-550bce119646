// KubeDoom Extension Styles

// Custom icon for KubeDoom
.icon-kubedoom {
  &:before {
    content: "🎮";
    font-size: 1.2em;
  }
}

// Alternative text-based icon
.icon-kubedoom-alt {
  &:before {
    content: "💀";
    font-size: 1.2em;
  }
}

// KubeDoom specific styling
.kubedoom-extension {
  // Custom color scheme
  --kubedoom-primary: #ff6b6b;
  --kubedoom-secondary: #4ecdc4;
  --kubedoom-danger: #ff4757;
  --kubedoom-warning: #ffa502;
  --kubedoom-success: #2ed573;
  --kubedoom-dark: #2f3542;
  --kubedoom-light: #f1f2f6;

  // Button styles
  .btn-kubedoom {
    background: var(--kubedoom-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: darken(var(--kubedoom-primary), 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  // Card styles
  .kubedoom-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
  }

  // Status indicators
  .status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;

    &.status-healthy {
      background: rgba(46, 213, 115, 0.1);
      color: var(--kubedoom-success);
    }

    &.status-warning {
      background: rgba(255, 165, 2, 0.1);
      color: var(--kubedoom-warning);
    }

    &.status-critical {
      background: rgba(255, 71, 87, 0.1);
      color: var(--kubedoom-danger);
    }

    .icon {
      margin-right: 4px;
    }
  }

  // Loading spinner
  .kubedoom-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--kubedoom-primary);
    border-radius: 50%;
    animation: kubedoom-spin 1s linear infinite;
  }

  @keyframes kubedoom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Game screen styles
  .game-screen {
    background: #000;
    border: 2px solid var(--kubedoom-dark);
    border-radius: 8px;
    position: relative;
    overflow: hidden;

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      border-radius: 0;
    }
  }

  // Pod kill animation
  .pod-kill-animation {
    animation: kubedoom-kill-flash 0.5s ease-in-out;
  }

  @keyframes kubedoom-kill-flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(255, 107, 107, 0.3); }
    100% { background-color: transparent; }
  }

  // Responsive design
  @media (max-width: 768px) {
    .kubedoom-card {
      padding: 15px;
      margin-bottom: 15px;
    }

    .btn-kubedoom {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}

// Dark theme support
.theme-dark {
  .kubedoom-extension {
    .kubedoom-card {
      background: var(--kubedoom-dark);
      color: white;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .kubedoom-extension {
    --kubedoom-primary: #ff0000;
    --kubedoom-secondary: #00ffff;
    
    .kubedoom-card {
      border: 2px solid var(--kubedoom-primary);
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .kubedoom-extension {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
