import { Store } from 'vuex';

export interface KubeDoomConfig {
  clusterId: string;
  namespace?: string;
  vncPassword?: string;
}

export interface KubeDoomStatus {
  deployed: boolean;
  ready: boolean;
  vncUrl?: string;
  error?: string;
}

export class KubeDoomManager {
  private store: Store<any>;

  constructor(store: Store<any>) {
    this.store = store;
  }

  /**
   * Deploy KubeDoom to the specified cluster
   */
  async deployKubeDoom(config: KubeDoomConfig): Promise<void> {
    const { clusterId, namespace } = config;

    try {
      // Create the kubedoom namespace
      await this.createNamespace(clusterId);

      // Create service account and RBAC
      await this.createServiceAccount(clusterId);
      await this.createClusterRole(clusterId);
      await this.createClusterRoleBinding(clusterId);

      // Deploy the main KubeDoom application
      await this.createDeployment(clusterId, namespace);

      // Create the VNC service
      await this.createVNCService(clusterId);

      // Wait for deployment to be ready
      await this.waitForDeployment(clusterId);

    } catch (error) {
      console.error('Failed to deploy KubeDoom:', error);
      throw new Error(`Failed to deploy KubeDoom: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Remove KubeDoom from the cluster
   */
  async removeKubeDoom(clusterId: string): Promise<void> {
    try {
      // Delete the deployment
      await this.deleteResource(clusterId, 'apps/v1', 'Deployment', 'kubedoom', 'kubedoom');

      // Delete the service
      await this.deleteResource(clusterId, 'v1', 'Service', 'kubedoom-vnc', 'kubedoom');

      // Delete RBAC resources
      await this.deleteResource(clusterId, 'rbac.authorization.k8s.io/v1', 'ClusterRoleBinding', 'kubedoom');
      await this.deleteResource(clusterId, 'rbac.authorization.k8s.io/v1', 'ClusterRole', 'kubedoom');

      // Delete service account
      await this.deleteResource(clusterId, 'v1', 'ServiceAccount', 'kubedoom', 'kubedoom');

      // Delete namespace (this will clean up everything else)
      await this.deleteResource(clusterId, 'v1', 'Namespace', 'kubedoom');

    } catch (error) {
      console.error('Failed to remove KubeDoom:', error);
      throw new Error(`Failed to remove KubeDoom: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get the status of KubeDoom deployment
   */
  async getKubeDoomStatus(clusterId: string): Promise<KubeDoomStatus> {
    try {
      // Check if namespace exists
      const namespace = await this.getResource(clusterId, 'v1', 'Namespace', 'kubedoom');
      if (!namespace) {
        return { deployed: false, ready: false };
      }

      // Check deployment status
      const deployment = await this.getResource(clusterId, 'apps/v1', 'Deployment', 'kubedoom', 'kubedoom');
      if (!deployment) {
        return { deployed: false, ready: false };
      }

      const ready = deployment.status?.readyReplicas === deployment.status?.replicas;
      const vncUrl = ready ? await this.getVNCUrl(clusterId) : undefined;

      return {
        deployed: true,
        ready,
        vncUrl,
      };

    } catch (error) {
      return {
        deployed: false,
        ready: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Get the VNC connection URL
   */
  async getVNCUrl(clusterId: string): Promise<string> {
    try {
      // In a real implementation, this would set up port forwarding or ingress
      // For now, we'll return a placeholder URL
      return `ws://kubedoom-vnc.kubedoom.svc.cluster.local:5900`;
    } catch (error) {
      throw new Error(`Failed to get VNC URL: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create the kubedoom namespace
   */
  private async createNamespace(clusterId: string): Promise<void> {
    const namespace = {
      apiVersion: 'v1',
      kind: 'Namespace',
      metadata: {
        name: 'kubedoom',
        labels: {
          name: 'kubedoom',
          'app.kubernetes.io/name': 'kubedoom',
          'app.kubernetes.io/managed-by': 'rancher-kubedoom-extension',
        },
      },
    };

    await this.createResource(clusterId, namespace);
  }

  /**
   * Create the service account
   */
  private async createServiceAccount(clusterId: string): Promise<void> {
    const serviceAccount = {
      apiVersion: 'v1',
      kind: 'ServiceAccount',
      metadata: {
        name: 'kubedoom',
        namespace: 'kubedoom',
      },
    };

    await this.createResource(clusterId, serviceAccount);
  }

  /**
   * Create the cluster role
   */
  private async createClusterRole(clusterId: string): Promise<void> {
    const clusterRole = {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'kubedoom',
      },
      rules: [
        {
          apiGroups: [''],
          resources: ['pods'],
          verbs: ['list', 'delete'],
        },
        {
          apiGroups: [''],
          resources: ['events'],
          verbs: ['create'],
        },
      ],
    };

    await this.createResource(clusterId, clusterRole);
  }

  /**
   * Create the cluster role binding
   */
  private async createClusterRoleBinding(clusterId: string): Promise<void> {
    const clusterRoleBinding = {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'kubedoom',
      },
      roleRef: {
        apiGroup: 'rbac.authorization.k8s.io',
        kind: 'ClusterRole',
        name: 'kubedoom',
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: 'kubedoom',
          namespace: 'kubedoom',
        },
      ],
    };

    await this.createResource(clusterId, clusterRoleBinding);
  }

  /**
   * Create the KubeDoom deployment
   */
  private async createDeployment(clusterId: string, targetNamespace?: string): Promise<void> {
    const deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'kubedoom',
        namespace: 'kubedoom',
        labels: {
          app: 'kubedoom',
        },
      },
      spec: {
        replicas: 1,
        selector: {
          matchLabels: {
            app: 'kubedoom',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'kubedoom',
            },
          },
          spec: {
            serviceAccountName: 'kubedoom',
            containers: [
              {
                name: 'kubedoom',
                image: 'ghcr.io/storax/kubedoom:latest',
                ports: [
                  {
                    containerPort: 5900,
                    name: 'vnc',
                  },
                ],
                env: [
                  {
                    name: 'NAMESPACE',
                    value: targetNamespace || '',
                  },
                ],
                resources: {
                  requests: {
                    memory: '256Mi',
                    cpu: '100m',
                  },
                  limits: {
                    memory: '512Mi',
                    cpu: '500m',
                  },
                },
                securityContext: {
                  runAsNonRoot: true,
                  runAsUser: 1000,
                  allowPrivilegeEscalation: false,
                  capabilities: {
                    drop: ['ALL'],
                  },
                },
              },
            ],
            securityContext: {
              fsGroup: 1000,
            },
          },
        },
      },
    };

    await this.createResource(clusterId, deployment);
  }

  /**
   * Create the VNC service
   */
  private async createVNCService(clusterId: string): Promise<void> {
    const service = {
      apiVersion: 'v1',
      kind: 'Service',
      metadata: {
        name: 'kubedoom-vnc',
        namespace: 'kubedoom',
        labels: {
          app: 'kubedoom',
        },
      },
      spec: {
        type: 'ClusterIP',
        ports: [
          {
            port: 5900,
            targetPort: 5900,
            protocol: 'TCP',
            name: 'vnc',
          },
        ],
        selector: {
          app: 'kubedoom',
        },
      },
    };

    await this.createResource(clusterId, service);
  }

  /**
   * Wait for deployment to be ready
   */
  private async waitForDeployment(clusterId: string, timeoutMs: number = 300000): Promise<void> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      const deployment = await this.getResource(clusterId, 'apps/v1', 'Deployment', 'kubedoom', 'kubedoom');

      if (deployment?.status?.readyReplicas === deployment?.status?.replicas) {
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    throw new Error('Timeout waiting for KubeDoom deployment to be ready');
  }

  /**
   * Generic method to create a Kubernetes resource
   */
  private async createResource(clusterId: string, resource: any): Promise<void> {
    try {
      await this.store.dispatch('cluster/create', {
        clusterId,
        resource,
      });
    } catch (error: any) {
      if (error.status === 409) {
        // Resource already exists, that's okay
        return;
      }
      throw error;
    }
  }

  /**
   * Generic method to get a Kubernetes resource
   */
  private async getResource(clusterId: string, apiVersion: string, kind: string, name: string, namespace?: string): Promise<any> {
    try {
      const type = `${apiVersion}/${kind}`.toLowerCase();
      return await this.store.dispatch('cluster/find', {
        clusterId,
        type,
        id: namespace ? `${namespace}/${name}` : name,
      });
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Generic method to delete a Kubernetes resource
   */
  private async deleteResource(clusterId: string, apiVersion: string, kind: string, name: string, namespace?: string): Promise<void> {
    try {
      const resource = await this.getResource(clusterId, apiVersion, kind, name, namespace);
      if (resource) {
        await this.store.dispatch('cluster/remove', {
          clusterId,
          resource,
        });
      }
    } catch (error: any) {
      if (error.status === 404) {
        // Resource doesn't exist, that's okay
        return;
      }
      throw error;
    }
  }
}
