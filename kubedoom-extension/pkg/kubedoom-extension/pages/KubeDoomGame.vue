<template>
  <div class="kubedoom-game">
    <div class="game-header">
      <div class="header-left">
        <button class="btn btn-secondary" @click="goBack">
          <i class="icon icon-arrow-left"></i>
          Back to Dashboard
        </button>
      </div>
      
      <div class="header-center">
        <h2>KubeDoom - {{ clusterName }}</h2>
        <div class="status" :class="connectionStatus">
          <i class="icon" :class="statusIcon"></i>
          {{ statusText }}
        </div>
      </div>
      
      <div class="header-right">
        <button class="btn btn-danger" @click="emergencyStop">
          <i class="icon icon-stop"></i>
          Emergency Stop
        </button>
      </div>
    </div>

    <div class="game-content">
      <div class="game-area">
        <div class="vnc-container" ref="vncContainer">
          <div v-if="!connected" class="connection-overlay">
            <div class="spinner"></div>
            <p>{{ connectionMessage }}</p>
          </div>
          <canvas ref="vncCanvas" v-show="connected"></canvas>
        </div>
        
        <div class="game-controls">
          <div class="control-group">
            <h4>Game Controls</h4>
            <div class="controls-grid">
              <div class="control-item">
                <span class="key">WASD</span>
                <span class="desc">Move</span>
              </div>
              <div class="control-item">
                <span class="key">Mouse</span>
                <span class="desc">Look around</span>
              </div>
              <div class="control-item">
                <span class="key">Ctrl</span>
                <span class="desc">Fire</span>
              </div>
              <div class="control-item">
                <span class="key">Space</span>
                <span class="desc">Open doors</span>
              </div>
              <div class="control-item">
                <span class="key">ESC</span>
                <span class="desc">Pause</span>
              </div>
            </div>
          </div>

          <div class="control-group">
            <h4>Cheats</h4>
            <div class="cheat-buttons">
              <button class="btn btn-sm" @click="sendCheat('idkfa')">
                All Weapons
              </button>
              <button class="btn btn-sm" @click="sendCheat('iddqd')">
                God Mode
              </button>
              <button class="btn btn-sm" @click="sendCheat('idspispopd')">
                No Clip
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="pod-monitor">
        <h3>Pod Monitor</h3>
        <div class="monitor-stats">
          <div class="stat">
            <span class="label">Pods Killed:</span>
            <span class="value killed">{{ podsKilled }}</span>
          </div>
          <div class="stat">
            <span class="label">Pods Remaining:</span>
            <span class="value remaining">{{ podsRemaining }}</span>
          </div>
          <div class="stat">
            <span class="label">Pods Respawned:</span>
            <span class="value respawned">{{ podsRespawned }}</span>
          </div>
        </div>

        <div class="recent-kills" v-if="recentKills.length > 0">
          <h4>Recent Pod Kills</h4>
          <div class="kill-list">
            <div 
              v-for="kill in recentKills" 
              :key="kill.id"
              class="kill-item"
            >
              <span class="pod-name">{{ kill.podName }}</span>
              <span class="namespace">{{ kill.namespace }}</span>
              <span class="time">{{ formatTime(kill.timestamp) }}</span>
            </div>
          </div>
        </div>

        <div class="cluster-health">
          <h4>Cluster Health</h4>
          <div class="health-indicator" :class="clusterHealth.status">
            <i class="icon" :class="clusterHealth.icon"></i>
            {{ clusterHealth.message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomGame',
  
  data() {
    return {
      connected: false,
      connectionStatus: 'connecting',
      connectionMessage: 'Connecting to KubeDoom...',
      vncClient: null,
      clusterName: '',
      podsKilled: 0,
      podsRemaining: 0,
      podsRespawned: 0,
      recentKills: [],
      clusterHealth: {
        status: 'healthy',
        icon: 'icon-checkmark',
        message: 'Cluster is healthy'
      },
      monitoringInterval: null,
    };
  },

  computed: {
    statusIcon() {
      switch (this.connectionStatus) {
        case 'connected': return 'icon-checkmark';
        case 'connecting': return 'icon-spinner';
        case 'error': return 'icon-error';
        default: return 'icon-info';
      }
    },

    statusText() {
      switch (this.connectionStatus) {
        case 'connected': return 'Connected to KubeDoom';
        case 'connecting': return 'Connecting...';
        case 'error': return 'Connection failed';
        default: return 'Unknown status';
      }
    }
  },

  async mounted() {
    this.clusterName = this.$route.params.cluster || 'Unknown';
    await this.initializeVNC();
    this.startMonitoring();
  },

  beforeUnmount() {
    this.cleanup();
  },

  methods: {
    async initializeVNC() {
      try {
        this.connectionMessage = 'Initializing VNC connection...';
        
        // In a real implementation, this would connect to the KubeDoom VNC server
        // For now, we'll simulate the connection
        await this.simulateVNCConnection();
        
      } catch (error) {
        console.error('Failed to initialize VNC:', error);
        this.connectionStatus = 'error';
        this.connectionMessage = 'Failed to connect to KubeDoom';
      }
    },

    async simulateVNCConnection() {
      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.connected = true;
      this.connectionStatus = 'connected';
      
      // Initialize canvas for game display
      this.initializeCanvas();
    },

    initializeCanvas() {
      const canvas = this.$refs.vncCanvas;
      if (!canvas) return;

      canvas.width = 800;
      canvas.height = 600;
      
      const ctx = canvas.getContext('2d');
      
      // Draw a placeholder game screen
      ctx.fillStyle = '#000';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      ctx.fillStyle = '#ff0000';
      ctx.font = '24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);
      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);
      
      // Add click handler for interaction
      canvas.addEventListener('click', this.handleCanvasClick);
    },

    handleCanvasClick() {
      // Simulate pod kill when clicking on the game
      this.simulatePodKill();
    },

    simulatePodKill() {
      const podNames = [
        'nginx-deployment-abc123',
        'redis-master-def456',
        'postgres-ghi789',
        'api-server-jkl012',
        'worker-mno345'
      ];
      
      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];
      
      const kill = {
        id: Date.now(),
        podName: podNames[Math.floor(Math.random() * podNames.length)],
        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],
        timestamp: new Date()
      };
      
      this.recentKills.unshift(kill);
      if (this.recentKills.length > 10) {
        this.recentKills.pop();
      }
      
      this.podsKilled++;
      this.podsRemaining = Math.max(0, this.podsRemaining - 1);
      
      // Update cluster health based on kills
      this.updateClusterHealth();
    },

    updateClusterHealth() {
      if (this.podsKilled > 20) {
        this.clusterHealth = {
          status: 'critical',
          icon: 'icon-error',
          message: 'Cluster under heavy stress!'
        };
      } else if (this.podsKilled > 10) {
        this.clusterHealth = {
          status: 'warning',
          icon: 'icon-warning',
          message: 'Cluster experiencing some stress'
        };
      } else {
        this.clusterHealth = {
          status: 'healthy',
          icon: 'icon-checkmark',
          message: 'Cluster is healthy'
        };
      }
    },

    sendCheat(cheatCode) {
      // In a real implementation, this would send the cheat to the VNC session
      console.log('Sending cheat:', cheatCode);
      
      this.$store.dispatch('growl/info', {
        title: 'Cheat Activated',
        message: `Cheat code "${cheatCode}" sent to game`
      });
    },

    startMonitoring() {
      this.podsRemaining = 50; // Initial pod count
      
      this.monitoringInterval = setInterval(() => {
        // Simulate pod respawning
        if (Math.random() < 0.1 && this.podsRemaining < 50) {
          this.podsRemaining++;
          this.podsRespawned++;
        }
      }, 5000);
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString();
    },

    goBack() {
      this.$router.push({
        name: 'kubedoom-c-cluster-dashboard',
        params: { cluster: this.$route.params.cluster }
      });
    },

    async emergencyStop() {
      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {
        await this.cleanup();
        this.goBack();
      }
    },

    cleanup() {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }
      
      if (this.vncClient) {
        // Disconnect VNC client
        this.vncClient = null;
      }
      
      const canvas = this.$refs.vncCanvas;
      if (canvas) {
        canvas.removeEventListener('click', this.handleCanvasClick);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.kubedoom-game {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  color: white;

  .game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #2d2d2d;
    border-bottom: 1px solid #444;

    .header-center {
      text-align: center;

      h2 {
        margin: 0 0 5px 0;
        color: #ff6b6b;
      }

      .status {
        font-size: 14px;
        
        &.connected { color: #28a745; }
        &.connecting { color: #ffc107; }
        &.error { color: #dc3545; }

        .icon {
          margin-right: 5px;
        }
      }
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;

      &.btn-secondary {
        background: #6c757d;
        color: white;
      }

      &.btn-danger {
        background: #dc3545;
        color: white;
      }

      .icon {
        margin-right: 5px;
      }
    }
  }

  .game-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    padding: 20px;

    .game-area {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .vnc-container {
        position: relative;
        background: #000;
        border: 2px solid #444;
        border-radius: 8px;
        overflow: hidden;
        height: 600px;

        .connection-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: rgba(0, 0, 0, 0.8);
          z-index: 10;

          .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
          }

          p {
            color: #ccc;
            font-size: 16px;
          }
        }

        canvas {
          width: 100%;
          height: 100%;
          cursor: crosshair;
        }
      }

      .game-controls {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .control-group {
          background: #2d2d2d;
          padding: 15px;
          border-radius: 8px;

          h4 {
            margin: 0 0 15px 0;
            color: #ff6b6b;
          }

          .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;

            .control-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px 0;

              .key {
                background: #444;
                padding: 2px 8px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
              }

              .desc {
                font-size: 12px;
                color: #ccc;
              }
            }
          }

          .cheat-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .btn {
              padding: 6px 12px;
              background: #444;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-size: 12px;

              &:hover {
                background: #555;
              }
            }
          }
        }
      }
    }

    .pod-monitor {
      background: #2d2d2d;
      padding: 20px;
      border-radius: 8px;
      height: fit-content;

      h3 {
        margin: 0 0 20px 0;
        color: #ff6b6b;
      }

      .monitor-stats {
        margin-bottom: 25px;

        .stat {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .label {
            color: #ccc;
          }

          .value {
            font-weight: 600;

            &.killed { color: #dc3545; }
            &.remaining { color: #28a745; }
            &.respawned { color: #17a2b8; }
          }
        }
      }

      .recent-kills {
        margin-bottom: 25px;

        h4 {
          margin: 0 0 15px 0;
          color: #ffc107;
        }

        .kill-list {
          max-height: 200px;
          overflow-y: auto;

          .kill-item {
            display: flex;
            flex-direction: column;
            padding: 8px;
            margin-bottom: 8px;
            background: #1a1a1a;
            border-radius: 4px;
            font-size: 12px;

            .pod-name {
              font-weight: 600;
              color: #ff6b6b;
            }

            .namespace {
              color: #17a2b8;
            }

            .time {
              color: #6c757d;
            }
          }
        }
      }

      .cluster-health {
        h4 {
          margin: 0 0 15px 0;
          color: #28a745;
        }

        .health-indicator {
          padding: 10px;
          border-radius: 4px;
          text-align: center;
          font-weight: 500;

          &.healthy {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
          }

          &.warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
          }

          &.critical {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
          }

          .icon {
            margin-right: 5px;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
