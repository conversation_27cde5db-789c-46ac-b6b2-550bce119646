<template>
  <div class="kubedoom-dashboard">
    <div class="header">
      <h1 class="title">
        <i class="icon icon-kubedoom"></i>
        KubeDoom
      </h1>
      <p class="subtitle">
        Kill Kubernetes pods by playing Doom!
      </p>
    </div>

    <div class="content">
      <div class="info-section">
        <div class="info-card">
          <h3>What is KubeDoom?</h3>
          <p>
            KubeDoom is a fun way to test the resilience of your Kubernetes cluster by playing the classic game Doom. 
            Each enemy in the game represents a pod in your cluster, and killing them will actually delete the corresponding pod.
          </p>
        </div>

        <div class="info-card">
          <h3>How it works</h3>
          <ul>
            <li>Each pod in your cluster appears as an enemy in Doom</li>
            <li>Shooting enemies kills the corresponding pods</li>
            <li>Watch how your applications handle pod failures</li>
            <li>Test your cluster's resilience and recovery mechanisms</li>
          </ul>
        </div>

        <div class="info-card">
          <h3>Safety Notice</h3>
          <div class="warning">
            <i class="icon icon-warning"></i>
            <strong>Warning:</strong> This will actually delete pods in your cluster. 
            Only use this in development or testing environments!
          </div>
        </div>
      </div>

      <div class="actions">
        <div class="cluster-selector">
          <label>Select Cluster:</label>
          <select v-model="selectedCluster" @change="onClusterChange">
            <option value="">Choose a cluster...</option>
            <option v-for="cluster in clusters" :key="cluster.id" :value="cluster.id">
              {{ cluster.nameDisplay }}
            </option>
          </select>
        </div>

        <div class="namespace-selector" v-if="selectedCluster">
          <label>Target Namespace (optional):</label>
          <select v-model="selectedNamespace">
            <option value="">All namespaces</option>
            <option v-for="namespace in namespaces" :key="namespace" :value="namespace">
              {{ namespace }}
            </option>
          </select>
        </div>

        <div class="pod-info" v-if="selectedCluster">
          <h4>Cluster Status</h4>
          <div class="stats">
            <div class="stat">
              <span class="label">Total Pods:</span>
              <span class="value">{{ podCount }}</span>
            </div>
            <div class="stat">
              <span class="label">Running Pods:</span>
              <span class="value">{{ runningPods }}</span>
            </div>
            <div class="stat">
              <span class="label">Target Namespace:</span>
              <span class="value">{{ selectedNamespace || 'All' }}</span>
            </div>
          </div>
        </div>

        <div class="game-controls">
          <button 
            class="btn btn-primary btn-lg"
            :disabled="!selectedCluster || isDeploying"
            @click="startKubeDoom"
          >
            <i class="icon icon-play"></i>
            {{ isDeploying ? 'Deploying KubeDoom...' : 'Start KubeDoom' }}
          </button>
          
          <button 
            class="btn btn-secondary"
            :disabled="!kubeDoomDeployed"
            @click="stopKubeDoom"
          >
            <i class="icon icon-stop"></i>
            Stop KubeDoom
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KubeDoomDashboard',
  
  data() {
    return {
      selectedCluster: '',
      selectedNamespace: '',
      clusters: [],
      namespaces: [],
      podCount: 0,
      runningPods: 0,
      isDeploying: false,
      kubeDoomDeployed: false,
    };
  },

  async mounted() {
    await this.loadClusters();
  },

  methods: {
    async loadClusters() {
      try {
        // Load clusters from Rancher API
        const clusters = await this.$store.dispatch('management/findAll', { type: 'cluster' });
        this.clusters = clusters.filter(cluster => cluster.isReady);
      } catch (error) {
        console.error('Failed to load clusters:', error);
        this.$store.dispatch('growl/error', {
          title: 'Error',
          message: 'Failed to load clusters'
        });
      }
    },

    async onClusterChange() {
      if (!this.selectedCluster) {
        this.namespaces = [];
        this.podCount = 0;
        this.runningPods = 0;
        return;
      }

      try {
        await this.loadNamespaces();
        await this.loadPodStats();
      } catch (error) {
        console.error('Failed to load cluster data:', error);
      }
    },

    async loadNamespaces() {
      try {
        // Load namespaces for the selected cluster
        const namespaces = await this.$store.dispatch('cluster/findAll', { 
          type: 'namespace',
          clusterId: this.selectedCluster 
        });
        this.namespaces = namespaces.map(ns => ns.metadata.name);
      } catch (error) {
        console.error('Failed to load namespaces:', error);
      }
    },

    async loadPodStats() {
      try {
        // Load pod statistics for the selected cluster
        const pods = await this.$store.dispatch('cluster/findAll', { 
          type: 'pod',
          clusterId: this.selectedCluster 
        });
        
        this.podCount = pods.length;
        this.runningPods = pods.filter(pod => pod.status?.phase === 'Running').length;
      } catch (error) {
        console.error('Failed to load pod stats:', error);
      }
    },

    async startKubeDoom() {
      this.isDeploying = true;
      
      try {
        // Deploy KubeDoom to the selected cluster
        await this.deployKubeDoom();
        
        this.$store.dispatch('growl/success', {
          title: 'Success',
          message: 'KubeDoom deployed successfully!'
        });
        
        // Navigate to the game page
        this.$router.push({
          name: 'kubedoom-c-cluster-game',
          params: { cluster: this.selectedCluster }
        });
        
      } catch (error) {
        console.error('Failed to deploy KubeDoom:', error);
        this.$store.dispatch('growl/error', {
          title: 'Error',
          message: 'Failed to deploy KubeDoom: ' + error.message
        });
      } finally {
        this.isDeploying = false;
      }
    },

    async deployKubeDoom() {
      // This would deploy the KubeDoom pod to the cluster
      // For now, we'll simulate the deployment
      await new Promise(resolve => setTimeout(resolve, 2000));
      this.kubeDoomDeployed = true;
    },

    async stopKubeDoom() {
      try {
        // Stop and remove KubeDoom deployment
        await this.removeKubeDoom();
        
        this.kubeDoomDeployed = false;
        
        this.$store.dispatch('growl/success', {
          title: 'Success',
          message: 'KubeDoom stopped successfully!'
        });
        
      } catch (error) {
        console.error('Failed to stop KubeDoom:', error);
        this.$store.dispatch('growl/error', {
          title: 'Error',
          message: 'Failed to stop KubeDoom: ' + error.message
        });
      }
    },

    async removeKubeDoom() {
      // This would remove the KubeDoom deployment
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
};
</script>

<style lang="scss" scoped>
.kubedoom-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    text-align: center;
    margin-bottom: 40px;

    .title {
      font-size: 3rem;
      color: #ff6b6b;
      margin-bottom: 10px;
      
      .icon {
        margin-right: 15px;
      }
    }

    .subtitle {
      font-size: 1.2rem;
      color: #666;
    }
  }

  .content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .info-section {
    .info-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      h3 {
        color: #333;
        margin-bottom: 15px;
      }

      ul {
        margin: 0;
        padding-left: 20px;
      }

      .warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        color: #856404;

        .icon {
          margin-right: 8px;
          color: #f39c12;
        }
      }
    }
  }

  .actions {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    height: fit-content;

    .cluster-selector,
    .namespace-selector {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
      }

      select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }
    }

    .pod-info {
      margin-bottom: 30px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 4px;

      h4 {
        margin-bottom: 15px;
        color: #333;
      }

      .stats {
        .stat {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .label {
            font-weight: 500;
          }

          .value {
            font-weight: 600;
            color: #007bff;
          }
        }
      }
    }

    .game-controls {
      .btn {
        width: 100%;
        margin-bottom: 10px;
        padding: 12px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s;

        .icon {
          margin-right: 8px;
        }

        &.btn-primary {
          background: #ff6b6b;
          color: white;

          &:hover:not(:disabled) {
            background: #ff5252;
          }

          &:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        }

        &.btn-secondary {
          background: #6c757d;
          color: white;

          &:hover:not(:disabled) {
            background: #5a6268;
          }

          &:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>
