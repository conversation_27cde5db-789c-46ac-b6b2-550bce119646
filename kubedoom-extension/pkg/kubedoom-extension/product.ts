import { IPlugin } from '@shell/core/types';

export function init($plugin: IPlugin, store: any) {
  const KUBEDOOM_PRODUCT_NAME = 'kubedoom';
  const BLANK_CLUSTER = '_';
  
  const { product } = $plugin.DSL(store, KUBEDOOM_PRODUCT_NAME);

  product({
    icon: 'icon-kubedoom',
    inStore: 'management',
    weight: 100,
    to: {
      name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-dashboard`,
      path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/dashboard`,
      params: {
        product: KUBEDOOM_PRODUCT_NAME,
        cluster: BLANK_CLUSTER,
        pkg: KUBEDOOM_PRODUCT_NAME,
      },
    },
  });
}
