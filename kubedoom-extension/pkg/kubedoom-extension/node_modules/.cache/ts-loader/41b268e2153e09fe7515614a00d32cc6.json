{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js??clonedRuleSet-42.use[3]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/product.ts", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/product.ts", "mtime": 1748284824209}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/babel.config.js", "mtime": 1748284699552}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1747923095351}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js", "mtime": 1747923099208}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": 1747923102829}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js", "mtime": 1747923103115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGZ1bmN0aW9uIGluaXQoJHBsdWdpbiwgc3RvcmUpIHsKICBjb25zdCBLVUJFRE9PTV9QUk9EVUNUX05BTUUgPSAna3ViZWRvb20nOwogIGNvbnN0IEJMQU5LX0NMVVNURVIgPSAnXyc7CiAgY29uc3QgewogICAgcHJvZHVjdAogIH0gPSAkcGx1Z2luLkRTTChzdG9yZSwgS1VCRURPT01fUFJPRFVDVF9OQU1FKTsKICBwcm9kdWN0KHsKICAgIGljb246ICdpY29uLWt1YmVkb29tJywKICAgIGluU3RvcmU6ICdtYW5hZ2VtZW50JywKICAgIHdlaWdodDogMTAwLAogICAgdG86IHsKICAgICAgbmFtZTogYCR7S1VCRURPT01fUFJPRFVDVF9OQU1FfS1jLWNsdXN0ZXItZGFzaGJvYXJkYCwKICAgICAgcGF0aDogYC8ke0tVQkVET09NX1BST0RVQ1RfTkFNRX0vYy86Y2x1c3Rlci9kYXNoYm9hcmRgLAogICAgICBwYXJhbXM6IHsKICAgICAgICBwcm9kdWN0OiBLVUJFRE9PTV9QUk9EVUNUX05BTUUsCiAgICAgICAgY2x1c3RlcjogQkxBTktfQ0xVU1RFUiwKICAgICAgICBwa2c6IEtVQkVET09NX1BST0RVQ1RfTkFNRQogICAgICB9CiAgICB9CiAgfSk7Cn0="}, {"version": 3, "names": ["init", "$plugin", "store", "KUBEDOOM_PRODUCT_NAME", "BLANK_CLUSTER", "product", "DSL", "icon", "inStore", "weight", "to", "name", "path", "params", "cluster", "pkg"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/product.ts"], "sourcesContent": ["import { IPlugin } from '@shell/core/types';\n\nexport function init($plugin: IPlugin, store: any) {\n  const KUBEDOOM_PRODUCT_NAME = 'kubedoom';\n  const BLANK_CLUSTER = '_';\n  \n  const { product } = $plugin.DSL(store, KUBEDOOM_PRODUCT_NAME);\n\n  product({\n    icon: 'icon-kubedoom',\n    inStore: 'management',\n    weight: 100,\n    to: {\n      name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-dashboard`,\n      path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/dashboard`,\n      params: {\n        product: KUBEDOOM_PRODUCT_NAME,\n        cluster: BLANK_CLUSTER,\n        pkg: KUBEDOOM_PRODUCT_NAME,\n      },\n    },\n  });\n}\n"], "mappings": "AAEA,OAAM,SAAUA,IAAIA,CAACC,OAAgB,EAAEC,KAAU;EAC/C,MAAMC,qBAAqB,GAAG,UAAU;EACxC,MAAMC,aAAa,GAAG,GAAG;EAEzB,MAAM;IAAEC;EAAO,CAAE,GAAGJ,OAAO,CAACK,GAAG,CAACJ,KAAK,EAAEC,qBAAqB,CAAC;EAE7DE,OAAO,CAAC;IACNE,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,GAAG;IACXC,EAAE,EAAE;MACFC,IAAI,EAAE,GAAGR,qBAAqB,sBAAsB;MACpDS,IAAI,EAAE,IAAIT,qBAAqB,uBAAuB;MACtDU,MAAM,EAAE;QACNR,OAAO,EAAEF,qBAAqB;QAC9BW,OAAO,EAAEV,aAAa;QACtBW,GAAG,EAAEZ;;;GAGV,CAAC;AACJ", "ignoreList": []}]}