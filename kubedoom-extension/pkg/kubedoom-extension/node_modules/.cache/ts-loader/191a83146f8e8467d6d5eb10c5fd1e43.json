{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js??clonedRuleSet-42.use[3]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/routing/extension-routing.ts", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/routing/extension-routing.ts", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/babel.config.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IERhc2hib2FyZCBmcm9tICcuLi9wYWdlcy9EYXNoYm9hcmQudnVlJzsKaW1wb3J0IEt1YmVEb29tR2FtZSBmcm9tICcuLi9wYWdlcy9LdWJlRG9vbUdhbWUudnVlJzsKY29uc3QgQkxBTktfQ0xVU1RFUiA9ICdfJzsKY29uc3QgS1VCRURPT01fUFJPRFVDVF9OQU1FID0gJ2t1YmVkb29tJzsKY29uc3Qgcm91dGVzID0gW3sKICBuYW1lOiBgJHtLVUJFRE9PTV9QUk9EVUNUX05BTUV9LWMtY2x1c3Rlci1kYXNoYm9hcmRgLAogIHBhdGg6IGAvJHtLVUJFRE9PTV9QUk9EVUNUX05BTUV9L2MvOmNsdXN0ZXIvZGFzaGJvYXJkYCwKICBjb21wb25lbnQ6IERhc2hib2FyZCwKICBtZXRhOiB7CiAgICBwcm9kdWN0OiBLVUJFRE9PTV9QUk9EVUNUX05BTUUsCiAgICBjbHVzdGVyOiBCTEFOS19DTFVTVEVSLAogICAgcGtnOiBLVUJFRE9PTV9QUk9EVUNUX05BTUUKICB9Cn0sIHsKICBuYW1lOiBgJHtLVUJFRE9PTV9QUk9EVUNUX05BTUV9LWMtY2x1c3Rlci1nYW1lYCwKICBwYXRoOiBgLyR7S1VCRURPT01fUFJPRFVDVF9OQU1FfS9jLzpjbHVzdGVyL2dhbWVgLAogIGNvbXBvbmVudDogS3ViZURvb21HYW1lLAogIG1ldGE6IHsKICAgIHByb2R1Y3Q6IEtVQkVET09NX1BST0RVQ1RfTkFNRSwKICAgIGNsdXN0ZXI6IEJMQU5LX0NMVVNURVIsCiAgICBwa2c6IEtVQkVET09NX1BST0RVQ1RfTkFNRQogIH0KfV07CmV4cG9ydCBkZWZhdWx0IHJvdXRlczs="}, {"version": 3, "names": ["Dashboard", "KubeDoomGame", "BLANK_CLUSTER", "KUBEDOOM_PRODUCT_NAME", "routes", "name", "path", "component", "meta", "product", "cluster", "pkg"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/routing/extension-routing.ts"], "sourcesContent": ["import Dashboard from '../pages/Dashboard.vue';\nimport KubeDoomGame from '../pages/KubeDoomGame.vue';\n\nconst BLANK_CLUSTER = '_';\nconst KUBEDOOM_PRODUCT_NAME = 'kubedoom';\n\nconst routes = [\n  {\n    name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-dashboard`,\n    path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/dashboard`,\n    component: Dashboard,\n    meta: {\n      product: KUBEDOOM_PRODUCT_NAME,\n      cluster: BLANK_CLUSTER,\n      pkg: KUBEDOOM_PRODUCT_NAME,\n    },\n  },\n  {\n    name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-game`,\n    path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/game`,\n    component: KubeDoomGame,\n    meta: {\n      product: KUBEDOOM_PRODUCT_NAME,\n      cluster: BLANK_CLUSTER,\n      pkg: KU<PERSON>D<PERSON><PERSON>_PRODUCT_NAME,\n    },\n  },\n];\n\nexport default routes;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AAEpD,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,qBAAqB,GAAG,UAAU;AAExC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAGF,qBAAqB,sBAAsB;EACpDG,IAAI,EAAE,IAAIH,qBAAqB,uBAAuB;EACtDI,SAAS,EAAEP,SAAS;EACpBQ,IAAI,EAAE;IACJC,OAAO,EAAEN,qBAAqB;IAC9BO,OAAO,EAAER,aAAa;IACtBS,GAAG,EAAER;;CAER,EACD;EACEE,IAAI,EAAE,GAAGF,qBAAqB,iBAAiB;EAC/CG,IAAI,EAAE,IAAIH,qBAAqB,kBAAkB;EACjDI,SAAS,EAAEN,YAAY;EACvBO,IAAI,EAAE;IACJC,OAAO,EAAEN,qBAAqB;IAC9BO,OAAO,EAAER,aAAa;IACtBS,GAAG,EAAER;;CAER,CACF;AAED,eAAeC,MAAM", "ignoreList": []}]}