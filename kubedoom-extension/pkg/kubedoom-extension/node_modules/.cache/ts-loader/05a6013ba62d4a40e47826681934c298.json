{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js??clonedRuleSet-42.use[3]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/index.ts", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/index.ts", "mtime": 1748284957487}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/babel.config.js", "mtime": 1748284699552}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1747923095351}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/thread-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": *************}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/ts-loader/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgaW1wb3J0VHlwZXMgfSBmcm9tICdAcmFuY2hlci9hdXRvLWltcG9ydCc7CmltcG9ydCBleHRlbnNpb25Sb3V0aW5nIGZyb20gJy4vcm91dGluZy9leHRlbnNpb24tcm91dGluZyc7Ci8vIEluaXQgdGhlIHBhY2thZ2UKZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gKHBsdWdpbikgewogIC8vIEF1dG8taW1wb3J0IG1vZGVsLCBkZXRhaWwsIGVkaXQgZnJvbSB0aGUgZm9sZGVycwogIGltcG9ydFR5cGVzKHBsdWdpbik7CiAgLy8gUHJvdmlkZSBwbHVnaW4gbWV0YWRhdGEgZnJvbSBwYWNrYWdlLmpzb24KICBwbHVnaW4ubWV0YWRhdGEgPSByZXF1aXJlKCcuL3BhY2thZ2UuanNvbicpOwogIC8vIExvYWQgYSBwcm9kdWN0CiAgcGx1Z2luLmFkZFByb2R1Y3QocmVxdWlyZSgnLi9wcm9kdWN0JykpOwogIC8vIEFkZCBWdWUgUm91dGVzCiAgcGx1Z2luLmFkZFJvdXRlcyhleHRlbnNpb25Sb3V0aW5nKTsKfQ=="}, {"version": 3, "names": ["importTypes", "extensionRouting", "plugin", "metadata", "require", "addProduct", "addRoutes"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/index.ts"], "sourcesContent": ["import { importTypes } from '@rancher/auto-import';\nimport { IPlugin } from '@shell/core/types';\nimport extensionRouting from './routing/extension-routing';\n\n// Init the package\nexport default function(plugin: IPlugin): void {\n  // Auto-import model, detail, edit from the folders\n  importTypes(plugin);\n\n  // Provide plugin metadata from package.json\n  plugin.metadata = require('./package.json');\n\n  // Load a product\n  plugin.addProduct(require('./product'));\n\n  // Add Vue Routes\n  plugin.addRoutes(extensionRouting);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,sBAAsB;AAElD,OAAOC,gBAAgB,MAAM,6BAA6B;AAE1D;AACA,eAAc,UAAUC,MAAe;EACrC;EACAF,WAAW,CAACE,MAAM,CAAC;EAEnB;EACAA,MAAM,CAACC,QAAQ,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EAE3C;EACAF,MAAM,CAACG,UAAU,CAACD,OAAO,CAAC,WAAW,CAAC,CAAC;EAEvC;EACAF,MAAM,CAACI,SAAS,CAACL,gBAAgB,CAAC;AACpC", "ignoreList": []}]}