{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>oomGame',\n  data() {\n    return {\n      connected: false,\n      connectionStatus: 'connecting',\n      connectionMessage: 'Connecting to KubeDoom...',\n      vncClient: null,\n      clusterName: '',\n      podsKilled: 0,\n      podsRemaining: 0,\n      podsRespawned: 0,\n      recentKills: [],\n      clusterHealth: {\n        status: 'healthy',\n        icon: 'icon-checkmark',\n        message: 'C<PERSON> is healthy'\n      },\n      monitoringInterval: null\n    };\n  },\n  computed: {\n    statusIcon() {\n      switch (this.connectionStatus) {\n        case 'connected':\n          return 'icon-checkmark';\n        case 'connecting':\n          return 'icon-spinner';\n        case 'error':\n          return 'icon-error';\n        default:\n          return 'icon-info';\n      }\n    },\n    statusText() {\n      switch (this.connectionStatus) {\n        case 'connected':\n          return 'Connected to KubeDoom';\n        case 'connecting':\n          return 'Connecting...';\n        case 'error':\n          return 'Connection failed';\n        default:\n          return 'Unknown status';\n      }\n    }\n  },\n  async mounted() {\n    this.clusterName = this.$route.params.cluster || 'Unknown';\n    await this.initializeVNC();\n    this.startMonitoring();\n  },\n  beforeUnmount() {\n    this.cleanup();\n  },\n  methods: {\n    async initializeVNC() {\n      try {\n        this.connectionMessage = 'Initializing VNC connection...';\n\n        // In a real implementation, this would connect to the KubeDoom VNC server\n        // For now, we'll simulate the connection\n        await this.simulateVNCConnection();\n      } catch (error) {\n        console.error('Failed to initialize VNC:', error);\n        this.connectionStatus = 'error';\n        this.connectionMessage = 'Failed to connect to KubeDoom';\n      }\n    },\n    async simulateVNCConnection() {\n      // Simulate connection delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      this.connected = true;\n      this.connectionStatus = 'connected';\n\n      // Initialize canvas for game display\n      this.initializeCanvas();\n    },\n    initializeCanvas() {\n      const canvas = this.$refs.vncCanvas;\n      if (!canvas) return;\n      canvas.width = 800;\n      canvas.height = 600;\n      const ctx = canvas.getContext('2d');\n\n      // Draw a placeholder game screen\n      ctx.fillStyle = '#000';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      ctx.fillStyle = '#ff0000';\n      ctx.font = '24px Arial';\n      ctx.textAlign = 'center';\n      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);\n      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);\n\n      // Add click handler for interaction\n      canvas.addEventListener('click', this.handleCanvasClick);\n    },\n    handleCanvasClick() {\n      // Simulate pod kill when clicking on the game\n      this.simulatePodKill();\n    },\n    simulatePodKill() {\n      const podNames = ['nginx-deployment-abc123', 'redis-master-def456', 'postgres-ghi789', 'api-server-jkl012', 'worker-mno345'];\n      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];\n      const kill = {\n        id: Date.now(),\n        podName: podNames[Math.floor(Math.random() * podNames.length)],\n        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],\n        timestamp: new Date()\n      };\n      this.recentKills.unshift(kill);\n      if (this.recentKills.length > 10) {\n        this.recentKills.pop();\n      }\n      this.podsKilled++;\n      this.podsRemaining = Math.max(0, this.podsRemaining - 1);\n\n      // Update cluster health based on kills\n      this.updateClusterHealth();\n    },\n    updateClusterHealth() {\n      if (this.podsKilled > 20) {\n        this.clusterHealth = {\n          status: 'critical',\n          icon: 'icon-error',\n          message: 'Cluster under heavy stress!'\n        };\n      } else if (this.podsKilled > 10) {\n        this.clusterHealth = {\n          status: 'warning',\n          icon: 'icon-warning',\n          message: 'Cluster experiencing some stress'\n        };\n      } else {\n        this.clusterHealth = {\n          status: 'healthy',\n          icon: 'icon-checkmark',\n          message: 'Cluster is healthy'\n        };\n      }\n    },\n    sendCheat(cheatCode) {\n      // In a real implementation, this would send the cheat to the VNC session\n      console.log('Sending cheat:', cheatCode);\n      this.$store.dispatch('growl/info', {\n        title: 'Cheat Activated',\n        message: `Cheat code \"${cheatCode}\" sent to game`\n      });\n    },\n    startMonitoring() {\n      this.podsRemaining = 50; // Initial pod count\n\n      this.monitoringInterval = setInterval(() => {\n        // Simulate pod respawning\n        if (Math.random() < 0.1 && this.podsRemaining < 50) {\n          this.podsRemaining++;\n          this.podsRespawned++;\n        }\n      }, 5000);\n    },\n    formatTime(timestamp) {\n      return timestamp.toLocaleTimeString();\n    },\n    goBack() {\n      this.$router.push({\n        name: 'kubedoom-c-cluster-dashboard',\n        params: {\n          cluster: this.$route.params.cluster\n        }\n      });\n    },\n    async emergencyStop() {\n      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {\n        await this.cleanup();\n        this.goBack();\n      }\n    },\n    cleanup() {\n      if (this.monitoringInterval) {\n        clearInterval(this.monitoringInterval);\n      }\n      if (this.vncClient) {\n        // Disconnect VNC client\n        this.vncClient = null;\n      }\n      const canvas = this.$refs.vncCanvas;\n      if (canvas) {\n        canvas.removeEventListener('click', this.handleCanvasClick);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "connected", "connectionStatus", "connectionMessage", "vncClient", "clusterName", "podsKilled", "podsRemaining", "podsRespawned", "recentKills", "clusterHealth", "status", "icon", "message", "monitoringInterval", "computed", "statusIcon", "statusText", "mounted", "$route", "params", "cluster", "initializeVNC", "startMonitoring", "beforeUnmount", "cleanup", "methods", "simulateVNCConnection", "error", "console", "Promise", "resolve", "setTimeout", "initializeCanvas", "canvas", "$refs", "vncCanvas", "width", "height", "ctx", "getContext", "fillStyle", "fillRect", "font", "textAlign", "fillText", "addEventListener", "handleCanvasClick", "simulatePodKill", "podNames", "namespaces", "kill", "id", "Date", "now", "podName", "Math", "floor", "random", "length", "namespace", "timestamp", "unshift", "pop", "max", "updateClusterHealth", "sendCheat", "cheatCode", "log", "$store", "dispatch", "title", "setInterval", "formatTime", "toLocaleTimeString", "goBack", "$router", "push", "emergencyStop", "confirm", "clearInterval", "removeEventListener"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue"], "sourcesContent": ["<template>\n  <div class=\"kubedoom-game\">\n    <div class=\"game-header\">\n      <div class=\"header-left\">\n        <button class=\"btn btn-secondary\" @click=\"goBack\">\n          <i class=\"icon icon-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n      \n      <div class=\"header-center\">\n        <h2>KubeDoom - {{ clusterName }}</h2>\n        <div class=\"status\" :class=\"connectionStatus\">\n          <i class=\"icon\" :class=\"statusIcon\"></i>\n          {{ statusText }}\n        </div>\n      </div>\n      \n      <div class=\"header-right\">\n        <button class=\"btn btn-danger\" @click=\"emergencyStop\">\n          <i class=\"icon icon-stop\"></i>\n          Emergency Stop\n        </button>\n      </div>\n    </div>\n\n    <div class=\"game-content\">\n      <div class=\"game-area\">\n        <div class=\"vnc-container\" ref=\"vncContainer\">\n          <div v-if=\"!connected\" class=\"connection-overlay\">\n            <div class=\"spinner\"></div>\n            <p>{{ connectionMessage }}</p>\n          </div>\n          <canvas ref=\"vncCanvas\" v-show=\"connected\"></canvas>\n        </div>\n        \n        <div class=\"game-controls\">\n          <div class=\"control-group\">\n            <h4>Game Controls</h4>\n            <div class=\"controls-grid\">\n              <div class=\"control-item\">\n                <span class=\"key\">WASD</span>\n                <span class=\"desc\">Move</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Mouse</span>\n                <span class=\"desc\">Look around</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Ctrl</span>\n                <span class=\"desc\">Fire</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Space</span>\n                <span class=\"desc\">Open doors</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">ESC</span>\n                <span class=\"desc\">Pause</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"control-group\">\n            <h4>Cheats</h4>\n            <div class=\"cheat-buttons\">\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idkfa')\">\n                All Weapons\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('iddqd')\">\n                God Mode\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idspispopd')\">\n                No Clip\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"pod-monitor\">\n        <h3>Pod Monitor</h3>\n        <div class=\"monitor-stats\">\n          <div class=\"stat\">\n            <span class=\"label\">Pods Killed:</span>\n            <span class=\"value killed\">{{ podsKilled }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Remaining:</span>\n            <span class=\"value remaining\">{{ podsRemaining }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Respawned:</span>\n            <span class=\"value respawned\">{{ podsRespawned }}</span>\n          </div>\n        </div>\n\n        <div class=\"recent-kills\" v-if=\"recentKills.length > 0\">\n          <h4>Recent Pod Kills</h4>\n          <div class=\"kill-list\">\n            <div \n              v-for=\"kill in recentKills\" \n              :key=\"kill.id\"\n              class=\"kill-item\"\n            >\n              <span class=\"pod-name\">{{ kill.podName }}</span>\n              <span class=\"namespace\">{{ kill.namespace }}</span>\n              <span class=\"time\">{{ formatTime(kill.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"cluster-health\">\n          <h4>Cluster Health</h4>\n          <div class=\"health-indicator\" :class=\"clusterHealth.status\">\n            <i class=\"icon\" :class=\"clusterHealth.icon\"></i>\n            {{ clusterHealth.message }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomGame',\n  \n  data() {\n    return {\n      connected: false,\n      connectionStatus: 'connecting',\n      connectionMessage: 'Connecting to KubeDoom...',\n      vncClient: null,\n      clusterName: '',\n      podsKilled: 0,\n      podsRemaining: 0,\n      podsRespawned: 0,\n      recentKills: [],\n      clusterHealth: {\n        status: 'healthy',\n        icon: 'icon-checkmark',\n        message: 'Cluster is healthy'\n      },\n      monitoringInterval: null,\n    };\n  },\n\n  computed: {\n    statusIcon() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'icon-checkmark';\n        case 'connecting': return 'icon-spinner';\n        case 'error': return 'icon-error';\n        default: return 'icon-info';\n      }\n    },\n\n    statusText() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'Connected to KubeDoom';\n        case 'connecting': return 'Connecting...';\n        case 'error': return 'Connection failed';\n        default: return 'Unknown status';\n      }\n    }\n  },\n\n  async mounted() {\n    this.clusterName = this.$route.params.cluster || 'Unknown';\n    await this.initializeVNC();\n    this.startMonitoring();\n  },\n\n  beforeUnmount() {\n    this.cleanup();\n  },\n\n  methods: {\n    async initializeVNC() {\n      try {\n        this.connectionMessage = 'Initializing VNC connection...';\n        \n        // In a real implementation, this would connect to the KubeDoom VNC server\n        // For now, we'll simulate the connection\n        await this.simulateVNCConnection();\n        \n      } catch (error) {\n        console.error('Failed to initialize VNC:', error);\n        this.connectionStatus = 'error';\n        this.connectionMessage = 'Failed to connect to KubeDoom';\n      }\n    },\n\n    async simulateVNCConnection() {\n      // Simulate connection delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      this.connected = true;\n      this.connectionStatus = 'connected';\n      \n      // Initialize canvas for game display\n      this.initializeCanvas();\n    },\n\n    initializeCanvas() {\n      const canvas = this.$refs.vncCanvas;\n      if (!canvas) return;\n\n      canvas.width = 800;\n      canvas.height = 600;\n      \n      const ctx = canvas.getContext('2d');\n      \n      // Draw a placeholder game screen\n      ctx.fillStyle = '#000';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      \n      ctx.fillStyle = '#ff0000';\n      ctx.font = '24px Arial';\n      ctx.textAlign = 'center';\n      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);\n      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);\n      \n      // Add click handler for interaction\n      canvas.addEventListener('click', this.handleCanvasClick);\n    },\n\n    handleCanvasClick() {\n      // Simulate pod kill when clicking on the game\n      this.simulatePodKill();\n    },\n\n    simulatePodKill() {\n      const podNames = [\n        'nginx-deployment-abc123',\n        'redis-master-def456',\n        'postgres-ghi789',\n        'api-server-jkl012',\n        'worker-mno345'\n      ];\n      \n      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];\n      \n      const kill = {\n        id: Date.now(),\n        podName: podNames[Math.floor(Math.random() * podNames.length)],\n        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],\n        timestamp: new Date()\n      };\n      \n      this.recentKills.unshift(kill);\n      if (this.recentKills.length > 10) {\n        this.recentKills.pop();\n      }\n      \n      this.podsKilled++;\n      this.podsRemaining = Math.max(0, this.podsRemaining - 1);\n      \n      // Update cluster health based on kills\n      this.updateClusterHealth();\n    },\n\n    updateClusterHealth() {\n      if (this.podsKilled > 20) {\n        this.clusterHealth = {\n          status: 'critical',\n          icon: 'icon-error',\n          message: 'Cluster under heavy stress!'\n        };\n      } else if (this.podsKilled > 10) {\n        this.clusterHealth = {\n          status: 'warning',\n          icon: 'icon-warning',\n          message: 'Cluster experiencing some stress'\n        };\n      } else {\n        this.clusterHealth = {\n          status: 'healthy',\n          icon: 'icon-checkmark',\n          message: 'Cluster is healthy'\n        };\n      }\n    },\n\n    sendCheat(cheatCode) {\n      // In a real implementation, this would send the cheat to the VNC session\n      console.log('Sending cheat:', cheatCode);\n      \n      this.$store.dispatch('growl/info', {\n        title: 'Cheat Activated',\n        message: `Cheat code \"${cheatCode}\" sent to game`\n      });\n    },\n\n    startMonitoring() {\n      this.podsRemaining = 50; // Initial pod count\n      \n      this.monitoringInterval = setInterval(() => {\n        // Simulate pod respawning\n        if (Math.random() < 0.1 && this.podsRemaining < 50) {\n          this.podsRemaining++;\n          this.podsRespawned++;\n        }\n      }, 5000);\n    },\n\n    formatTime(timestamp) {\n      return timestamp.toLocaleTimeString();\n    },\n\n    goBack() {\n      this.$router.push({\n        name: 'kubedoom-c-cluster-dashboard',\n        params: { cluster: this.$route.params.cluster }\n      });\n    },\n\n    async emergencyStop() {\n      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {\n        await this.cleanup();\n        this.goBack();\n      }\n    },\n\n    cleanup() {\n      if (this.monitoringInterval) {\n        clearInterval(this.monitoringInterval);\n      }\n      \n      if (this.vncClient) {\n        // Disconnect VNC client\n        this.vncClient = null;\n      }\n      \n      const canvas = this.$refs.vncCanvas;\n      if (canvas) {\n        canvas.removeEventListener('click', this.handleCanvasClick);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-game {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #1a1a1a;\n  color: white;\n\n  .game-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 20px;\n    background: #2d2d2d;\n    border-bottom: 1px solid #444;\n\n    .header-center {\n      text-align: center;\n\n      h2 {\n        margin: 0 0 5px 0;\n        color: #ff6b6b;\n      }\n\n      .status {\n        font-size: 14px;\n        \n        &.connected { color: #28a745; }\n        &.connecting { color: #ffc107; }\n        &.error { color: #dc3545; }\n\n        .icon {\n          margin-right: 5px;\n        }\n      }\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-weight: 500;\n\n      &.btn-secondary {\n        background: #6c757d;\n        color: white;\n      }\n\n      &.btn-danger {\n        background: #dc3545;\n        color: white;\n      }\n\n      .icon {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  .game-content {\n    flex: 1;\n    display: grid;\n    grid-template-columns: 1fr 300px;\n    gap: 20px;\n    padding: 20px;\n\n    .game-area {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n\n      .vnc-container {\n        position: relative;\n        background: #000;\n        border: 2px solid #444;\n        border-radius: 8px;\n        overflow: hidden;\n        height: 600px;\n\n        .connection-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          background: rgba(0, 0, 0, 0.8);\n          z-index: 10;\n\n          .spinner {\n            width: 40px;\n            height: 40px;\n            border: 4px solid #333;\n            border-top: 4px solid #ff6b6b;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-bottom: 20px;\n          }\n\n          p {\n            color: #ccc;\n            font-size: 16px;\n          }\n        }\n\n        canvas {\n          width: 100%;\n          height: 100%;\n          cursor: crosshair;\n        }\n      }\n\n      .game-controls {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 20px;\n\n        .control-group {\n          background: #2d2d2d;\n          padding: 15px;\n          border-radius: 8px;\n\n          h4 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n          }\n\n          .controls-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 10px;\n\n            .control-item {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n              padding: 5px 0;\n\n              .key {\n                background: #444;\n                padding: 2px 8px;\n                border-radius: 4px;\n                font-family: monospace;\n                font-size: 12px;\n              }\n\n              .desc {\n                font-size: 12px;\n                color: #ccc;\n              }\n            }\n          }\n\n          .cheat-buttons {\n            display: flex;\n            flex-direction: column;\n            gap: 8px;\n\n            .btn {\n              padding: 6px 12px;\n              background: #444;\n              color: white;\n              border: none;\n              border-radius: 4px;\n              cursor: pointer;\n              font-size: 12px;\n\n              &:hover {\n                background: #555;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .pod-monitor {\n      background: #2d2d2d;\n      padding: 20px;\n      border-radius: 8px;\n      height: fit-content;\n\n      h3 {\n        margin: 0 0 20px 0;\n        color: #ff6b6b;\n      }\n\n      .monitor-stats {\n        margin-bottom: 25px;\n\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n\n          .label {\n            color: #ccc;\n          }\n\n          .value {\n            font-weight: 600;\n\n            &.killed { color: #dc3545; }\n            &.remaining { color: #28a745; }\n            &.respawned { color: #17a2b8; }\n          }\n        }\n      }\n\n      .recent-kills {\n        margin-bottom: 25px;\n\n        h4 {\n          margin: 0 0 15px 0;\n          color: #ffc107;\n        }\n\n        .kill-list {\n          max-height: 200px;\n          overflow-y: auto;\n\n          .kill-item {\n            display: flex;\n            flex-direction: column;\n            padding: 8px;\n            margin-bottom: 8px;\n            background: #1a1a1a;\n            border-radius: 4px;\n            font-size: 12px;\n\n            .pod-name {\n              font-weight: 600;\n              color: #ff6b6b;\n            }\n\n            .namespace {\n              color: #17a2b8;\n            }\n\n            .time {\n              color: #6c757d;\n            }\n          }\n        }\n      }\n\n      .cluster-health {\n        h4 {\n          margin: 0 0 15px 0;\n          color: #28a745;\n        }\n\n        .health-indicator {\n          padding: 10px;\n          border-radius: 4px;\n          text-align: center;\n          font-weight: 500;\n\n          &.healthy {\n            background: rgba(40, 167, 69, 0.2);\n            color: #28a745;\n          }\n\n          &.warning {\n            background: rgba(255, 193, 7, 0.2);\n            color: #ffc107;\n          }\n\n          &.critical {\n            background: rgba(220, 53, 69, 0.2);\n            color: #dc3545;\n          }\n\n          .icon {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n</style>\n"], "mappings": "AA6HA,eAAe;EACbA,IAAI,EAAE,cAAc;EAEpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,2BAA2B;MAC9CC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;QACbC,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE,gBAAgB;QACtBC,OAAO,EAAE;MACX,CAAC;MACDC,kBAAkB,EAAE;IACtB,CAAC;EACH,CAAC;EAEDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,QAAQ,IAAI,CAACd,gBAAgB;QAC3B,KAAK,WAAW;UAAE,OAAO,gBAAgB;QACzC,KAAK,YAAY;UAAE,OAAO,cAAc;QACxC,KAAK,OAAO;UAAE,OAAO,YAAY;QACjC;UAAS,OAAO,WAAW;MAC7B;IACF,CAAC;IAEDe,UAAUA,CAAA,EAAG;MACX,QAAQ,IAAI,CAACf,gBAAgB;QAC3B,KAAK,WAAW;UAAE,OAAO,uBAAuB;QAChD,KAAK,YAAY;UAAE,OAAO,eAAe;QACzC,KAAK,OAAO;UAAE,OAAO,mBAAmB;QACxC;UAAS,OAAO,gBAAgB;MAClC;IACF;EACF,CAAC;EAED,MAAMgB,OAAOA,CAAA,EAAG;IACd,IAAI,CAACb,WAAU,GAAI,IAAI,CAACc,MAAM,CAACC,MAAM,CAACC,OAAM,IAAK,SAAS;IAC1D,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAAC,CAAC;EAChB,CAAC;EAEDC,OAAO,EAAE;IACP,MAAMJ,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,IAAI,CAACnB,iBAAgB,GAAI,gCAAgC;;QAEzD;QACA;QACA,MAAM,IAAI,CAACwB,qBAAqB,CAAC,CAAC;MAEpC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC1B,gBAAe,GAAI,OAAO;QAC/B,IAAI,CAACC,iBAAgB,GAAI,+BAA+B;MAC1D;IACF,CAAC;IAED,MAAMwB,qBAAqBA,CAAA,EAAG;MAC5B;MACA,MAAM,IAAIG,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAI,CAAC9B,SAAQ,GAAI,IAAI;MACrB,IAAI,CAACC,gBAAe,GAAI,WAAW;;MAEnC;MACA,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;IACzB,CAAC;IAEDA,gBAAgBA,CAAA,EAAG;MACjB,MAAMC,MAAK,GAAI,IAAI,CAACC,KAAK,CAACC,SAAS;MACnC,IAAI,CAACF,MAAM,EAAE;MAEbA,MAAM,CAACG,KAAI,GAAI,GAAG;MAClBH,MAAM,CAACI,MAAK,GAAI,GAAG;MAEnB,MAAMC,GAAE,GAAIL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;;MAEnC;MACAD,GAAG,CAACE,SAAQ,GAAI,MAAM;MACtBF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAER,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;MAE/CC,GAAG,CAACE,SAAQ,GAAI,SAAS;MACzBF,GAAG,CAACI,IAAG,GAAI,YAAY;MACvBJ,GAAG,CAACK,SAAQ,GAAI,QAAQ;MACxBL,GAAG,CAACM,QAAQ,CAAC,sBAAsB,EAAEX,MAAM,CAACG,KAAI,GAAI,CAAC,EAAEH,MAAM,CAACI,MAAK,GAAI,CAAC,CAAC;MACzEC,GAAG,CAACM,QAAQ,CAAC,8BAA8B,EAAEX,MAAM,CAACG,KAAI,GAAI,CAAC,EAAEH,MAAM,CAACI,MAAK,GAAI,IAAI,EAAE,CAAC;;MAEtF;MACAJ,MAAM,CAACY,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,iBAAiB,CAAC;IAC1D,CAAC;IAEDA,iBAAiBA,CAAA,EAAG;MAClB;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACxB,CAAC;IAEDA,eAAeA,CAAA,EAAG;MAChB,MAAMC,QAAO,GAAI,CACf,yBAAyB,EACzB,qBAAqB,EACrB,iBAAiB,EACjB,mBAAmB,EACnB,eAAc,CACf;MAED,MAAMC,UAAS,GAAI,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC;MAEtE,MAAMC,IAAG,GAAI;QACXC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,OAAO,EAAEN,QAAQ,CAACO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIT,QAAQ,CAACU,MAAM,CAAC,CAAC;QAC9DC,SAAS,EAAEV,UAAU,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIR,UAAU,CAACS,MAAM,CAAC,CAAC;QACpEE,SAAS,EAAE,IAAIR,IAAI,CAAC;MACtB,CAAC;MAED,IAAI,CAAC5C,WAAW,CAACqD,OAAO,CAACX,IAAI,CAAC;MAC9B,IAAI,IAAI,CAAC1C,WAAW,CAACkD,MAAK,GAAI,EAAE,EAAE;QAChC,IAAI,CAAClD,WAAW,CAACsD,GAAG,CAAC,CAAC;MACxB;MAEA,IAAI,CAACzD,UAAU,EAAE;MACjB,IAAI,CAACC,aAAY,GAAIiD,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzD,aAAY,GAAI,CAAC,CAAC;;MAExD;MACA,IAAI,CAAC0D,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAEDA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAC3D,UAAS,GAAI,EAAE,EAAE;QACxB,IAAI,CAACI,aAAY,GAAI;UACnBC,MAAM,EAAE,UAAU;UAClBC,IAAI,EAAE,YAAY;UAClBC,OAAO,EAAE;QACX,CAAC;MACH,OAAO,IAAI,IAAI,CAACP,UAAS,GAAI,EAAE,EAAE;QAC/B,IAAI,CAACI,aAAY,GAAI;UACnBC,MAAM,EAAE,SAAS;UACjBC,IAAI,EAAE,cAAc;UACpBC,OAAO,EAAE;QACX,CAAC;MACH,OAAO;QACL,IAAI,CAACH,aAAY,GAAI;UACnBC,MAAM,EAAE,SAAS;UACjBC,IAAI,EAAE,gBAAgB;UACtBC,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IAEDqD,SAASA,CAACC,SAAS,EAAE;MACnB;MACAtC,OAAO,CAACuC,GAAG,CAAC,gBAAgB,EAAED,SAAS,CAAC;MAExC,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,YAAY,EAAE;QACjCC,KAAK,EAAE,iBAAiB;QACxB1D,OAAO,EAAE,eAAesD,SAAS;MACnC,CAAC,CAAC;IACJ,CAAC;IAED5C,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAChB,aAAY,GAAI,EAAE,EAAE;;MAEzB,IAAI,CAACO,kBAAiB,GAAI0D,WAAW,CAAC,MAAM;QAC1C;QACA,IAAIhB,IAAI,CAACE,MAAM,CAAC,IAAI,GAAE,IAAK,IAAI,CAACnD,aAAY,GAAI,EAAE,EAAE;UAClD,IAAI,CAACA,aAAa,EAAE;UACpB,IAAI,CAACC,aAAa,EAAE;QACtB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDiE,UAAUA,CAACZ,SAAS,EAAE;MACpB,OAAOA,SAAS,CAACa,kBAAkB,CAAC,CAAC;IACvC,CAAC;IAEDC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAChB9E,IAAI,EAAE,8BAA8B;QACpCqB,MAAM,EAAE;UAAEC,OAAO,EAAE,IAAI,CAACF,MAAM,CAACC,MAAM,CAACC;QAAQ;MAChD,CAAC,CAAC;IACJ,CAAC;IAED,MAAMyD,aAAaA,CAAA,EAAG;MACpB,IAAIC,OAAO,CAAC,yEAAyE,CAAC,EAAE;QACtF,MAAM,IAAI,CAACtD,OAAO,CAAC,CAAC;QACpB,IAAI,CAACkD,MAAM,CAAC,CAAC;MACf;IACF,CAAC;IAEDlD,OAAOA,CAAA,EAAG;MACR,IAAI,IAAI,CAACX,kBAAkB,EAAE;QAC3BkE,aAAa,CAAC,IAAI,CAAClE,kBAAkB,CAAC;MACxC;MAEA,IAAI,IAAI,CAACV,SAAS,EAAE;QAClB;QACA,IAAI,CAACA,SAAQ,GAAI,IAAI;MACvB;MAEA,MAAM8B,MAAK,GAAI,IAAI,CAACC,KAAK,CAACC,SAAS;MACnC,IAAIF,MAAM,EAAE;QACVA,MAAM,CAAC+C,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAClC,iBAAiB,CAAC;MAC7D;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}