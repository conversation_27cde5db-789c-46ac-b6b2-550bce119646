{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"kubedoom-game\"\n};\nconst _hoisted_2 = {\n  class: \"game-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  class: \"header-center\"\n};\nconst _hoisted_5 = {\n  class: \"header-right\"\n};\nconst _hoisted_6 = {\n  class: \"game-content\"\n};\nconst _hoisted_7 = {\n  class: \"game-area\"\n};\nconst _hoisted_8 = {\n  class: \"vnc-container\",\n  ref: \"vncContainer\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"connection-overlay\"\n};\nconst _hoisted_10 = {\n  ref: \"vncCanvas\"\n};\nconst _hoisted_11 = {\n  class: \"game-controls\"\n};\nconst _hoisted_12 = {\n  class: \"control-group\"\n};\nconst _hoisted_13 = {\n  class: \"cheat-buttons\"\n};\nconst _hoisted_14 = {\n  class: \"pod-monitor\"\n};\nconst _hoisted_15 = {\n  class: \"monitor-stats\"\n};\nconst _hoisted_16 = {\n  class: \"stat\"\n};\nconst _hoisted_17 = {\n  class: \"value killed\"\n};\nconst _hoisted_18 = {\n  class: \"stat\"\n};\nconst _hoisted_19 = {\n  class: \"value remaining\"\n};\nconst _hoisted_20 = {\n  class: \"stat\"\n};\nconst _hoisted_21 = {\n  class: \"value respawned\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"recent-kills\"\n};\nconst _hoisted_23 = {\n  class: \"kill-list\"\n};\nconst _hoisted_24 = {\n  class: \"pod-name\"\n};\nconst _hoisted_25 = {\n  class: \"namespace\"\n};\nconst _hoisted_26 = {\n  class: \"time\"\n};\nconst _hoisted_27 = {\n  class: \"cluster-health\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"btn btn-secondary\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))\n  }, _cache[5] || (_cache[5] = [_createElementVNode(\"i\", {\n    class: \"icon icon-arrow-left\"\n  }, null, -1), _createTextVNode(\" Back to Dashboard \")]))]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h2\", null, \"KubeDoom - \" + _toDisplayString($data.clusterName), 1), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"status\", $data.connectionStatus])\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"icon\", $options.statusIcon])\n  }, null, 2), _createTextVNode(\" \" + _toDisplayString($options.statusText), 1)], 2)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: \"btn btn-danger\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.emergencyStop && $options.emergencyStop(...args))\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n    class: \"icon icon-stop\"\n  }, null, -1), _createTextVNode(\" Emergency Stop \")]))])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [!$data.connected ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"spinner\"\n  }, null, -1)), _createElementVNode(\"p\", null, _toDisplayString($data.connectionMessage), 1)])) : _createCommentVNode(\"\", true), _withDirectives(_createElementVNode(\"canvas\", _hoisted_10, null, 512), [[_vShow, $data.connected]])], 512), _createElementVNode(\"div\", _hoisted_11, [_cache[9] || (_cache[9] = _createStaticVNode(\"<div class=\\\"control-group\\\" data-v-6d118c02><h4 data-v-6d118c02>Game Controls</h4><div class=\\\"controls-grid\\\" data-v-6d118c02><div class=\\\"control-item\\\" data-v-6d118c02><span class=\\\"key\\\" data-v-6d118c02>WASD</span><span class=\\\"desc\\\" data-v-6d118c02>Move</span></div><div class=\\\"control-item\\\" data-v-6d118c02><span class=\\\"key\\\" data-v-6d118c02>Mouse</span><span class=\\\"desc\\\" data-v-6d118c02>Look around</span></div><div class=\\\"control-item\\\" data-v-6d118c02><span class=\\\"key\\\" data-v-6d118c02>Ctrl</span><span class=\\\"desc\\\" data-v-6d118c02>Fire</span></div><div class=\\\"control-item\\\" data-v-6d118c02><span class=\\\"key\\\" data-v-6d118c02>Space</span><span class=\\\"desc\\\" data-v-6d118c02>Open doors</span></div><div class=\\\"control-item\\\" data-v-6d118c02><span class=\\\"key\\\" data-v-6d118c02>ESC</span><span class=\\\"desc\\\" data-v-6d118c02>Pause</span></div></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_12, [_cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"Cheats\", -1)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"button\", {\n    class: \"btn btn-sm\",\n    onClick: _cache[2] || (_cache[2] = $event => $options.sendCheat('idkfa'))\n  }, \" All Weapons \"), _createElementVNode(\"button\", {\n    class: \"btn btn-sm\",\n    onClick: _cache[3] || (_cache[3] = $event => $options.sendCheat('iddqd'))\n  }, \" God Mode \"), _createElementVNode(\"button\", {\n    class: \"btn btn-sm\",\n    onClick: _cache[4] || (_cache[4] = $event => $options.sendCheat('idspispopd'))\n  }, \" No Clip \")])])])]), _createElementVNode(\"div\", _hoisted_14, [_cache[15] || (_cache[15] = _createElementVNode(\"h3\", null, \"Pod Monitor\", -1)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n    class: \"label\"\n  }, \"Pods Killed:\", -1)), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($data.podsKilled), 1)]), _createElementVNode(\"div\", _hoisted_18, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n    class: \"label\"\n  }, \"Pods Remaining:\", -1)), _createElementVNode(\"span\", _hoisted_19, _toDisplayString($data.podsRemaining), 1)]), _createElementVNode(\"div\", _hoisted_20, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n    class: \"label\"\n  }, \"Pods Respawned:\", -1)), _createElementVNode(\"span\", _hoisted_21, _toDisplayString($data.podsRespawned), 1)])]), $data.recentKills.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_cache[13] || (_cache[13] = _createElementVNode(\"h4\", null, \"Recent Pod Kills\", -1)), _createElementVNode(\"div\", _hoisted_23, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recentKills, kill => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: kill.id,\n      class: \"kill-item\"\n    }, [_createElementVNode(\"span\", _hoisted_24, _toDisplayString(kill.podName), 1), _createElementVNode(\"span\", _hoisted_25, _toDisplayString(kill.namespace), 1), _createElementVNode(\"span\", _hoisted_26, _toDisplayString($options.formatTime(kill.timestamp)), 1)]);\n  }), 128))])])) : _createCommentVNode(\"\", true), _createElementVNode(\"div\", _hoisted_27, [_cache[14] || (_cache[14] = _createElementVNode(\"h4\", null, \"Cluster Health\", -1)), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"health-indicator\", $data.clusterHealth.status])\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"icon\", $data.clusterHealth.icon])\n  }, null, 2), _createTextVNode(\" \" + _toDisplayString($data.clusterHealth.message), 1)], 2)])])])]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "goBack", "_hoisted_4", "_toDisplayString", "$data", "clusterName", "_normalizeClass", "connectionStatus", "statusIcon", "statusText", "_hoisted_5", "emergencyStop", "_hoisted_6", "_hoisted_7", "_hoisted_8", "connected", "_hoisted_9", "connectionMessage", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "$event", "sendCheat", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "podsKilled", "_hoisted_18", "_hoisted_19", "podsRemaining", "_hoisted_20", "_hoisted_21", "podsRespawned", "recentKills", "length", "_hoisted_22", "_hoisted_23", "_Fragment", "_renderList", "kill", "key", "id", "_hoisted_24", "podName", "_hoisted_25", "namespace", "_hoisted_26", "formatTime", "timestamp", "_hoisted_27", "clusterHealth", "status", "icon", "message"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue"], "sourcesContent": ["<template>\n  <div class=\"kubedoom-game\">\n    <div class=\"game-header\">\n      <div class=\"header-left\">\n        <button class=\"btn btn-secondary\" @click=\"goBack\">\n          <i class=\"icon icon-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n      \n      <div class=\"header-center\">\n        <h2>KubeDoom - {{ clusterName }}</h2>\n        <div class=\"status\" :class=\"connectionStatus\">\n          <i class=\"icon\" :class=\"statusIcon\"></i>\n          {{ statusText }}\n        </div>\n      </div>\n      \n      <div class=\"header-right\">\n        <button class=\"btn btn-danger\" @click=\"emergencyStop\">\n          <i class=\"icon icon-stop\"></i>\n          Emergency Stop\n        </button>\n      </div>\n    </div>\n\n    <div class=\"game-content\">\n      <div class=\"game-area\">\n        <div class=\"vnc-container\" ref=\"vncContainer\">\n          <div v-if=\"!connected\" class=\"connection-overlay\">\n            <div class=\"spinner\"></div>\n            <p>{{ connectionMessage }}</p>\n          </div>\n          <canvas ref=\"vncCanvas\" v-show=\"connected\"></canvas>\n        </div>\n        \n        <div class=\"game-controls\">\n          <div class=\"control-group\">\n            <h4>Game Controls</h4>\n            <div class=\"controls-grid\">\n              <div class=\"control-item\">\n                <span class=\"key\">WASD</span>\n                <span class=\"desc\">Move</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Mouse</span>\n                <span class=\"desc\">Look around</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Ctrl</span>\n                <span class=\"desc\">Fire</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Space</span>\n                <span class=\"desc\">Open doors</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">ESC</span>\n                <span class=\"desc\">Pause</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"control-group\">\n            <h4>Cheats</h4>\n            <div class=\"cheat-buttons\">\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idkfa')\">\n                All Weapons\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('iddqd')\">\n                God Mode\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idspispopd')\">\n                No Clip\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"pod-monitor\">\n        <h3>Pod Monitor</h3>\n        <div class=\"monitor-stats\">\n          <div class=\"stat\">\n            <span class=\"label\">Pods Killed:</span>\n            <span class=\"value killed\">{{ podsKilled }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Remaining:</span>\n            <span class=\"value remaining\">{{ podsRemaining }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Respawned:</span>\n            <span class=\"value respawned\">{{ podsRespawned }}</span>\n          </div>\n        </div>\n\n        <div class=\"recent-kills\" v-if=\"recentKills.length > 0\">\n          <h4>Recent Pod Kills</h4>\n          <div class=\"kill-list\">\n            <div \n              v-for=\"kill in recentKills\" \n              :key=\"kill.id\"\n              class=\"kill-item\"\n            >\n              <span class=\"pod-name\">{{ kill.podName }}</span>\n              <span class=\"namespace\">{{ kill.namespace }}</span>\n              <span class=\"time\">{{ formatTime(kill.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"cluster-health\">\n          <h4>Cluster Health</h4>\n          <div class=\"health-indicator\" :class=\"clusterHealth.status\">\n            <i class=\"icon\" :class=\"clusterHealth.icon\"></i>\n            {{ clusterHealth.message }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomGame',\n  \n  data() {\n    return {\n      connected: false,\n      connectionStatus: 'connecting',\n      connectionMessage: 'Connecting to KubeDoom...',\n      vncClient: null,\n      clusterName: '',\n      podsKilled: 0,\n      podsRemaining: 0,\n      podsRespawned: 0,\n      recentKills: [],\n      clusterHealth: {\n        status: 'healthy',\n        icon: 'icon-checkmark',\n        message: 'Cluster is healthy'\n      },\n      monitoringInterval: null,\n    };\n  },\n\n  computed: {\n    statusIcon() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'icon-checkmark';\n        case 'connecting': return 'icon-spinner';\n        case 'error': return 'icon-error';\n        default: return 'icon-info';\n      }\n    },\n\n    statusText() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'Connected to KubeDoom';\n        case 'connecting': return 'Connecting...';\n        case 'error': return 'Connection failed';\n        default: return 'Unknown status';\n      }\n    }\n  },\n\n  async mounted() {\n    this.clusterName = this.$route.params.cluster || 'Unknown';\n    await this.initializeVNC();\n    this.startMonitoring();\n  },\n\n  beforeUnmount() {\n    this.cleanup();\n  },\n\n  methods: {\n    async initializeVNC() {\n      try {\n        this.connectionMessage = 'Initializing VNC connection...';\n        \n        // In a real implementation, this would connect to the KubeDoom VNC server\n        // For now, we'll simulate the connection\n        await this.simulateVNCConnection();\n        \n      } catch (error) {\n        console.error('Failed to initialize VNC:', error);\n        this.connectionStatus = 'error';\n        this.connectionMessage = 'Failed to connect to KubeDoom';\n      }\n    },\n\n    async simulateVNCConnection() {\n      // Simulate connection delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      this.connected = true;\n      this.connectionStatus = 'connected';\n      \n      // Initialize canvas for game display\n      this.initializeCanvas();\n    },\n\n    initializeCanvas() {\n      const canvas = this.$refs.vncCanvas;\n      if (!canvas) return;\n\n      canvas.width = 800;\n      canvas.height = 600;\n      \n      const ctx = canvas.getContext('2d');\n      \n      // Draw a placeholder game screen\n      ctx.fillStyle = '#000';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      \n      ctx.fillStyle = '#ff0000';\n      ctx.font = '24px Arial';\n      ctx.textAlign = 'center';\n      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);\n      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);\n      \n      // Add click handler for interaction\n      canvas.addEventListener('click', this.handleCanvasClick);\n    },\n\n    handleCanvasClick() {\n      // Simulate pod kill when clicking on the game\n      this.simulatePodKill();\n    },\n\n    simulatePodKill() {\n      const podNames = [\n        'nginx-deployment-abc123',\n        'redis-master-def456',\n        'postgres-ghi789',\n        'api-server-jkl012',\n        'worker-mno345'\n      ];\n      \n      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];\n      \n      const kill = {\n        id: Date.now(),\n        podName: podNames[Math.floor(Math.random() * podNames.length)],\n        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],\n        timestamp: new Date()\n      };\n      \n      this.recentKills.unshift(kill);\n      if (this.recentKills.length > 10) {\n        this.recentKills.pop();\n      }\n      \n      this.podsKilled++;\n      this.podsRemaining = Math.max(0, this.podsRemaining - 1);\n      \n      // Update cluster health based on kills\n      this.updateClusterHealth();\n    },\n\n    updateClusterHealth() {\n      if (this.podsKilled > 20) {\n        this.clusterHealth = {\n          status: 'critical',\n          icon: 'icon-error',\n          message: 'Cluster under heavy stress!'\n        };\n      } else if (this.podsKilled > 10) {\n        this.clusterHealth = {\n          status: 'warning',\n          icon: 'icon-warning',\n          message: 'Cluster experiencing some stress'\n        };\n      } else {\n        this.clusterHealth = {\n          status: 'healthy',\n          icon: 'icon-checkmark',\n          message: 'Cluster is healthy'\n        };\n      }\n    },\n\n    sendCheat(cheatCode) {\n      // In a real implementation, this would send the cheat to the VNC session\n      console.log('Sending cheat:', cheatCode);\n      \n      this.$store.dispatch('growl/info', {\n        title: 'Cheat Activated',\n        message: `Cheat code \"${cheatCode}\" sent to game`\n      });\n    },\n\n    startMonitoring() {\n      this.podsRemaining = 50; // Initial pod count\n      \n      this.monitoringInterval = setInterval(() => {\n        // Simulate pod respawning\n        if (Math.random() < 0.1 && this.podsRemaining < 50) {\n          this.podsRemaining++;\n          this.podsRespawned++;\n        }\n      }, 5000);\n    },\n\n    formatTime(timestamp) {\n      return timestamp.toLocaleTimeString();\n    },\n\n    goBack() {\n      this.$router.push({\n        name: 'kubedoom-c-cluster-dashboard',\n        params: { cluster: this.$route.params.cluster }\n      });\n    },\n\n    async emergencyStop() {\n      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {\n        await this.cleanup();\n        this.goBack();\n      }\n    },\n\n    cleanup() {\n      if (this.monitoringInterval) {\n        clearInterval(this.monitoringInterval);\n      }\n      \n      if (this.vncClient) {\n        // Disconnect VNC client\n        this.vncClient = null;\n      }\n      \n      const canvas = this.$refs.vncCanvas;\n      if (canvas) {\n        canvas.removeEventListener('click', this.handleCanvasClick);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-game {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #1a1a1a;\n  color: white;\n\n  .game-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 20px;\n    background: #2d2d2d;\n    border-bottom: 1px solid #444;\n\n    .header-center {\n      text-align: center;\n\n      h2 {\n        margin: 0 0 5px 0;\n        color: #ff6b6b;\n      }\n\n      .status {\n        font-size: 14px;\n        \n        &.connected { color: #28a745; }\n        &.connecting { color: #ffc107; }\n        &.error { color: #dc3545; }\n\n        .icon {\n          margin-right: 5px;\n        }\n      }\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-weight: 500;\n\n      &.btn-secondary {\n        background: #6c757d;\n        color: white;\n      }\n\n      &.btn-danger {\n        background: #dc3545;\n        color: white;\n      }\n\n      .icon {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  .game-content {\n    flex: 1;\n    display: grid;\n    grid-template-columns: 1fr 300px;\n    gap: 20px;\n    padding: 20px;\n\n    .game-area {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n\n      .vnc-container {\n        position: relative;\n        background: #000;\n        border: 2px solid #444;\n        border-radius: 8px;\n        overflow: hidden;\n        height: 600px;\n\n        .connection-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          background: rgba(0, 0, 0, 0.8);\n          z-index: 10;\n\n          .spinner {\n            width: 40px;\n            height: 40px;\n            border: 4px solid #333;\n            border-top: 4px solid #ff6b6b;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-bottom: 20px;\n          }\n\n          p {\n            color: #ccc;\n            font-size: 16px;\n          }\n        }\n\n        canvas {\n          width: 100%;\n          height: 100%;\n          cursor: crosshair;\n        }\n      }\n\n      .game-controls {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 20px;\n\n        .control-group {\n          background: #2d2d2d;\n          padding: 15px;\n          border-radius: 8px;\n\n          h4 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n          }\n\n          .controls-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 10px;\n\n            .control-item {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n              padding: 5px 0;\n\n              .key {\n                background: #444;\n                padding: 2px 8px;\n                border-radius: 4px;\n                font-family: monospace;\n                font-size: 12px;\n              }\n\n              .desc {\n                font-size: 12px;\n                color: #ccc;\n              }\n            }\n          }\n\n          .cheat-buttons {\n            display: flex;\n            flex-direction: column;\n            gap: 8px;\n\n            .btn {\n              padding: 6px 12px;\n              background: #444;\n              color: white;\n              border: none;\n              border-radius: 4px;\n              cursor: pointer;\n              font-size: 12px;\n\n              &:hover {\n                background: #555;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .pod-monitor {\n      background: #2d2d2d;\n      padding: 20px;\n      border-radius: 8px;\n      height: fit-content;\n\n      h3 {\n        margin: 0 0 20px 0;\n        color: #ff6b6b;\n      }\n\n      .monitor-stats {\n        margin-bottom: 25px;\n\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n\n          .label {\n            color: #ccc;\n          }\n\n          .value {\n            font-weight: 600;\n\n            &.killed { color: #dc3545; }\n            &.remaining { color: #28a745; }\n            &.respawned { color: #17a2b8; }\n          }\n        }\n      }\n\n      .recent-kills {\n        margin-bottom: 25px;\n\n        h4 {\n          margin: 0 0 15px 0;\n          color: #ffc107;\n        }\n\n        .kill-list {\n          max-height: 200px;\n          overflow-y: auto;\n\n          .kill-item {\n            display: flex;\n            flex-direction: column;\n            padding: 8px;\n            margin-bottom: 8px;\n            background: #1a1a1a;\n            border-radius: 4px;\n            font-size: 12px;\n\n            .pod-name {\n              font-weight: 600;\n              color: #ff6b6b;\n            }\n\n            .namespace {\n              color: #17a2b8;\n            }\n\n            .time {\n              color: #6c757d;\n            }\n          }\n        }\n      }\n\n      .cluster-health {\n        h4 {\n          margin: 0 0 15px 0;\n          color: #28a745;\n        }\n\n        .health-indicator {\n          padding: 10px;\n          border-radius: 4px;\n          text-align: center;\n          font-weight: 500;\n\n          &.healthy {\n            background: rgba(40, 167, 69, 0.2);\n            color: #28a745;\n          }\n\n          &.warning {\n            background: rgba(255, 193, 7, 0.2);\n            color: #ffc107;\n          }\n\n          &.critical {\n            background: rgba(220, 53, 69, 0.2);\n            color: #dc3545;\n          }\n\n          .icon {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAOnBA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAc;;EAQtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC,eAAe;EAACC,GAAG,EAAC;;;;EACND,KAAK,EAAC;;;EAIrBC,GAAG,EAAC;AAAW;;EAGpBD,KAAK,EAAC;AAAe;;EA2BnBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAe;;EAe3BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAM;;EAETA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAM;;EAETA,KAAK,EAAC;AAAiB;;EAE1BA,KAAK,EAAC;AAAM;;EAETA,KAAK,EAAC;AAAiB;;;EAI5BA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAW;;EAMZA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAM;;EAKnBA,KAAK,EAAC;AAAgB;;uBA/GjCE,mBAAA,CAwHM,OAxHNC,UAwHM,GAvHJC,mBAAA,CAsBM,OAtBNC,UAsBM,GArBJD,mBAAA,CAKM,OALNE,UAKM,GAJJF,mBAAA,CAGS;IAHDJ,KAAK,EAAC,mBAAmB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;gCAC9CL,mBAAA,CAAoC;IAAjCJ,KAAK,EAAC;EAAsB,c,iBAAK,qBAEtC,E,MAGFI,mBAAA,CAMM,OANNQ,UAMM,GALJR,mBAAA,CAAqC,YAAjC,aAAW,GAAAS,gBAAA,CAAGC,KAAA,CAAAC,WAAW,OAC7BX,mBAAA,CAGM;IAHDJ,KAAK,EAAAgB,eAAA,EAAC,QAAQ,EAASF,KAAA,CAAAG,gBAAgB;MAC1Cb,mBAAA,CAAwC;IAArCJ,KAAK,EAAAgB,eAAA,EAAC,MAAM,EAASN,QAAA,CAAAQ,UAAU;gCAAM,GACxC,GAAAL,gBAAA,CAAGH,QAAA,CAAAS,UAAU,M,QAIjBf,mBAAA,CAKM,OALNgB,UAKM,GAJJhB,mBAAA,CAGS;IAHDJ,KAAK,EAAC,gBAAgB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAW,aAAA,IAAAX,QAAA,CAAAW,aAAA,IAAAZ,IAAA,CAAa;gCAClDL,mBAAA,CAA8B;IAA3BJ,KAAK,EAAC;EAAgB,c,iBAAK,kBAEhC,E,QAIJI,mBAAA,CA8FM,OA9FNkB,UA8FM,GA7FJlB,mBAAA,CAmDM,OAnDNmB,UAmDM,GAlDJnB,mBAAA,CAMM,OANNoB,UAMM,G,CALQV,KAAA,CAAAW,SAAS,I,cAArBvB,mBAAA,CAGM,OAHNwB,UAGM,G,0BAFJtB,mBAAA,CAA2B;IAAtBJ,KAAK,EAAC;EAAS,eACpBI,mBAAA,CAA8B,WAAAS,gBAAA,CAAxBC,KAAA,CAAAa,iBAAiB,M,oDAEzBvB,mBAAA,CAAoD,UAApDwB,WAAoD,e,SAApBd,KAAA,CAAAW,SAAS,E,UAG3CrB,mBAAA,CAyCM,OAzCNyB,WAyCM,G,s6BAdJzB,mBAAA,CAaM,OAbN0B,WAaM,G,0BAZJ1B,mBAAA,CAAe,YAAX,QAAM,QACVA,mBAAA,CAUM,OAVN2B,WAUM,GATJ3B,mBAAA,CAES;IAFDJ,KAAK,EAAC,YAAY;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,SAAS;KAAW,eAEvD,GACA7B,mBAAA,CAES;IAFDJ,KAAK,EAAC,YAAY;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,SAAS;KAAW,YAEvD,GACA7B,mBAAA,CAES;IAFDJ,KAAK,EAAC,YAAY;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,SAAS;KAAgB,WAE5D,E,SAMR7B,mBAAA,CAuCM,OAvCN8B,WAuCM,G,4BAtCJ9B,mBAAA,CAAoB,YAAhB,aAAW,QACfA,mBAAA,CAaM,OAbN+B,WAaM,GAZJ/B,mBAAA,CAGM,OAHNgC,WAGM,G,4BAFJhC,mBAAA,CAAuC;IAAjCJ,KAAK,EAAC;EAAO,GAAC,cAAY,QAChCI,mBAAA,CAAkD,QAAlDiC,WAAkD,EAAAxB,gBAAA,CAApBC,KAAA,CAAAwB,UAAU,M,GAE1ClC,mBAAA,CAGM,OAHNmC,WAGM,G,4BAFJnC,mBAAA,CAA0C;IAApCJ,KAAK,EAAC;EAAO,GAAC,iBAAe,QACnCI,mBAAA,CAAwD,QAAxDoC,WAAwD,EAAA3B,gBAAA,CAAvBC,KAAA,CAAA2B,aAAa,M,GAEhDrC,mBAAA,CAGM,OAHNsC,WAGM,G,4BAFJtC,mBAAA,CAA0C;IAApCJ,KAAK,EAAC;EAAO,GAAC,iBAAe,QACnCI,mBAAA,CAAwD,QAAxDuC,WAAwD,EAAA9B,gBAAA,CAAvBC,KAAA,CAAA8B,aAAa,M,KAIlB9B,KAAA,CAAA+B,WAAW,CAACC,MAAM,Q,cAAlD5C,mBAAA,CAaM,OAbN6C,WAaM,G,4BAZJ3C,mBAAA,CAAyB,YAArB,kBAAgB,QACpBA,mBAAA,CAUM,OAVN4C,WAUM,I,kBATJ9C,mBAAA,CAQM+C,SAAA,QAAAC,WAAA,CAPWpC,KAAA,CAAA+B,WAAW,EAAnBM,IAAI;yBADbjD,mBAAA,CAQM;MANHkD,GAAG,EAAED,IAAI,CAACE,EAAE;MACbrD,KAAK,EAAC;QAENI,mBAAA,CAAgD,QAAhDkD,WAAgD,EAAAzC,gBAAA,CAAtBsC,IAAI,CAACI,OAAO,OACtCnD,mBAAA,CAAmD,QAAnDoD,WAAmD,EAAA3C,gBAAA,CAAxBsC,IAAI,CAACM,SAAS,OACzCrD,mBAAA,CAA0D,QAA1DsD,WAA0D,EAAA7C,gBAAA,CAApCH,QAAA,CAAAiD,UAAU,CAACR,IAAI,CAACS,SAAS,O;kDAKrDxD,mBAAA,CAMM,OANNyD,WAMM,G,4BALJzD,mBAAA,CAAuB,YAAnB,gBAAc,QAClBA,mBAAA,CAGM;IAHDJ,KAAK,EAAAgB,eAAA,EAAC,kBAAkB,EAASF,KAAA,CAAAgD,aAAa,CAACC,MAAM;MACxD3D,mBAAA,CAAgD;IAA7CJ,KAAK,EAAAgB,eAAA,EAAC,MAAM,EAASF,KAAA,CAAAgD,aAAa,CAACE,IAAI;gCAAM,GAChD,GAAAnD,gBAAA,CAAGC,KAAA,CAAAgD,aAAa,CAACG,OAAO,M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}