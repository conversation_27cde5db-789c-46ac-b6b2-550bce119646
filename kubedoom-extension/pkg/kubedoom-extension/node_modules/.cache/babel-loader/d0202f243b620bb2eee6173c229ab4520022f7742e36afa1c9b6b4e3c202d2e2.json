{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>oomDashboard',\n  data() {\n    return {\n      selectedCluster: '',\n      selectedNamespace: '',\n      clusters: [],\n      namespaces: [],\n      podCount: 0,\n      runningPods: 0,\n      isDeploying: false,\n      kubeDoomDeployed: false\n    };\n  },\n  async mounted() {\n    await this.loadClusters();\n  },\n  methods: {\n    async loadClusters() {\n      try {\n        // Load clusters from Rancher API\n        const clusters = await this.$store.dispatch('management/findAll', {\n          type: 'cluster'\n        });\n        this.clusters = clusters.filter(cluster => cluster.isReady);\n      } catch (error) {\n        console.error('Failed to load clusters:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to load clusters'\n        });\n      }\n    },\n    async onClusterChange() {\n      if (!this.selectedCluster) {\n        this.namespaces = [];\n        this.podCount = 0;\n        this.runningPods = 0;\n        return;\n      }\n      try {\n        await this.loadNamespaces();\n        await this.loadPodStats();\n      } catch (error) {\n        console.error('Failed to load cluster data:', error);\n      }\n    },\n    async loadNamespaces() {\n      try {\n        // Load namespaces for the selected cluster\n        const namespaces = await this.$store.dispatch('cluster/findAll', {\n          type: 'namespace',\n          clusterId: this.selectedCluster\n        });\n        this.namespaces = namespaces.map(ns => ns.metadata.name);\n      } catch (error) {\n        console.error('Failed to load namespaces:', error);\n      }\n    },\n    async loadPodStats() {\n      try {\n        // Load pod statistics for the selected cluster\n        const pods = await this.$store.dispatch('cluster/findAll', {\n          type: 'pod',\n          clusterId: this.selectedCluster\n        });\n        this.podCount = pods.length;\n        this.runningPods = pods.filter(pod => pod.status?.phase === 'Running').length;\n      } catch (error) {\n        console.error('Failed to load pod stats:', error);\n      }\n    },\n    async startKubeDoom() {\n      this.isDeploying = true;\n      try {\n        // Deploy KubeDoom to the selected cluster\n        await this.deployKubeDoom();\n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom deployed successfully!'\n        });\n\n        // Navigate to the game page\n        this.$router.push({\n          name: 'kubedoom-c-cluster-game',\n          params: {\n            cluster: this.selectedCluster\n          }\n        });\n      } catch (error) {\n        console.error('Failed to deploy KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to deploy KubeDoom: ' + error.message\n        });\n      } finally {\n        this.isDeploying = false;\n      }\n    },\n    async deployKubeDoom() {\n      // This would deploy the KubeDoom pod to the cluster\n      // For now, we'll simulate the deployment\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      this.kubeDoomDeployed = true;\n    },\n    async stopKubeDoom() {\n      try {\n        // Stop and remove KubeDoom deployment\n        await this.removeKubeDoom();\n        this.kubeDoomDeployed = false;\n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom stopped successfully!'\n        });\n      } catch (error) {\n        console.error('Failed to stop KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to stop KubeDoom: ' + error.message\n        });\n      }\n    },\n    async removeKubeDoom() {\n      // This would remove the KubeDoom deployment\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "selectedCluster", "selectedNamespace", "clusters", "namespaces", "podCount", "runningPods", "isDeploying", "kubeDoomDeployed", "mounted", "loadClusters", "methods", "$store", "dispatch", "type", "filter", "cluster", "isReady", "error", "console", "title", "message", "onClusterChange", "loadNamespaces", "loadPodStats", "clusterId", "map", "ns", "metadata", "pods", "length", "pod", "status", "phase", "startKubeDoom", "deployKubeDoom", "$router", "push", "params", "Promise", "resolve", "setTimeout", "stopKubeDoom", "removeKubeDoom"], "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"kubedoom-dashboard\">\n    <div class=\"header\">\n      <h1 class=\"title\">\n        <i class=\"icon icon-kubedoom\"></i>\n        KubeDoom\n      </h1>\n      <p class=\"subtitle\">\n        Kill Kubernetes pods by playing Doom!\n      </p>\n    </div>\n\n    <div class=\"content\">\n      <div class=\"info-section\">\n        <div class=\"info-card\">\n          <h3>What is KubeDoom?</h3>\n          <p>\n            KubeDoom is a fun way to test the resilience of your Kubernetes cluster by playing the classic game Doom. \n            Each enemy in the game represents a pod in your cluster, and killing them will actually delete the corresponding pod.\n          </p>\n        </div>\n\n        <div class=\"info-card\">\n          <h3>How it works</h3>\n          <ul>\n            <li>Each pod in your cluster appears as an enemy in Doom</li>\n            <li>Shooting enemies kills the corresponding pods</li>\n            <li>Watch how your applications handle pod failures</li>\n            <li>Test your cluster's resilience and recovery mechanisms</li>\n          </ul>\n        </div>\n\n        <div class=\"info-card\">\n          <h3>Safety Notice</h3>\n          <div class=\"warning\">\n            <i class=\"icon icon-warning\"></i>\n            <strong>Warning:</strong> This will actually delete pods in your cluster. \n            Only use this in development or testing environments!\n          </div>\n        </div>\n      </div>\n\n      <div class=\"actions\">\n        <div class=\"cluster-selector\">\n          <label>Select Cluster:</label>\n          <select v-model=\"selectedCluster\" @change=\"onClusterChange\">\n            <option value=\"\">Choose a cluster...</option>\n            <option v-for=\"cluster in clusters\" :key=\"cluster.id\" :value=\"cluster.id\">\n              {{ cluster.nameDisplay }}\n            </option>\n          </select>\n        </div>\n\n        <div class=\"namespace-selector\" v-if=\"selectedCluster\">\n          <label>Target Namespace (optional):</label>\n          <select v-model=\"selectedNamespace\">\n            <option value=\"\">All namespaces</option>\n            <option v-for=\"namespace in namespaces\" :key=\"namespace\" :value=\"namespace\">\n              {{ namespace }}\n            </option>\n          </select>\n        </div>\n\n        <div class=\"pod-info\" v-if=\"selectedCluster\">\n          <h4>Cluster Status</h4>\n          <div class=\"stats\">\n            <div class=\"stat\">\n              <span class=\"label\">Total Pods:</span>\n              <span class=\"value\">{{ podCount }}</span>\n            </div>\n            <div class=\"stat\">\n              <span class=\"label\">Running Pods:</span>\n              <span class=\"value\">{{ runningPods }}</span>\n            </div>\n            <div class=\"stat\">\n              <span class=\"label\">Target Namespace:</span>\n              <span class=\"value\">{{ selectedNamespace || 'All' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"game-controls\">\n          <button \n            class=\"btn btn-primary btn-lg\"\n            :disabled=\"!selectedCluster || isDeploying\"\n            @click=\"startKubeDoom\"\n          >\n            <i class=\"icon icon-play\"></i>\n            {{ isDeploying ? 'Deploying KubeDoom...' : 'Start KubeDoom' }}\n          </button>\n          \n          <button \n            class=\"btn btn-secondary\"\n            :disabled=\"!kubeDoomDeployed\"\n            @click=\"stopKubeDoom\"\n          >\n            <i class=\"icon icon-stop\"></i>\n            Stop KubeDoom\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomDashboard',\n  \n  data() {\n    return {\n      selectedCluster: '',\n      selectedNamespace: '',\n      clusters: [],\n      namespaces: [],\n      podCount: 0,\n      runningPods: 0,\n      isDeploying: false,\n      kubeDoomDeployed: false,\n    };\n  },\n\n  async mounted() {\n    await this.loadClusters();\n  },\n\n  methods: {\n    async loadClusters() {\n      try {\n        // Load clusters from Rancher API\n        const clusters = await this.$store.dispatch('management/findAll', { type: 'cluster' });\n        this.clusters = clusters.filter(cluster => cluster.isReady);\n      } catch (error) {\n        console.error('Failed to load clusters:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to load clusters'\n        });\n      }\n    },\n\n    async onClusterChange() {\n      if (!this.selectedCluster) {\n        this.namespaces = [];\n        this.podCount = 0;\n        this.runningPods = 0;\n        return;\n      }\n\n      try {\n        await this.loadNamespaces();\n        await this.loadPodStats();\n      } catch (error) {\n        console.error('Failed to load cluster data:', error);\n      }\n    },\n\n    async loadNamespaces() {\n      try {\n        // Load namespaces for the selected cluster\n        const namespaces = await this.$store.dispatch('cluster/findAll', { \n          type: 'namespace',\n          clusterId: this.selectedCluster \n        });\n        this.namespaces = namespaces.map(ns => ns.metadata.name);\n      } catch (error) {\n        console.error('Failed to load namespaces:', error);\n      }\n    },\n\n    async loadPodStats() {\n      try {\n        // Load pod statistics for the selected cluster\n        const pods = await this.$store.dispatch('cluster/findAll', { \n          type: 'pod',\n          clusterId: this.selectedCluster \n        });\n        \n        this.podCount = pods.length;\n        this.runningPods = pods.filter(pod => pod.status?.phase === 'Running').length;\n      } catch (error) {\n        console.error('Failed to load pod stats:', error);\n      }\n    },\n\n    async startKubeDoom() {\n      this.isDeploying = true;\n      \n      try {\n        // Deploy KubeDoom to the selected cluster\n        await this.deployKubeDoom();\n        \n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom deployed successfully!'\n        });\n        \n        // Navigate to the game page\n        this.$router.push({\n          name: 'kubedoom-c-cluster-game',\n          params: { cluster: this.selectedCluster }\n        });\n        \n      } catch (error) {\n        console.error('Failed to deploy KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to deploy KubeDoom: ' + error.message\n        });\n      } finally {\n        this.isDeploying = false;\n      }\n    },\n\n    async deployKubeDoom() {\n      // This would deploy the KubeDoom pod to the cluster\n      // For now, we'll simulate the deployment\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      this.kubeDoomDeployed = true;\n    },\n\n    async stopKubeDoom() {\n      try {\n        // Stop and remove KubeDoom deployment\n        await this.removeKubeDoom();\n        \n        this.kubeDoomDeployed = false;\n        \n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom stopped successfully!'\n        });\n        \n      } catch (error) {\n        console.error('Failed to stop KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to stop KubeDoom: ' + error.message\n        });\n      }\n    },\n\n    async removeKubeDoom() {\n      // This would remove the KubeDoom deployment\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-dashboard {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n\n  .header {\n    text-align: center;\n    margin-bottom: 40px;\n\n    .title {\n      font-size: 3rem;\n      color: #ff6b6b;\n      margin-bottom: 10px;\n      \n      .icon {\n        margin-right: 15px;\n      }\n    }\n\n    .subtitle {\n      font-size: 1.2rem;\n      color: #666;\n    }\n  }\n\n  .content {\n    display: grid;\n    grid-template-columns: 1fr 400px;\n    gap: 40px;\n    \n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .info-section {\n    .info-card {\n      background: #f8f9fa;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      padding: 20px;\n      margin-bottom: 20px;\n\n      h3 {\n        color: #333;\n        margin-bottom: 15px;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n      }\n\n      .warning {\n        background: #fff3cd;\n        border: 1px solid #ffeaa7;\n        border-radius: 4px;\n        padding: 15px;\n        color: #856404;\n\n        .icon {\n          margin-right: 8px;\n          color: #f39c12;\n        }\n      }\n    }\n  }\n\n  .actions {\n    background: white;\n    border: 1px solid #e9ecef;\n    border-radius: 8px;\n    padding: 20px;\n    height: fit-content;\n\n    .cluster-selector,\n    .namespace-selector {\n      margin-bottom: 20px;\n\n      label {\n        display: block;\n        margin-bottom: 5px;\n        font-weight: 600;\n      }\n\n      select {\n        width: 100%;\n        padding: 8px 12px;\n        border: 1px solid #ddd;\n        border-radius: 4px;\n        font-size: 14px;\n      }\n    }\n\n    .pod-info {\n      margin-bottom: 30px;\n      padding: 15px;\n      background: #f8f9fa;\n      border-radius: 4px;\n\n      h4 {\n        margin-bottom: 15px;\n        color: #333;\n      }\n\n      .stats {\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n\n          .label {\n            font-weight: 500;\n          }\n\n          .value {\n            font-weight: 600;\n            color: #007bff;\n          }\n        }\n      }\n    }\n\n    .game-controls {\n      .btn {\n        width: 100%;\n        margin-bottom: 10px;\n        padding: 12px;\n        font-size: 16px;\n        font-weight: 600;\n        border-radius: 6px;\n        border: none;\n        cursor: pointer;\n        transition: all 0.2s;\n\n        .icon {\n          margin-right: 8px;\n        }\n\n        &.btn-primary {\n          background: #ff6b6b;\n          color: white;\n\n          &:hover:not(:disabled) {\n            background: #ff5252;\n          }\n\n          &:disabled {\n            background: #ccc;\n            cursor: not-allowed;\n          }\n        }\n\n        &.btn-secondary {\n          background: #6c757d;\n          color: white;\n\n          &:hover:not(:disabled) {\n            background: #5a6268;\n          }\n\n          &:disabled {\n            background: #ccc;\n            cursor: not-allowed;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": "AA0GA,eAAe;EACbA,IAAI,EAAE,mBAAmB;EAEzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,eAAe,EAAE,EAAE;MACnBC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;EAC3B,CAAC;EAEDC,OAAO,EAAE;IACP,MAAMD,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF;QACA,MAAMP,QAAO,GAAI,MAAM,IAAI,CAACS,MAAM,CAACC,QAAQ,CAAC,oBAAoB,EAAE;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;QACtF,IAAI,CAACX,QAAO,GAAIA,QAAQ,CAACY,MAAM,CAACC,OAAM,IAAKA,OAAO,CAACC,OAAO,CAAC;MAC7D,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACN,MAAM,CAACC,QAAQ,CAAC,aAAa,EAAE;UAClCO,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAMC,eAAeA,CAAA,EAAG;MACtB,IAAI,CAAC,IAAI,CAACrB,eAAe,EAAE;QACzB,IAAI,CAACG,UAAS,GAAI,EAAE;QACpB,IAAI,CAACC,QAAO,GAAI,CAAC;QACjB,IAAI,CAACC,WAAU,GAAI,CAAC;QACpB;MACF;MAEA,IAAI;QACF,MAAM,IAAI,CAACiB,cAAc,CAAC,CAAC;QAC3B,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;MAC3B,EAAE,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAED,MAAMK,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF;QACA,MAAMnB,UAAS,GAAI,MAAM,IAAI,CAACQ,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAE;UAC/DC,IAAI,EAAE,WAAW;UACjBW,SAAS,EAAE,IAAI,CAACxB;QAClB,CAAC,CAAC;QACF,IAAI,CAACG,UAAS,GAAIA,UAAU,CAACsB,GAAG,CAACC,EAAC,IAAKA,EAAE,CAACC,QAAQ,CAAC7B,IAAI,CAAC;MAC1D,EAAE,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IACF,CAAC;IAED,MAAMM,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF;QACA,MAAMK,IAAG,GAAI,MAAM,IAAI,CAACjB,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAE;UACzDC,IAAI,EAAE,KAAK;UACXW,SAAS,EAAE,IAAI,CAACxB;QAClB,CAAC,CAAC;QAEF,IAAI,CAACI,QAAO,GAAIwB,IAAI,CAACC,MAAM;QAC3B,IAAI,CAACxB,WAAU,GAAIuB,IAAI,CAACd,MAAM,CAACgB,GAAE,IAAKA,GAAG,CAACC,MAAM,EAAEC,KAAI,KAAM,SAAS,CAAC,CAACH,MAAM;MAC/E,EAAE,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAED,MAAMgB,aAAaA,CAAA,EAAG;MACpB,IAAI,CAAC3B,WAAU,GAAI,IAAI;MAEvB,IAAI;QACF;QACA,MAAM,IAAI,CAAC4B,cAAc,CAAC,CAAC;QAE3B,IAAI,CAACvB,MAAM,CAACC,QAAQ,CAAC,eAAe,EAAE;UACpCO,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE;QACX,CAAC,CAAC;;QAEF;QACA,IAAI,CAACe,OAAO,CAACC,IAAI,CAAC;UAChBtC,IAAI,EAAE,yBAAyB;UAC/BuC,MAAM,EAAE;YAAEtB,OAAO,EAAE,IAAI,CAACf;UAAgB;QAC1C,CAAC,CAAC;MAEJ,EAAE,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACN,MAAM,CAACC,QAAQ,CAAC,aAAa,EAAE;UAClCO,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,6BAA4B,GAAIH,KAAK,CAACG;QACjD,CAAC,CAAC;MACJ,UAAU;QACR,IAAI,CAACd,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IAED,MAAM4B,cAAcA,CAAA,EAAG;MACrB;MACA;MACA,MAAM,IAAII,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD,IAAI,CAAChC,gBAAe,GAAI,IAAI;IAC9B,CAAC;IAED,MAAMkC,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF;QACA,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC;QAE3B,IAAI,CAACnC,gBAAe,GAAI,KAAK;QAE7B,IAAI,CAACI,MAAM,CAACC,QAAQ,CAAC,eAAe,EAAE;UACpCO,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE;QACX,CAAC,CAAC;MAEJ,EAAE,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACN,MAAM,CAACC,QAAQ,CAAC,aAAa,EAAE;UAClCO,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,2BAA0B,GAAIH,KAAK,CAACG;QAC/C,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAMsB,cAAcA,CAAA,EAAG;MACrB;MACA,MAAM,IAAIJ,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACzD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}