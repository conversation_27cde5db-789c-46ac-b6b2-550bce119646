{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue", "mtime": 1748284942021}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": 1747923102829}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1747923095351}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js", "mtime": 1747923098915}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnS3ViZURvb21HYW1lJywKICAKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY29ubmVjdGVkOiBmYWxzZSwKICAgICAgY29ubmVjdGlvblN0YXR1czogJ2Nvbm5lY3RpbmcnLAogICAgICBjb25uZWN0aW9uTWVzc2FnZTogJ0Nvbm5lY3RpbmcgdG8gS3ViZURvb20uLi4nLAogICAgICB2bmNDbGllbnQ6IG51bGwsCiAgICAgIGNsdXN0ZXJOYW1lOiAnJywKICAgICAgcG9kc0tpbGxlZDogMCwKICAgICAgcG9kc1JlbWFpbmluZzogMCwKICAgICAgcG9kc1Jlc3Bhd25lZDogMCwKICAgICAgcmVjZW50S2lsbHM6IFtdLAogICAgICBjbHVzdGVySGVhbHRoOiB7CiAgICAgICAgc3RhdHVzOiAnaGVhbHRoeScsCiAgICAgICAgaWNvbjogJ2ljb24tY2hlY2ttYXJrJywKICAgICAgICBtZXNzYWdlOiAnQ2x1c3RlciBpcyBoZWFsdGh5JwogICAgICB9LAogICAgICBtb25pdG9yaW5nSW50ZXJ2YWw6IG51bGwsCiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICBzdGF0dXNJY29uKCkgewogICAgICBzd2l0Y2ggKHRoaXMuY29ubmVjdGlvblN0YXR1cykgewogICAgICAgIGNhc2UgJ2Nvbm5lY3RlZCc6IHJldHVybiAnaWNvbi1jaGVja21hcmsnOwogICAgICAgIGNhc2UgJ2Nvbm5lY3RpbmcnOiByZXR1cm4gJ2ljb24tc3Bpbm5lcic7CiAgICAgICAgY2FzZSAnZXJyb3InOiByZXR1cm4gJ2ljb24tZXJyb3InOwogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnaWNvbi1pbmZvJzsKICAgICAgfQogICAgfSwKCiAgICBzdGF0dXNUZXh0KCkgewogICAgICBzd2l0Y2ggKHRoaXMuY29ubmVjdGlvblN0YXR1cykgewogICAgICAgIGNhc2UgJ2Nvbm5lY3RlZCc6IHJldHVybiAnQ29ubmVjdGVkIHRvIEt1YmVEb29tJzsKICAgICAgICBjYXNlICdjb25uZWN0aW5nJzogcmV0dXJuICdDb25uZWN0aW5nLi4uJzsKICAgICAgICBjYXNlICdlcnJvcic6IHJldHVybiAnQ29ubmVjdGlvbiBmYWlsZWQnOwogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnVW5rbm93biBzdGF0dXMnOwogICAgICB9CiAgICB9CiAgfSwKCiAgYXN5bmMgbW91bnRlZCgpIHsKICAgIHRoaXMuY2x1c3Rlck5hbWUgPSB0aGlzLiRyb3V0ZS5wYXJhbXMuY2x1c3RlciB8fCAnVW5rbm93bic7CiAgICBhd2FpdCB0aGlzLmluaXRpYWxpemVWTkMoKTsKICAgIHRoaXMuc3RhcnRNb25pdG9yaW5nKCk7CiAgfSwKCiAgYmVmb3JlVW5tb3VudCgpIHsKICAgIHRoaXMuY2xlYW51cCgpOwogIH0sCgogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGluaXRpYWxpemVWTkMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5jb25uZWN0aW9uTWVzc2FnZSA9ICdJbml0aWFsaXppbmcgVk5DIGNvbm5lY3Rpb24uLi4nOwogICAgICAgIAogICAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjb25uZWN0IHRvIHRoZSBLdWJlRG9vbSBWTkMgc2VydmVyCiAgICAgICAgLy8gRm9yIG5vdywgd2UnbGwgc2ltdWxhdGUgdGhlIGNvbm5lY3Rpb24KICAgICAgICBhd2FpdCB0aGlzLnNpbXVsYXRlVk5DQ29ubmVjdGlvbigpOwogICAgICAgIAogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbml0aWFsaXplIFZOQzonLCBlcnJvcik7CiAgICAgICAgdGhpcy5jb25uZWN0aW9uU3RhdHVzID0gJ2Vycm9yJzsKICAgICAgICB0aGlzLmNvbm5lY3Rpb25NZXNzYWdlID0gJ0ZhaWxlZCB0byBjb25uZWN0IHRvIEt1YmVEb29tJzsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBzaW11bGF0ZVZOQ0Nvbm5lY3Rpb24oKSB7CiAgICAgIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gZGVsYXkKICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKTsKICAgICAgCiAgICAgIHRoaXMuY29ubmVjdGVkID0gdHJ1ZTsKICAgICAgdGhpcy5jb25uZWN0aW9uU3RhdHVzID0gJ2Nvbm5lY3RlZCc7CiAgICAgIAogICAgICAvLyBJbml0aWFsaXplIGNhbnZhcyBmb3IgZ2FtZSBkaXNwbGF5CiAgICAgIHRoaXMuaW5pdGlhbGl6ZUNhbnZhcygpOwogICAgfSwKCiAgICBpbml0aWFsaXplQ2FudmFzKCkgewogICAgICBjb25zdCBjYW52YXMgPSB0aGlzLiRyZWZzLnZuY0NhbnZhczsKICAgICAgaWYgKCFjYW52YXMpIHJldHVybjsKCiAgICAgIGNhbnZhcy53aWR0aCA9IDgwMDsKICAgICAgY2FudmFzLmhlaWdodCA9IDYwMDsKICAgICAgCiAgICAgIGNvbnN0IGN0eCA9IGNhbnZhcy5nZXRDb250ZXh0KCcyZCcpOwogICAgICAKICAgICAgLy8gRHJhdyBhIHBsYWNlaG9sZGVyIGdhbWUgc2NyZWVuCiAgICAgIGN0eC5maWxsU3R5bGUgPSAnIzAwMCc7CiAgICAgIGN0eC5maWxsUmVjdCgwLCAwLCBjYW52YXMud2lkdGgsIGNhbnZhcy5oZWlnaHQpOwogICAgICAKICAgICAgY3R4LmZpbGxTdHlsZSA9ICcjZmYwMDAwJzsKICAgICAgY3R4LmZvbnQgPSAnMjRweCBBcmlhbCc7CiAgICAgIGN0eC50ZXh0QWxpZ24gPSAnY2VudGVyJzsKICAgICAgY3R4LmZpbGxUZXh0KCdLdWJlRG9vbSBHYW1lIFNjcmVlbicsIGNhbnZhcy53aWR0aCAvIDIsIGNhbnZhcy5oZWlnaHQgLyAyKTsKICAgICAgY3R4LmZpbGxUZXh0KCcoVk5DIENvbm5lY3Rpb24gUGxhY2Vob2xkZXIpJywgY2FudmFzLndpZHRoIC8gMiwgY2FudmFzLmhlaWdodCAvIDIgKyAzMCk7CiAgICAgIAogICAgICAvLyBBZGQgY2xpY2sgaGFuZGxlciBmb3IgaW50ZXJhY3Rpb24KICAgICAgY2FudmFzLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5oYW5kbGVDYW52YXNDbGljayk7CiAgICB9LAoKICAgIGhhbmRsZUNhbnZhc0NsaWNrKCkgewogICAgICAvLyBTaW11bGF0ZSBwb2Qga2lsbCB3aGVuIGNsaWNraW5nIG9uIHRoZSBnYW1lCiAgICAgIHRoaXMuc2ltdWxhdGVQb2RLaWxsKCk7CiAgICB9LAoKICAgIHNpbXVsYXRlUG9kS2lsbCgpIHsKICAgICAgY29uc3QgcG9kTmFtZXMgPSBbCiAgICAgICAgJ25naW54LWRlcGxveW1lbnQtYWJjMTIzJywKICAgICAgICAncmVkaXMtbWFzdGVyLWRlZjQ1NicsCiAgICAgICAgJ3Bvc3RncmVzLWdoaTc4OScsCiAgICAgICAgJ2FwaS1zZXJ2ZXItamtsMDEyJywKICAgICAgICAnd29ya2VyLW1ubzM0NScKICAgICAgXTsKICAgICAgCiAgICAgIGNvbnN0IG5hbWVzcGFjZXMgPSBbJ2RlZmF1bHQnLCAna3ViZS1zeXN0ZW0nLCAnbW9uaXRvcmluZycsICdpbmdyZXNzJ107CiAgICAgIAogICAgICBjb25zdCBraWxsID0gewogICAgICAgIGlkOiBEYXRlLm5vdygpLAogICAgICAgIHBvZE5hbWU6IHBvZE5hbWVzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHBvZE5hbWVzLmxlbmd0aCldLAogICAgICAgIG5hbWVzcGFjZTogbmFtZXNwYWNlc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBuYW1lc3BhY2VzLmxlbmd0aCldLAogICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKQogICAgICB9OwogICAgICAKICAgICAgdGhpcy5yZWNlbnRLaWxscy51bnNoaWZ0KGtpbGwpOwogICAgICBpZiAodGhpcy5yZWNlbnRLaWxscy5sZW5ndGggPiAxMCkgewogICAgICAgIHRoaXMucmVjZW50S2lsbHMucG9wKCk7CiAgICAgIH0KICAgICAgCiAgICAgIHRoaXMucG9kc0tpbGxlZCsrOwogICAgICB0aGlzLnBvZHNSZW1haW5pbmcgPSBNYXRoLm1heCgwLCB0aGlzLnBvZHNSZW1haW5pbmcgLSAxKTsKICAgICAgCiAgICAgIC8vIFVwZGF0ZSBjbHVzdGVyIGhlYWx0aCBiYXNlZCBvbiBraWxscwogICAgICB0aGlzLnVwZGF0ZUNsdXN0ZXJIZWFsdGgoKTsKICAgIH0sCgogICAgdXBkYXRlQ2x1c3RlckhlYWx0aCgpIHsKICAgICAgaWYgKHRoaXMucG9kc0tpbGxlZCA+IDIwKSB7CiAgICAgICAgdGhpcy5jbHVzdGVySGVhbHRoID0gewogICAgICAgICAgc3RhdHVzOiAnY3JpdGljYWwnLAogICAgICAgICAgaWNvbjogJ2ljb24tZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ0NsdXN0ZXIgdW5kZXIgaGVhdnkgc3RyZXNzIScKICAgICAgICB9OwogICAgICB9IGVsc2UgaWYgKHRoaXMucG9kc0tpbGxlZCA+IDEwKSB7CiAgICAgICAgdGhpcy5jbHVzdGVySGVhbHRoID0gewogICAgICAgICAgc3RhdHVzOiAnd2FybmluZycsCiAgICAgICAgICBpY29uOiAnaWNvbi13YXJuaW5nJywKICAgICAgICAgIG1lc3NhZ2U6ICdDbHVzdGVyIGV4cGVyaWVuY2luZyBzb21lIHN0cmVzcycKICAgICAgICB9OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY2x1c3RlckhlYWx0aCA9IHsKICAgICAgICAgIHN0YXR1czogJ2hlYWx0aHknLAogICAgICAgICAgaWNvbjogJ2ljb24tY2hlY2ttYXJrJywKICAgICAgICAgIG1lc3NhZ2U6ICdDbHVzdGVyIGlzIGhlYWx0aHknCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKCiAgICBzZW5kQ2hlYXQoY2hlYXRDb2RlKSB7CiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBzZW5kIHRoZSBjaGVhdCB0byB0aGUgVk5DIHNlc3Npb24KICAgICAgY29uc29sZS5sb2coJ1NlbmRpbmcgY2hlYXQ6JywgY2hlYXRDb2RlKTsKICAgICAgCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdncm93bC9pbmZvJywgewogICAgICAgIHRpdGxlOiAnQ2hlYXQgQWN0aXZhdGVkJywKICAgICAgICBtZXNzYWdlOiBgQ2hlYXQgY29kZSAiJHtjaGVhdENvZGV9IiBzZW50IHRvIGdhbWVgCiAgICAgIH0pOwogICAgfSwKCiAgICBzdGFydE1vbml0b3JpbmcoKSB7CiAgICAgIHRoaXMucG9kc1JlbWFpbmluZyA9IDUwOyAvLyBJbml0aWFsIHBvZCBjb3VudAogICAgICAKICAgICAgdGhpcy5tb25pdG9yaW5nSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgLy8gU2ltdWxhdGUgcG9kIHJlc3Bhd25pbmcKICAgICAgICBpZiAoTWF0aC5yYW5kb20oKSA8IDAuMSAmJiB0aGlzLnBvZHNSZW1haW5pbmcgPCA1MCkgewogICAgICAgICAgdGhpcy5wb2RzUmVtYWluaW5nKys7CiAgICAgICAgICB0aGlzLnBvZHNSZXNwYXduZWQrKzsKICAgICAgICB9CiAgICAgIH0sIDUwMDApOwogICAgfSwKCiAgICBmb3JtYXRUaW1lKHRpbWVzdGFtcCkgewogICAgICByZXR1cm4gdGltZXN0YW1wLnRvTG9jYWxlVGltZVN0cmluZygpOwogICAgfSwKCiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAna3ViZWRvb20tYy1jbHVzdGVyLWRhc2hib2FyZCcsCiAgICAgICAgcGFyYW1zOiB7IGNsdXN0ZXI6IHRoaXMuJHJvdXRlLnBhcmFtcy5jbHVzdGVyIH0KICAgICAgfSk7CiAgICB9LAoKICAgIGFzeW5jIGVtZXJnZW5jeVN0b3AoKSB7CiAgICAgIGlmIChjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gc3RvcCBLdWJlRG9vbT8gVGhpcyB3aWxsIGVuZCB0aGUgZ2FtZSBzZXNzaW9uLicpKSB7CiAgICAgICAgYXdhaXQgdGhpcy5jbGVhbnVwKCk7CiAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgICAgfQogICAgfSwKCiAgICBjbGVhbnVwKCkgewogICAgICBpZiAodGhpcy5tb25pdG9yaW5nSW50ZXJ2YWwpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMubW9uaXRvcmluZ0ludGVydmFsKTsKICAgICAgfQogICAgICAKICAgICAgaWYgKHRoaXMudm5jQ2xpZW50KSB7CiAgICAgICAgLy8gRGlzY29ubmVjdCBWTkMgY2xpZW50CiAgICAgICAgdGhpcy52bmNDbGllbnQgPSBudWxsOwogICAgICB9CiAgICAgIAogICAgICBjb25zdCBjYW52YXMgPSB0aGlzLiRyZWZzLnZuY0NhbnZhczsKICAgICAgaWYgKGNhbnZhcykgewogICAgICAgIGNhbnZhcy5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIHRoaXMuaGFuZGxlQ2FudmFzQ2xpY2spOwogICAgICB9CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue"], "names": [], "mappings": ";AA6HA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzD,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEtF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAExD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;MACH,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC;MACH,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;MACH;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChD,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"kubedoom-game\">\n    <div class=\"game-header\">\n      <div class=\"header-left\">\n        <button class=\"btn btn-secondary\" @click=\"goBack\">\n          <i class=\"icon icon-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n      \n      <div class=\"header-center\">\n        <h2>KubeDoom - {{ clusterName }}</h2>\n        <div class=\"status\" :class=\"connectionStatus\">\n          <i class=\"icon\" :class=\"statusIcon\"></i>\n          {{ statusText }}\n        </div>\n      </div>\n      \n      <div class=\"header-right\">\n        <button class=\"btn btn-danger\" @click=\"emergencyStop\">\n          <i class=\"icon icon-stop\"></i>\n          Emergency Stop\n        </button>\n      </div>\n    </div>\n\n    <div class=\"game-content\">\n      <div class=\"game-area\">\n        <div class=\"vnc-container\" ref=\"vncContainer\">\n          <div v-if=\"!connected\" class=\"connection-overlay\">\n            <div class=\"spinner\"></div>\n            <p>{{ connectionMessage }}</p>\n          </div>\n          <canvas ref=\"vncCanvas\" v-show=\"connected\"></canvas>\n        </div>\n        \n        <div class=\"game-controls\">\n          <div class=\"control-group\">\n            <h4>Game Controls</h4>\n            <div class=\"controls-grid\">\n              <div class=\"control-item\">\n                <span class=\"key\">WASD</span>\n                <span class=\"desc\">Move</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Mouse</span>\n                <span class=\"desc\">Look around</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Ctrl</span>\n                <span class=\"desc\">Fire</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Space</span>\n                <span class=\"desc\">Open doors</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">ESC</span>\n                <span class=\"desc\">Pause</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"control-group\">\n            <h4>Cheats</h4>\n            <div class=\"cheat-buttons\">\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idkfa')\">\n                All Weapons\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('iddqd')\">\n                God Mode\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idspispopd')\">\n                No Clip\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"pod-monitor\">\n        <h3>Pod Monitor</h3>\n        <div class=\"monitor-stats\">\n          <div class=\"stat\">\n            <span class=\"label\">Pods Killed:</span>\n            <span class=\"value killed\">{{ podsKilled }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Remaining:</span>\n            <span class=\"value remaining\">{{ podsRemaining }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Respawned:</span>\n            <span class=\"value respawned\">{{ podsRespawned }}</span>\n          </div>\n        </div>\n\n        <div class=\"recent-kills\" v-if=\"recentKills.length > 0\">\n          <h4>Recent Pod Kills</h4>\n          <div class=\"kill-list\">\n            <div \n              v-for=\"kill in recentKills\" \n              :key=\"kill.id\"\n              class=\"kill-item\"\n            >\n              <span class=\"pod-name\">{{ kill.podName }}</span>\n              <span class=\"namespace\">{{ kill.namespace }}</span>\n              <span class=\"time\">{{ formatTime(kill.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"cluster-health\">\n          <h4>Cluster Health</h4>\n          <div class=\"health-indicator\" :class=\"clusterHealth.status\">\n            <i class=\"icon\" :class=\"clusterHealth.icon\"></i>\n            {{ clusterHealth.message }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomGame',\n  \n  data() {\n    return {\n      connected: false,\n      connectionStatus: 'connecting',\n      connectionMessage: 'Connecting to KubeDoom...',\n      vncClient: null,\n      clusterName: '',\n      podsKilled: 0,\n      podsRemaining: 0,\n      podsRespawned: 0,\n      recentKills: [],\n      clusterHealth: {\n        status: 'healthy',\n        icon: 'icon-checkmark',\n        message: 'Cluster is healthy'\n      },\n      monitoringInterval: null,\n    };\n  },\n\n  computed: {\n    statusIcon() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'icon-checkmark';\n        case 'connecting': return 'icon-spinner';\n        case 'error': return 'icon-error';\n        default: return 'icon-info';\n      }\n    },\n\n    statusText() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'Connected to KubeDoom';\n        case 'connecting': return 'Connecting...';\n        case 'error': return 'Connection failed';\n        default: return 'Unknown status';\n      }\n    }\n  },\n\n  async mounted() {\n    this.clusterName = this.$route.params.cluster || 'Unknown';\n    await this.initializeVNC();\n    this.startMonitoring();\n  },\n\n  beforeUnmount() {\n    this.cleanup();\n  },\n\n  methods: {\n    async initializeVNC() {\n      try {\n        this.connectionMessage = 'Initializing VNC connection...';\n        \n        // In a real implementation, this would connect to the KubeDoom VNC server\n        // For now, we'll simulate the connection\n        await this.simulateVNCConnection();\n        \n      } catch (error) {\n        console.error('Failed to initialize VNC:', error);\n        this.connectionStatus = 'error';\n        this.connectionMessage = 'Failed to connect to KubeDoom';\n      }\n    },\n\n    async simulateVNCConnection() {\n      // Simulate connection delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      this.connected = true;\n      this.connectionStatus = 'connected';\n      \n      // Initialize canvas for game display\n      this.initializeCanvas();\n    },\n\n    initializeCanvas() {\n      const canvas = this.$refs.vncCanvas;\n      if (!canvas) return;\n\n      canvas.width = 800;\n      canvas.height = 600;\n      \n      const ctx = canvas.getContext('2d');\n      \n      // Draw a placeholder game screen\n      ctx.fillStyle = '#000';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      \n      ctx.fillStyle = '#ff0000';\n      ctx.font = '24px Arial';\n      ctx.textAlign = 'center';\n      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);\n      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);\n      \n      // Add click handler for interaction\n      canvas.addEventListener('click', this.handleCanvasClick);\n    },\n\n    handleCanvasClick() {\n      // Simulate pod kill when clicking on the game\n      this.simulatePodKill();\n    },\n\n    simulatePodKill() {\n      const podNames = [\n        'nginx-deployment-abc123',\n        'redis-master-def456',\n        'postgres-ghi789',\n        'api-server-jkl012',\n        'worker-mno345'\n      ];\n      \n      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];\n      \n      const kill = {\n        id: Date.now(),\n        podName: podNames[Math.floor(Math.random() * podNames.length)],\n        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],\n        timestamp: new Date()\n      };\n      \n      this.recentKills.unshift(kill);\n      if (this.recentKills.length > 10) {\n        this.recentKills.pop();\n      }\n      \n      this.podsKilled++;\n      this.podsRemaining = Math.max(0, this.podsRemaining - 1);\n      \n      // Update cluster health based on kills\n      this.updateClusterHealth();\n    },\n\n    updateClusterHealth() {\n      if (this.podsKilled > 20) {\n        this.clusterHealth = {\n          status: 'critical',\n          icon: 'icon-error',\n          message: 'Cluster under heavy stress!'\n        };\n      } else if (this.podsKilled > 10) {\n        this.clusterHealth = {\n          status: 'warning',\n          icon: 'icon-warning',\n          message: 'Cluster experiencing some stress'\n        };\n      } else {\n        this.clusterHealth = {\n          status: 'healthy',\n          icon: 'icon-checkmark',\n          message: 'Cluster is healthy'\n        };\n      }\n    },\n\n    sendCheat(cheatCode) {\n      // In a real implementation, this would send the cheat to the VNC session\n      console.log('Sending cheat:', cheatCode);\n      \n      this.$store.dispatch('growl/info', {\n        title: 'Cheat Activated',\n        message: `Cheat code \"${cheatCode}\" sent to game`\n      });\n    },\n\n    startMonitoring() {\n      this.podsRemaining = 50; // Initial pod count\n      \n      this.monitoringInterval = setInterval(() => {\n        // Simulate pod respawning\n        if (Math.random() < 0.1 && this.podsRemaining < 50) {\n          this.podsRemaining++;\n          this.podsRespawned++;\n        }\n      }, 5000);\n    },\n\n    formatTime(timestamp) {\n      return timestamp.toLocaleTimeString();\n    },\n\n    goBack() {\n      this.$router.push({\n        name: 'kubedoom-c-cluster-dashboard',\n        params: { cluster: this.$route.params.cluster }\n      });\n    },\n\n    async emergencyStop() {\n      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {\n        await this.cleanup();\n        this.goBack();\n      }\n    },\n\n    cleanup() {\n      if (this.monitoringInterval) {\n        clearInterval(this.monitoringInterval);\n      }\n      \n      if (this.vncClient) {\n        // Disconnect VNC client\n        this.vncClient = null;\n      }\n      \n      const canvas = this.$refs.vncCanvas;\n      if (canvas) {\n        canvas.removeEventListener('click', this.handleCanvasClick);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-game {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #1a1a1a;\n  color: white;\n\n  .game-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 20px;\n    background: #2d2d2d;\n    border-bottom: 1px solid #444;\n\n    .header-center {\n      text-align: center;\n\n      h2 {\n        margin: 0 0 5px 0;\n        color: #ff6b6b;\n      }\n\n      .status {\n        font-size: 14px;\n        \n        &.connected { color: #28a745; }\n        &.connecting { color: #ffc107; }\n        &.error { color: #dc3545; }\n\n        .icon {\n          margin-right: 5px;\n        }\n      }\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-weight: 500;\n\n      &.btn-secondary {\n        background: #6c757d;\n        color: white;\n      }\n\n      &.btn-danger {\n        background: #dc3545;\n        color: white;\n      }\n\n      .icon {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  .game-content {\n    flex: 1;\n    display: grid;\n    grid-template-columns: 1fr 300px;\n    gap: 20px;\n    padding: 20px;\n\n    .game-area {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n\n      .vnc-container {\n        position: relative;\n        background: #000;\n        border: 2px solid #444;\n        border-radius: 8px;\n        overflow: hidden;\n        height: 600px;\n\n        .connection-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          background: rgba(0, 0, 0, 0.8);\n          z-index: 10;\n\n          .spinner {\n            width: 40px;\n            height: 40px;\n            border: 4px solid #333;\n            border-top: 4px solid #ff6b6b;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-bottom: 20px;\n          }\n\n          p {\n            color: #ccc;\n            font-size: 16px;\n          }\n        }\n\n        canvas {\n          width: 100%;\n          height: 100%;\n          cursor: crosshair;\n        }\n      }\n\n      .game-controls {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 20px;\n\n        .control-group {\n          background: #2d2d2d;\n          padding: 15px;\n          border-radius: 8px;\n\n          h4 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n          }\n\n          .controls-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 10px;\n\n            .control-item {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n              padding: 5px 0;\n\n              .key {\n                background: #444;\n                padding: 2px 8px;\n                border-radius: 4px;\n                font-family: monospace;\n                font-size: 12px;\n              }\n\n              .desc {\n                font-size: 12px;\n                color: #ccc;\n              }\n            }\n          }\n\n          .cheat-buttons {\n            display: flex;\n            flex-direction: column;\n            gap: 8px;\n\n            .btn {\n              padding: 6px 12px;\n              background: #444;\n              color: white;\n              border: none;\n              border-radius: 4px;\n              cursor: pointer;\n              font-size: 12px;\n\n              &:hover {\n                background: #555;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .pod-monitor {\n      background: #2d2d2d;\n      padding: 20px;\n      border-radius: 8px;\n      height: fit-content;\n\n      h3 {\n        margin: 0 0 20px 0;\n        color: #ff6b6b;\n      }\n\n      .monitor-stats {\n        margin-bottom: 25px;\n\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n\n          .label {\n            color: #ccc;\n          }\n\n          .value {\n            font-weight: 600;\n\n            &.killed { color: #dc3545; }\n            &.remaining { color: #28a745; }\n            &.respawned { color: #17a2b8; }\n          }\n        }\n      }\n\n      .recent-kills {\n        margin-bottom: 25px;\n\n        h4 {\n          margin: 0 0 15px 0;\n          color: #ffc107;\n        }\n\n        .kill-list {\n          max-height: 200px;\n          overflow-y: auto;\n\n          .kill-item {\n            display: flex;\n            flex-direction: column;\n            padding: 8px;\n            margin-bottom: 8px;\n            background: #1a1a1a;\n            border-radius: 4px;\n            font-size: 12px;\n\n            .pod-name {\n              font-weight: 600;\n              color: #ff6b6b;\n            }\n\n            .namespace {\n              color: #17a2b8;\n            }\n\n            .time {\n              color: #6c757d;\n            }\n          }\n        }\n      }\n\n      .cluster-health {\n        h4 {\n          margin: 0 0 15px 0;\n          color: #28a745;\n        }\n\n        .health-indicator {\n          padding: 10px;\n          border-radius: 4px;\n          text-align: center;\n          font-weight: 500;\n\n          &.healthy {\n            background: rgba(40, 167, 69, 0.2);\n            color: #28a745;\n          }\n\n          &.warning {\n            background: rgba(255, 193, 7, 0.2);\n            color: #ffc107;\n          }\n\n          &.critical {\n            background: rgba(220, 53, 69, 0.2);\n            color: #dc3545;\n          }\n\n          .icon {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n</style>\n"]}]}