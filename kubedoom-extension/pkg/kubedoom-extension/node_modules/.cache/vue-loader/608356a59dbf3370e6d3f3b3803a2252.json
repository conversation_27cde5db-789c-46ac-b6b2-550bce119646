{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/Dashboard.vue?vue&type=template&id=471e0416&scoped=true", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/Dashboard.vue", "mtime": 1748284877191}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": 1747923102829}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1747923098915}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1747923095351}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js", "mtime": 1747923098915}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/Dashboard.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACtH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/Dashboard.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"kubedoom-dashboard\">\n    <div class=\"header\">\n      <h1 class=\"title\">\n        <i class=\"icon icon-kubedoom\"></i>\n        KubeDoom\n      </h1>\n      <p class=\"subtitle\">\n        Kill Kubernetes pods by playing Doom!\n      </p>\n    </div>\n\n    <div class=\"content\">\n      <div class=\"info-section\">\n        <div class=\"info-card\">\n          <h3>What is KubeDoom?</h3>\n          <p>\n            KubeDoom is a fun way to test the resilience of your Kubernetes cluster by playing the classic game Doom. \n            Each enemy in the game represents a pod in your cluster, and killing them will actually delete the corresponding pod.\n          </p>\n        </div>\n\n        <div class=\"info-card\">\n          <h3>How it works</h3>\n          <ul>\n            <li>Each pod in your cluster appears as an enemy in Doom</li>\n            <li>Shooting enemies kills the corresponding pods</li>\n            <li>Watch how your applications handle pod failures</li>\n            <li>Test your cluster's resilience and recovery mechanisms</li>\n          </ul>\n        </div>\n\n        <div class=\"info-card\">\n          <h3>Safety Notice</h3>\n          <div class=\"warning\">\n            <i class=\"icon icon-warning\"></i>\n            <strong>Warning:</strong> This will actually delete pods in your cluster. \n            Only use this in development or testing environments!\n          </div>\n        </div>\n      </div>\n\n      <div class=\"actions\">\n        <div class=\"cluster-selector\">\n          <label>Select Cluster:</label>\n          <select v-model=\"selectedCluster\" @change=\"onClusterChange\">\n            <option value=\"\">Choose a cluster...</option>\n            <option v-for=\"cluster in clusters\" :key=\"cluster.id\" :value=\"cluster.id\">\n              {{ cluster.nameDisplay }}\n            </option>\n          </select>\n        </div>\n\n        <div class=\"namespace-selector\" v-if=\"selectedCluster\">\n          <label>Target Namespace (optional):</label>\n          <select v-model=\"selectedNamespace\">\n            <option value=\"\">All namespaces</option>\n            <option v-for=\"namespace in namespaces\" :key=\"namespace\" :value=\"namespace\">\n              {{ namespace }}\n            </option>\n          </select>\n        </div>\n\n        <div class=\"pod-info\" v-if=\"selectedCluster\">\n          <h4>Cluster Status</h4>\n          <div class=\"stats\">\n            <div class=\"stat\">\n              <span class=\"label\">Total Pods:</span>\n              <span class=\"value\">{{ podCount }}</span>\n            </div>\n            <div class=\"stat\">\n              <span class=\"label\">Running Pods:</span>\n              <span class=\"value\">{{ runningPods }}</span>\n            </div>\n            <div class=\"stat\">\n              <span class=\"label\">Target Namespace:</span>\n              <span class=\"value\">{{ selectedNamespace || 'All' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"game-controls\">\n          <button \n            class=\"btn btn-primary btn-lg\"\n            :disabled=\"!selectedCluster || isDeploying\"\n            @click=\"startKubeDoom\"\n          >\n            <i class=\"icon icon-play\"></i>\n            {{ isDeploying ? 'Deploying KubeDoom...' : 'Start KubeDoom' }}\n          </button>\n          \n          <button \n            class=\"btn btn-secondary\"\n            :disabled=\"!kubeDoomDeployed\"\n            @click=\"stopKubeDoom\"\n          >\n            <i class=\"icon icon-stop\"></i>\n            Stop KubeDoom\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomDashboard',\n  \n  data() {\n    return {\n      selectedCluster: '',\n      selectedNamespace: '',\n      clusters: [],\n      namespaces: [],\n      podCount: 0,\n      runningPods: 0,\n      isDeploying: false,\n      kubeDoomDeployed: false,\n    };\n  },\n\n  async mounted() {\n    await this.loadClusters();\n  },\n\n  methods: {\n    async loadClusters() {\n      try {\n        // Load clusters from Rancher API\n        const clusters = await this.$store.dispatch('management/findAll', { type: 'cluster' });\n        this.clusters = clusters.filter(cluster => cluster.isReady);\n      } catch (error) {\n        console.error('Failed to load clusters:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to load clusters'\n        });\n      }\n    },\n\n    async onClusterChange() {\n      if (!this.selectedCluster) {\n        this.namespaces = [];\n        this.podCount = 0;\n        this.runningPods = 0;\n        return;\n      }\n\n      try {\n        await this.loadNamespaces();\n        await this.loadPodStats();\n      } catch (error) {\n        console.error('Failed to load cluster data:', error);\n      }\n    },\n\n    async loadNamespaces() {\n      try {\n        // Load namespaces for the selected cluster\n        const namespaces = await this.$store.dispatch('cluster/findAll', { \n          type: 'namespace',\n          clusterId: this.selectedCluster \n        });\n        this.namespaces = namespaces.map(ns => ns.metadata.name);\n      } catch (error) {\n        console.error('Failed to load namespaces:', error);\n      }\n    },\n\n    async loadPodStats() {\n      try {\n        // Load pod statistics for the selected cluster\n        const pods = await this.$store.dispatch('cluster/findAll', { \n          type: 'pod',\n          clusterId: this.selectedCluster \n        });\n        \n        this.podCount = pods.length;\n        this.runningPods = pods.filter(pod => pod.status?.phase === 'Running').length;\n      } catch (error) {\n        console.error('Failed to load pod stats:', error);\n      }\n    },\n\n    async startKubeDoom() {\n      this.isDeploying = true;\n      \n      try {\n        // Deploy KubeDoom to the selected cluster\n        await this.deployKubeDoom();\n        \n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom deployed successfully!'\n        });\n        \n        // Navigate to the game page\n        this.$router.push({\n          name: 'kubedoom-c-cluster-game',\n          params: { cluster: this.selectedCluster }\n        });\n        \n      } catch (error) {\n        console.error('Failed to deploy KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to deploy KubeDoom: ' + error.message\n        });\n      } finally {\n        this.isDeploying = false;\n      }\n    },\n\n    async deployKubeDoom() {\n      // This would deploy the KubeDoom pod to the cluster\n      // For now, we'll simulate the deployment\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      this.kubeDoomDeployed = true;\n    },\n\n    async stopKubeDoom() {\n      try {\n        // Stop and remove KubeDoom deployment\n        await this.removeKubeDoom();\n        \n        this.kubeDoomDeployed = false;\n        \n        this.$store.dispatch('growl/success', {\n          title: 'Success',\n          message: 'KubeDoom stopped successfully!'\n        });\n        \n      } catch (error) {\n        console.error('Failed to stop KubeDoom:', error);\n        this.$store.dispatch('growl/error', {\n          title: 'Error',\n          message: 'Failed to stop KubeDoom: ' + error.message\n        });\n      }\n    },\n\n    async removeKubeDoom() {\n      // This would remove the KubeDoom deployment\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-dashboard {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n\n  .header {\n    text-align: center;\n    margin-bottom: 40px;\n\n    .title {\n      font-size: 3rem;\n      color: #ff6b6b;\n      margin-bottom: 10px;\n      \n      .icon {\n        margin-right: 15px;\n      }\n    }\n\n    .subtitle {\n      font-size: 1.2rem;\n      color: #666;\n    }\n  }\n\n  .content {\n    display: grid;\n    grid-template-columns: 1fr 400px;\n    gap: 40px;\n    \n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .info-section {\n    .info-card {\n      background: #f8f9fa;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      padding: 20px;\n      margin-bottom: 20px;\n\n      h3 {\n        color: #333;\n        margin-bottom: 15px;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n      }\n\n      .warning {\n        background: #fff3cd;\n        border: 1px solid #ffeaa7;\n        border-radius: 4px;\n        padding: 15px;\n        color: #856404;\n\n        .icon {\n          margin-right: 8px;\n          color: #f39c12;\n        }\n      }\n    }\n  }\n\n  .actions {\n    background: white;\n    border: 1px solid #e9ecef;\n    border-radius: 8px;\n    padding: 20px;\n    height: fit-content;\n\n    .cluster-selector,\n    .namespace-selector {\n      margin-bottom: 20px;\n\n      label {\n        display: block;\n        margin-bottom: 5px;\n        font-weight: 600;\n      }\n\n      select {\n        width: 100%;\n        padding: 8px 12px;\n        border: 1px solid #ddd;\n        border-radius: 4px;\n        font-size: 14px;\n      }\n    }\n\n    .pod-info {\n      margin-bottom: 30px;\n      padding: 15px;\n      background: #f8f9fa;\n      border-radius: 4px;\n\n      h4 {\n        margin-bottom: 15px;\n        color: #333;\n      }\n\n      .stats {\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n\n          .label {\n            font-weight: 500;\n          }\n\n          .value {\n            font-weight: 600;\n            color: #007bff;\n          }\n        }\n      }\n    }\n\n    .game-controls {\n      .btn {\n        width: 100%;\n        margin-bottom: 10px;\n        padding: 12px;\n        font-size: 16px;\n        font-weight: 600;\n        border-radius: 6px;\n        border: none;\n        cursor: pointer;\n        transition: all 0.2s;\n\n        .icon {\n          margin-right: 8px;\n        }\n\n        &.btn-primary {\n          background: #ff6b6b;\n          color: white;\n\n          &:hover:not(:disabled) {\n            background: #ff5252;\n          }\n\n          &:disabled {\n            background: #ccc;\n            cursor: not-allowed;\n          }\n        }\n\n        &.btn-secondary {\n          background: #6c757d;\n          color: white;\n\n          &:hover:not(:disabled) {\n            background: #5a6268;\n          }\n\n          &:disabled {\n            background: #ccc;\n            cursor: not-allowed;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}