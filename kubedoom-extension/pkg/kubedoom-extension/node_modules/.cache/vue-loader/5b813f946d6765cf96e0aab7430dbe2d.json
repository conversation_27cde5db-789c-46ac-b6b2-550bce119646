{"remainingRequest": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue?vue&type=template&id=6d118c02&scoped=true", "dependencies": [{"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue", "mtime": 1748284942021}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/babel-loader/lib/index.js", "mtime": 1747923102829}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1747923098915}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1747923095351}, {"path": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/node_modules/vue-loader/dist/index.js", "mtime": 1747923098915}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC,EAAE,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/Documents/augment-projects/rancher-ui-extension-kubedoom/kubedoom-extension/pkg/kubedoom-extension/pages/KubeDoomGame.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"kubedoom-game\">\n    <div class=\"game-header\">\n      <div class=\"header-left\">\n        <button class=\"btn btn-secondary\" @click=\"goBack\">\n          <i class=\"icon icon-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n      \n      <div class=\"header-center\">\n        <h2>KubeDoom - {{ clusterName }}</h2>\n        <div class=\"status\" :class=\"connectionStatus\">\n          <i class=\"icon\" :class=\"statusIcon\"></i>\n          {{ statusText }}\n        </div>\n      </div>\n      \n      <div class=\"header-right\">\n        <button class=\"btn btn-danger\" @click=\"emergencyStop\">\n          <i class=\"icon icon-stop\"></i>\n          Emergency Stop\n        </button>\n      </div>\n    </div>\n\n    <div class=\"game-content\">\n      <div class=\"game-area\">\n        <div class=\"vnc-container\" ref=\"vncContainer\">\n          <div v-if=\"!connected\" class=\"connection-overlay\">\n            <div class=\"spinner\"></div>\n            <p>{{ connectionMessage }}</p>\n          </div>\n          <canvas ref=\"vncCanvas\" v-show=\"connected\"></canvas>\n        </div>\n        \n        <div class=\"game-controls\">\n          <div class=\"control-group\">\n            <h4>Game Controls</h4>\n            <div class=\"controls-grid\">\n              <div class=\"control-item\">\n                <span class=\"key\">WASD</span>\n                <span class=\"desc\">Move</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Mouse</span>\n                <span class=\"desc\">Look around</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Ctrl</span>\n                <span class=\"desc\">Fire</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">Space</span>\n                <span class=\"desc\">Open doors</span>\n              </div>\n              <div class=\"control-item\">\n                <span class=\"key\">ESC</span>\n                <span class=\"desc\">Pause</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"control-group\">\n            <h4>Cheats</h4>\n            <div class=\"cheat-buttons\">\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idkfa')\">\n                All Weapons\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('iddqd')\">\n                God Mode\n              </button>\n              <button class=\"btn btn-sm\" @click=\"sendCheat('idspispopd')\">\n                No Clip\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"pod-monitor\">\n        <h3>Pod Monitor</h3>\n        <div class=\"monitor-stats\">\n          <div class=\"stat\">\n            <span class=\"label\">Pods Killed:</span>\n            <span class=\"value killed\">{{ podsKilled }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Remaining:</span>\n            <span class=\"value remaining\">{{ podsRemaining }}</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"label\">Pods Respawned:</span>\n            <span class=\"value respawned\">{{ podsRespawned }}</span>\n          </div>\n        </div>\n\n        <div class=\"recent-kills\" v-if=\"recentKills.length > 0\">\n          <h4>Recent Pod Kills</h4>\n          <div class=\"kill-list\">\n            <div \n              v-for=\"kill in recentKills\" \n              :key=\"kill.id\"\n              class=\"kill-item\"\n            >\n              <span class=\"pod-name\">{{ kill.podName }}</span>\n              <span class=\"namespace\">{{ kill.namespace }}</span>\n              <span class=\"time\">{{ formatTime(kill.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"cluster-health\">\n          <h4>Cluster Health</h4>\n          <div class=\"health-indicator\" :class=\"clusterHealth.status\">\n            <i class=\"icon\" :class=\"clusterHealth.icon\"></i>\n            {{ clusterHealth.message }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'KubeDoomGame',\n  \n  data() {\n    return {\n      connected: false,\n      connectionStatus: 'connecting',\n      connectionMessage: 'Connecting to KubeDoom...',\n      vncClient: null,\n      clusterName: '',\n      podsKilled: 0,\n      podsRemaining: 0,\n      podsRespawned: 0,\n      recentKills: [],\n      clusterHealth: {\n        status: 'healthy',\n        icon: 'icon-checkmark',\n        message: 'Cluster is healthy'\n      },\n      monitoringInterval: null,\n    };\n  },\n\n  computed: {\n    statusIcon() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'icon-checkmark';\n        case 'connecting': return 'icon-spinner';\n        case 'error': return 'icon-error';\n        default: return 'icon-info';\n      }\n    },\n\n    statusText() {\n      switch (this.connectionStatus) {\n        case 'connected': return 'Connected to KubeDoom';\n        case 'connecting': return 'Connecting...';\n        case 'error': return 'Connection failed';\n        default: return 'Unknown status';\n      }\n    }\n  },\n\n  async mounted() {\n    this.clusterName = this.$route.params.cluster || 'Unknown';\n    await this.initializeVNC();\n    this.startMonitoring();\n  },\n\n  beforeUnmount() {\n    this.cleanup();\n  },\n\n  methods: {\n    async initializeVNC() {\n      try {\n        this.connectionMessage = 'Initializing VNC connection...';\n        \n        // In a real implementation, this would connect to the KubeDoom VNC server\n        // For now, we'll simulate the connection\n        await this.simulateVNCConnection();\n        \n      } catch (error) {\n        console.error('Failed to initialize VNC:', error);\n        this.connectionStatus = 'error';\n        this.connectionMessage = 'Failed to connect to KubeDoom';\n      }\n    },\n\n    async simulateVNCConnection() {\n      // Simulate connection delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      this.connected = true;\n      this.connectionStatus = 'connected';\n      \n      // Initialize canvas for game display\n      this.initializeCanvas();\n    },\n\n    initializeCanvas() {\n      const canvas = this.$refs.vncCanvas;\n      if (!canvas) return;\n\n      canvas.width = 800;\n      canvas.height = 600;\n      \n      const ctx = canvas.getContext('2d');\n      \n      // Draw a placeholder game screen\n      ctx.fillStyle = '#000';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      \n      ctx.fillStyle = '#ff0000';\n      ctx.font = '24px Arial';\n      ctx.textAlign = 'center';\n      ctx.fillText('KubeDoom Game Screen', canvas.width / 2, canvas.height / 2);\n      ctx.fillText('(VNC Connection Placeholder)', canvas.width / 2, canvas.height / 2 + 30);\n      \n      // Add click handler for interaction\n      canvas.addEventListener('click', this.handleCanvasClick);\n    },\n\n    handleCanvasClick() {\n      // Simulate pod kill when clicking on the game\n      this.simulatePodKill();\n    },\n\n    simulatePodKill() {\n      const podNames = [\n        'nginx-deployment-abc123',\n        'redis-master-def456',\n        'postgres-ghi789',\n        'api-server-jkl012',\n        'worker-mno345'\n      ];\n      \n      const namespaces = ['default', 'kube-system', 'monitoring', 'ingress'];\n      \n      const kill = {\n        id: Date.now(),\n        podName: podNames[Math.floor(Math.random() * podNames.length)],\n        namespace: namespaces[Math.floor(Math.random() * namespaces.length)],\n        timestamp: new Date()\n      };\n      \n      this.recentKills.unshift(kill);\n      if (this.recentKills.length > 10) {\n        this.recentKills.pop();\n      }\n      \n      this.podsKilled++;\n      this.podsRemaining = Math.max(0, this.podsRemaining - 1);\n      \n      // Update cluster health based on kills\n      this.updateClusterHealth();\n    },\n\n    updateClusterHealth() {\n      if (this.podsKilled > 20) {\n        this.clusterHealth = {\n          status: 'critical',\n          icon: 'icon-error',\n          message: 'Cluster under heavy stress!'\n        };\n      } else if (this.podsKilled > 10) {\n        this.clusterHealth = {\n          status: 'warning',\n          icon: 'icon-warning',\n          message: 'Cluster experiencing some stress'\n        };\n      } else {\n        this.clusterHealth = {\n          status: 'healthy',\n          icon: 'icon-checkmark',\n          message: 'Cluster is healthy'\n        };\n      }\n    },\n\n    sendCheat(cheatCode) {\n      // In a real implementation, this would send the cheat to the VNC session\n      console.log('Sending cheat:', cheatCode);\n      \n      this.$store.dispatch('growl/info', {\n        title: 'Cheat Activated',\n        message: `Cheat code \"${cheatCode}\" sent to game`\n      });\n    },\n\n    startMonitoring() {\n      this.podsRemaining = 50; // Initial pod count\n      \n      this.monitoringInterval = setInterval(() => {\n        // Simulate pod respawning\n        if (Math.random() < 0.1 && this.podsRemaining < 50) {\n          this.podsRemaining++;\n          this.podsRespawned++;\n        }\n      }, 5000);\n    },\n\n    formatTime(timestamp) {\n      return timestamp.toLocaleTimeString();\n    },\n\n    goBack() {\n      this.$router.push({\n        name: 'kubedoom-c-cluster-dashboard',\n        params: { cluster: this.$route.params.cluster }\n      });\n    },\n\n    async emergencyStop() {\n      if (confirm('Are you sure you want to stop KubeDoom? This will end the game session.')) {\n        await this.cleanup();\n        this.goBack();\n      }\n    },\n\n    cleanup() {\n      if (this.monitoringInterval) {\n        clearInterval(this.monitoringInterval);\n      }\n      \n      if (this.vncClient) {\n        // Disconnect VNC client\n        this.vncClient = null;\n      }\n      \n      const canvas = this.$refs.vncCanvas;\n      if (canvas) {\n        canvas.removeEventListener('click', this.handleCanvasClick);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.kubedoom-game {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #1a1a1a;\n  color: white;\n\n  .game-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px 20px;\n    background: #2d2d2d;\n    border-bottom: 1px solid #444;\n\n    .header-center {\n      text-align: center;\n\n      h2 {\n        margin: 0 0 5px 0;\n        color: #ff6b6b;\n      }\n\n      .status {\n        font-size: 14px;\n        \n        &.connected { color: #28a745; }\n        &.connecting { color: #ffc107; }\n        &.error { color: #dc3545; }\n\n        .icon {\n          margin-right: 5px;\n        }\n      }\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-weight: 500;\n\n      &.btn-secondary {\n        background: #6c757d;\n        color: white;\n      }\n\n      &.btn-danger {\n        background: #dc3545;\n        color: white;\n      }\n\n      .icon {\n        margin-right: 5px;\n      }\n    }\n  }\n\n  .game-content {\n    flex: 1;\n    display: grid;\n    grid-template-columns: 1fr 300px;\n    gap: 20px;\n    padding: 20px;\n\n    .game-area {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n\n      .vnc-container {\n        position: relative;\n        background: #000;\n        border: 2px solid #444;\n        border-radius: 8px;\n        overflow: hidden;\n        height: 600px;\n\n        .connection-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          background: rgba(0, 0, 0, 0.8);\n          z-index: 10;\n\n          .spinner {\n            width: 40px;\n            height: 40px;\n            border: 4px solid #333;\n            border-top: 4px solid #ff6b6b;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-bottom: 20px;\n          }\n\n          p {\n            color: #ccc;\n            font-size: 16px;\n          }\n        }\n\n        canvas {\n          width: 100%;\n          height: 100%;\n          cursor: crosshair;\n        }\n      }\n\n      .game-controls {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 20px;\n\n        .control-group {\n          background: #2d2d2d;\n          padding: 15px;\n          border-radius: 8px;\n\n          h4 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n          }\n\n          .controls-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 10px;\n\n            .control-item {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n              padding: 5px 0;\n\n              .key {\n                background: #444;\n                padding: 2px 8px;\n                border-radius: 4px;\n                font-family: monospace;\n                font-size: 12px;\n              }\n\n              .desc {\n                font-size: 12px;\n                color: #ccc;\n              }\n            }\n          }\n\n          .cheat-buttons {\n            display: flex;\n            flex-direction: column;\n            gap: 8px;\n\n            .btn {\n              padding: 6px 12px;\n              background: #444;\n              color: white;\n              border: none;\n              border-radius: 4px;\n              cursor: pointer;\n              font-size: 12px;\n\n              &:hover {\n                background: #555;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .pod-monitor {\n      background: #2d2d2d;\n      padding: 20px;\n      border-radius: 8px;\n      height: fit-content;\n\n      h3 {\n        margin: 0 0 20px 0;\n        color: #ff6b6b;\n      }\n\n      .monitor-stats {\n        margin-bottom: 25px;\n\n        .stat {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n\n          .label {\n            color: #ccc;\n          }\n\n          .value {\n            font-weight: 600;\n\n            &.killed { color: #dc3545; }\n            &.remaining { color: #28a745; }\n            &.respawned { color: #17a2b8; }\n          }\n        }\n      }\n\n      .recent-kills {\n        margin-bottom: 25px;\n\n        h4 {\n          margin: 0 0 15px 0;\n          color: #ffc107;\n        }\n\n        .kill-list {\n          max-height: 200px;\n          overflow-y: auto;\n\n          .kill-item {\n            display: flex;\n            flex-direction: column;\n            padding: 8px;\n            margin-bottom: 8px;\n            background: #1a1a1a;\n            border-radius: 4px;\n            font-size: 12px;\n\n            .pod-name {\n              font-weight: 600;\n              color: #ff6b6b;\n            }\n\n            .namespace {\n              color: #17a2b8;\n            }\n\n            .time {\n              color: #6c757d;\n            }\n          }\n        }\n      }\n\n      .cluster-health {\n        h4 {\n          margin: 0 0 15px 0;\n          color: #28a745;\n        }\n\n        .health-indicator {\n          padding: 10px;\n          border-radius: 4px;\n          text-align: center;\n          font-weight: 500;\n\n          &.healthy {\n            background: rgba(40, 167, 69, 0.2);\n            color: #28a745;\n          }\n\n          &.warning {\n            background: rgba(255, 193, 7, 0.2);\n            color: #ffc107;\n          }\n\n          &.critical {\n            background: rgba(220, 53, 69, 0.2);\n            color: #dc3545;\n          }\n\n          .icon {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n</style>\n"]}]}