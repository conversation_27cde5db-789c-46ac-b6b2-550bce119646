import Dashboard from '../pages/Dashboard.vue';
import KubeDoomGame from '../pages/KubeDoomGame.vue';

const BLANK_CLUSTER = '_';
const KUBEDOOM_PRODUCT_NAME = 'kubedoom';

const routes = [
  {
    name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-dashboard`,
    path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/dashboard`,
    component: Dashboard,
    meta: {
      product: KUBEDOOM_PRODUCT_NAME,
      cluster: BLANK_CLUSTER,
      pkg: KUBEDOOM_PRODUCT_NAME,
    },
  },
  {
    name: `${KUBEDOOM_PRODUCT_NAME}-c-cluster-game`,
    path: `/${KUBEDOOM_PRODUCT_NAME}/c/:cluster/game`,
    component: KubeDoomGame,
    meta: {
      product: KUBEDOOM_PRODUCT_NAME,
      cluster: BLANK_CLUSTER,
      pkg: KU<PERSON>D<PERSON><PERSON>_PRODUCT_NAME,
    },
  },
];

export default routes;
