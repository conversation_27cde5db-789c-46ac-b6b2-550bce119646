apiVersion: v1
kind: Namespace
metadata:
  name: kubedoom
  labels:
    name: kubedoom
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kubedoom
  namespace: kubedoom
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubedoom
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["list", "delete"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubedoom
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubedoom
subjects:
- kind: ServiceAccount
  name: kubedoom
  namespace: kubedoom
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubedoom
  namespace: kubedoom
  labels:
    app: kubedoom
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubedoom
  template:
    metadata:
      labels:
        app: kubedoom
    spec:
      serviceAccountName: kubedoom
      containers:
      - name: kubedoom
        image: ghcr.io/storax/kubedoom:latest
        ports:
        - containerPort: 5900
          name: vnc
        env:
        - name: NAMESPACE
          value: ""  # Empty means all namespaces
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      securityContext:
        fsGroup: 1000
---
apiVersion: v1
kind: Service
metadata:
  name: kubedoom-vnc
  namespace: kubedoom
  labels:
    app: kubedoom
spec:
  type: ClusterIP
  ports:
  - port: 5900
    targetPort: 5900
    protocol: TCP
    name: vnc
  selector:
    app: kubedoom
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kubedoom-netpol
  namespace: kubedoom
spec:
  podSelector:
    matchLabels:
      app: kubedoom
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: cattle-system
    ports:
    - protocol: TCP
      port: 5900
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # Kubernetes API
    - protocol: TCP
      port: 6443 # Kubernetes API (alternative port)
  - to: []
    ports:
    - protocol: UDP
      port: 53   # DNS
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kubedoom-config
  namespace: kubedoom
data:
  doom.cfg: |
    # Doom configuration for KubeDoom
    # This file contains game settings
    
    # Video settings
    screenblocks 11
    detaillevel 0
    
    # Audio settings
    snd_sfxvolume 8
    snd_musicvolume 8
    
    # Game settings
    skill 2
    
    # Controls
    key_right 77
    key_left 75
    key_up 72
    key_down 80
    key_strafeleft 44
    key_straferight 46
    key_fire 29
    key_use 57
    key_strafe 56
    key_speed 54
    
    # Mouse settings
    use_mouse 1
    mousesensitivity 5
    
    # Network settings (disabled for security)
    doomcom ""
    
  entrypoint.sh: |
    #!/bin/bash
    set -e
    
    echo "Starting KubeDoom..."
    echo "Namespace filter: ${NAMESPACE:-all}"
    
    # Start VNC server
    Xvfb :1 -screen 0 800x600x16 &
    x11vnc -display :1 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever &
    
    # Wait for X server to start
    sleep 2
    
    # Set display
    export DISPLAY=:1
    
    # Start the kubedoom binary
    exec /usr/local/bin/kubedoom
---
apiVersion: v1
kind: Secret
metadata:
  name: kubedoom-vnc-password
  namespace: kubedoom
type: Opaque
data:
  password: aWRiZWhvbGQ=  # Base64 encoded "idbehold"
