export declare const defaultEditorOptions: {
    mode: string;
    lint: boolean;
    indentWithTabs: boolean;
    smartIndent: boolean;
    lineNumbers: boolean;
    autofocus: boolean;
    lineWrapping: boolean;
    readOnly: boolean;
};
export declare const propEditorOptions: {
    mode: string;
    lint: boolean;
    indentWithTabs: boolean;
    smartIndent: boolean;
    lineNumbers: boolean;
    autofocus: boolean;
};
export declare const jsonEditorOptions: {
    mode: string;
    lint: boolean;
    indentWithTabs: boolean;
    smartIndent: boolean;
    lineNumbers: boolean;
    autofocus: boolean;
    matchBrackets: boolean;
};
