#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get the package name from command line arguments
const packageName = process.argv[2];

if (!packageName) {
  console.error('Usage: node build-pkg.js <package-name>');
  process.exit(1);
}

const pkgDir = path.join(__dirname, '..', 'pkg', packageName);
const distDir = path.join(__dirname, '..', 'dist-pkg');

// Check if package exists
if (!fs.existsSync(pkgDir)) {
  console.error(`Package ${packageName} not found in pkg/ directory`);
  process.exit(1);
}

// Read package.json
const packageJsonPath = path.join(pkgDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error(`package.json not found in ${pkgDir}`);
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const version = packageJson.version;

console.log(`🎮 Building ${packageName} v${version}...`);

try {
  // Create dist directory
  const outputDir = path.join(distDir, `${packageName}-${version}`);
  if (fs.existsSync(outputDir)) {
    fs.rmSync(outputDir, { recursive: true });
  }
  fs.mkdirSync(outputDir, { recursive: true });

  // Build the extension using a simple approach
  console.log('📦 Creating extension bundle...');

  // Copy package.json
  fs.copyFileSync(packageJsonPath, path.join(outputDir, 'package.json'));

  // Create a simple UMD bundle
  const indexPath = path.join(pkgDir, 'index.ts');
  const vuePath = path.join(pkgDir, 'KubeDoomPage.vue');
  const productPath = path.join(pkgDir, 'product.ts');

  // Read the source files
  let indexContent = '';
  let vueContent = '';
  let productContent = '';

  if (fs.existsSync(indexPath)) {
    indexContent = fs.readFileSync(indexPath, 'utf8');
  }

  if (fs.existsSync(vuePath)) {
    vueContent = fs.readFileSync(vuePath, 'utf8');
  }

  if (fs.existsSync(productPath)) {
    productContent = fs.readFileSync(productPath, 'utf8');
  }

  // Create a super simple, working UMD bundle without complex HTML
  const bundleContent = `(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['${packageName}'] = factory());
}(this, (function () { 'use strict';

  function plugin(pluginApi) {
    console.log('🎮 Loading KubeDoom Extension...');

    // Add a simple product
    pluginApi.addProduct({
      init: function(plugin, store) {
        const { product } = plugin.DSL(store, '${packageName}');

        product({
          inStore: 'management',
          icon: 'icon-pipeline',
          label: 'KubeDoom',
          removable: false,
          showClusterSwitcher: true,
          category: 'global',
          to: {
            name: '${packageName}',
            params: { cluster: 'local' }
          }
        });
      }
    });

    // Add a simple route
    pluginApi.addRoute({
      name: '${packageName}',
      path: '/${packageName}',
      component: {
        name: 'KubeDoomPage',
        template: '<div style="padding: 20px;"><h1>🎮 KubeDoom</h1><p>Extension loaded successfully!</p><div style="margin: 20px 0;"><a href="http://localhost:8080" target="_blank" style="background: #007cbb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🎮 Play KubeDoom</a></div><div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px;"><h3>How to Play:</h3><ul><li>WASD - Move</li><li>Mouse - Look</li><li>Ctrl - Fire (kill pods)</li></ul></div></div>',
        data() {
          return {
            message: 'KubeDoom is ready!'
          };
        },
        mounted() {
          console.log('KubeDoom page mounted');
        }
      }
    });

    // Add header action
    if (pluginApi.addAction) {
      pluginApi.addAction('header', {}, {
        tooltip: 'Open KubeDoom VNC',
        icon: 'icon-pipeline',
        invoke() {
          window.open('http://localhost:8080', '_blank');
        }
      });
    }

    console.log('✅ KubeDoom Extension loaded successfully!');
  }

  return plugin;

})));`;

  // Write the bundle
  const bundlePath = path.join(outputDir, `${packageName}-${version}.umd.min.js`);
  fs.writeFileSync(bundlePath, bundleContent);

  console.log(`✅ Extension built successfully!`);
  console.log(`📁 Output: ${outputDir}`);
  console.log(`📄 Bundle: ${bundlePath}`);

} catch (error) {
  console.error(`❌ Build failed: ${error.message}`);
  process.exit(1);
}
