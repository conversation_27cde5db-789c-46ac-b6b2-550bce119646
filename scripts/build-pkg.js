#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get the package name from command line arguments
const packageName = process.argv[2];

if (!packageName) {
  console.error('Usage: node build-pkg.js <package-name>');
  process.exit(1);
}

const pkgDir = path.join(__dirname, '..', 'pkg', packageName);
const distDir = path.join(__dirname, '..', 'dist-pkg');

// Check if package exists
if (!fs.existsSync(pkgDir)) {
  console.error(`Package ${packageName} not found in pkg/ directory`);
  process.exit(1);
}

// Read package.json
const packageJsonPath = path.join(pkgDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error(`package.json not found in ${pkgDir}`);
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const version = packageJson.version;

console.log(`🎮 Building ${packageName} v${version}...`);

try {
  // Create dist directory
  const outputDir = path.join(distDir, `${packageName}-${version}`);
  if (fs.existsSync(outputDir)) {
    fs.rmSync(outputDir, { recursive: true });
  }
  fs.mkdirSync(outputDir, { recursive: true });

  // Build the extension using a simple approach
  console.log('📦 Creating extension bundle...');

  // Copy package.json
  fs.copyFileSync(packageJsonPath, path.join(outputDir, 'package.json'));

  // Create a simple UMD bundle
  const indexPath = path.join(pkgDir, 'index.ts');
  const vuePath = path.join(pkgDir, 'KubeDoomPage.vue');
  const productPath = path.join(pkgDir, 'product.ts');

  // Read the source files
  let indexContent = '';
  let vueContent = '';
  let productContent = '';

  if (fs.existsSync(indexPath)) {
    indexContent = fs.readFileSync(indexPath, 'utf8');
  }

  if (fs.existsSync(vuePath)) {
    vueContent = fs.readFileSync(vuePath, 'utf8');
  }

  if (fs.existsSync(productPath)) {
    productContent = fs.readFileSync(productPath, 'utf8');
  }

  // Create a minimal, working UMD bundle
  const bundleContent = `(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['${packageName}'] = factory());
}(this, (function () { 'use strict';

  // Package metadata
  const metadata = ${JSON.stringify(packageJson, null, 2)};

  // Main plugin function
  function plugin(pluginApi) {
    console.log('🎮 Loading KubeDoom Extension...');

    // Set metadata
    pluginApi.metadata = metadata;

    // Add a simple product
    pluginApi.addProduct({
      init: function(plugin, store) {
        const { product } = plugin.DSL(store, '${packageName}');

        product({
          inStore: 'management',
          icon: 'icon-kubedoom',
          label: 'KubeDoom',
          removable: false,
          showClusterSwitcher: true,
          category: 'global',
          to: {
            name: '${packageName}',
            params: { cluster: 'local' }
          }
        });
      }
    });

    // Add a simple route with basic component
    pluginApi.addRoute({
      name: '${packageName}',
      path: '/${packageName}',
      component: {
        name: 'KubeDoomPage',
        template: \`
          <div class="kubedoom-container" style="padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="font-size: 2.5em; margin-bottom: 10px;">🎮 KubeDoom</h1>
              <p style="font-size: 1.2em; color: #666;">Play Doom to kill Kubernetes pods and test cluster resilience</p>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
                <h3>🚀 Quick Start</h3>
                <p>KubeDoom is ready to use! Click the button below to start playing:</p>
                <a href="http://localhost:8080" target="_blank"
                   style="display: inline-block; background-color: #007cbb; color: white; padding: 12px 24px;
                          text-decoration: none; border-radius: 4px; font-weight: bold;">
                  🎮 Open KubeDoom VNC
                </a>
              </div>

              <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
                <h3>📊 Status</h3>
                <div style="padding: 10px; background-color: #d4edda; color: #155724; border-radius: 4px; margin: 10px 0;">
                  ✅ KubeDoom Extension Loaded
                </div>
                <div style="padding: 10px; background-color: #d1ecf1; color: #0c5460; border-radius: 4px; margin: 10px 0;">
                  ℹ️ VNC Server: http://localhost:8080
                </div>
              </div>
            </div>

            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
              <h3>ℹ️ About KubeDoom</h3>
              <p>KubeDoom allows you to kill Kubernetes pods by playing the classic game Doom. It's a fun way to test your cluster's resilience.</p>
              <ul>
                <li>🎯 Target pods by shooting them in the game</li>
                <li>🔄 Watch your cluster recover automatically</li>
                <li>📈 Test application resilience and monitoring</li>
                <li>🎮 Have fun while learning about Kubernetes!</li>
              </ul>
              <h4>🎮 How to Play:</h4>
              <ul>
                <li><strong>WASD</strong> - Move around</li>
                <li><strong>Mouse</strong> - Look around</li>
                <li><strong>Ctrl</strong> - Fire (kill pods)</li>
                <li><strong>Space</strong> - Open doors</li>
              </ul>
            </div>
          </div>
        \`
      }
    });

    console.log('✅ KubeDoom Extension loaded successfully!');
  }

  return plugin;

})));`;

  // Write the bundle
  const bundlePath = path.join(outputDir, `${packageName}-${version}.umd.min.js`);
  fs.writeFileSync(bundlePath, bundleContent);

  console.log(`✅ Extension built successfully!`);
  console.log(`📁 Output: ${outputDir}`);
  console.log(`📄 Bundle: ${bundlePath}`);

} catch (error) {
  console.error(`❌ Build failed: ${error.message}`);
  process.exit(1);
}
