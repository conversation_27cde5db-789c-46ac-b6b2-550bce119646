#!/bin/bash

# Final KubeDoom Extension Installation Script
# Uses the properly built extension following <PERSON><PERSON>'s official pattern

echo "🎮 Installing KubeDoom Extension (Final Version)"
echo "==============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if rancher container is running
if ! docker ps | grep -q rancher-server; then
    print_error "Rancher server container not found. Please start it first."
    exit 1
fi

print_success "Found Rancher server container"

# Check if extension server is running
if ! docker ps | grep -q extension-server; then
    print_error "Extension server container not found. Please start it first."
    exit 1
fi

print_success "Found extension server container"

# Test if extension is accessible
print_step "Testing extension accessibility..."
if curl -s http://localhost:8000/kubedoom-0.1.0/package.json > /dev/null; then
    print_success "Extension is accessible at http://localhost:8000/kubedoom-0.1.0/"
else
    print_error "Extension is not accessible. Please check the extension server."
    exit 1
fi

# Create the UIPlugin YAML using the properly built extension
cat > kubedoom-uiplugin-final.yaml << 'EOF'
apiVersion: catalog.cattle.io/v1
kind: UIPlugin
metadata:
  name: kubedoom
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom
  annotations:
    catalog.cattle.io/display-name: "KubeDoom"
    catalog.cattle.io/description: "Play Doom to kill Kubernetes pods and test cluster resilience"
    catalog.cattle.io/ui-extensions-version: ">= 3.0.0 < 4.0.0"
spec:
  plugin:
    name: kubedoom
    version: 0.1.0
    endpoint: "http://extension-server:8000/kubedoom-0.1.0"
    noCache: false
    metadata:
      displayName: "KubeDoom"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "🎮"
EOF

# Create namespace if it doesn't exist
print_step "Creating cattle-ui-plugin-system namespace..."
docker exec rancher-server kubectl create namespace cattle-ui-plugin-system --dry-run=client -o yaml | docker exec -i rancher-server kubectl apply -f -

# Apply the UIPlugin
print_step "Installing KubeDoom UIPlugin..."
docker cp kubedoom-uiplugin-final.yaml rancher-server:/tmp/kubedoom-uiplugin-final.yaml
docker exec rancher-server kubectl apply -f /tmp/kubedoom-uiplugin-final.yaml

if [ $? -eq 0 ]; then
    echo ""
    print_success "KubeDoom Extension installed successfully!"
    echo ""
    echo "📋 Installation Summary:"
    echo "  ✅ Extension Name: kubedoom"
    echo "  ✅ Version: 0.1.0"
    echo "  ✅ Namespace: cattle-ui-plugin-system"
    echo "  ✅ Endpoint: http://extension-server:8000/kubedoom-0.1.0"
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Refresh your Rancher UI (F5 or Ctrl+R)"
    echo "2. Look for 'KubeDoom' in the main navigation menu"
    echo "3. If it doesn't appear immediately, wait 1-2 minutes and refresh again"
    echo "4. Click on KubeDoom to access the extension"
    echo ""
    echo "🎮 Using KubeDoom:"
    echo "1. Select your cluster and namespace"
    echo "2. Click 'Deploy KubeDoom'"
    echo "3. Click 'Play KubeDoom' to open the VNC interface"
    echo "4. Use WASD to move, mouse to look, Ctrl to fire"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "  Check extension status: docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system"
    echo "  Check extension logs: docker-compose logs extension-server"
    echo "  Test extension URL: curl http://localhost:8000/kubedoom-0.1.0/package.json"
    echo ""
    echo "🗑️ To uninstall:"
    echo "  docker exec rancher-server kubectl delete uiplugin kubedoom -n cattle-ui-plugin-system"
else
    print_error "Failed to install UIExtension"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "1. Check if Rancher is fully started:"
    echo "   curl -k https://localhost"
    echo ""
    echo "2. Check if kubectl is working:"
    echo "   docker exec rancher-server kubectl get nodes"
    echo ""
    echo "3. Check if the namespace exists:"
    echo "   docker exec rancher-server kubectl get namespace cattle-ui-plugin-system"
fi

# Clean up
rm -f kubedoom-uiplugin-final.yaml

echo ""
print_info "🎮 KubeDoom Extension installation complete!"
echo ""
print_info "💡 Remember:"
echo "  - The extension follows the official Rancher pattern"
echo "  - It's built as a proper UMD bundle"
echo "  - It includes Vue.js components and proper routing"
echo "  - VNC access is available at http://localhost:8080"
