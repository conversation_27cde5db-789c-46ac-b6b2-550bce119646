# 🎮 KubeDoom Extension for Rancher 2.11

A Rancher UI extension that integrates <PERSON><PERSON><PERSON><PERSON> directly into the Rancher interface, allowing you to play the classic game Doom to kill Kubernetes pods and test your cluster's resilience!

![KubeDoom Extension](https://img.shields.io/badge/Rancher-2.11-blue) ![License](https://img.shields.io/badge/license-MIT-green) ![Status](https://img.shields.io/badge/status-development-yellow)

## 🎯 What is KubeDoom?

KubeDoom is a fun and educational tool that combines the classic Doom game with Kubernetes cluster management. Each enemy in the game represents a pod in your cluster, and killing them will actually delete the corresponding pod. This provides an entertaining way to:

- Test your cluster's resilience and recovery mechanisms
- Observe how applications handle pod failures
- Learn about Kubernetes self-healing capabilities
- Have fun while doing chaos engineering!

## ⚠️ Safety Warning

**This extension will actually delete pods in your cluster!** Only use this in development, testing, or staging environments. Never use this in production unless you fully understand the consequences.

## ✨ Features

- **Seamless Integration**: Native Rancher UI extension with full integration
- **VNC Web Client**: Play Doom directly in your browser through the Rancher interface
- **Cluster Selection**: Choose which cluster and namespace to target
- **Real-time Monitoring**: Watch pod statistics and cluster health in real-time
- **Safety Controls**: Emergency stop functionality and namespace filtering
- **Pod Tracking**: Monitor killed pods, respawned pods, and recent activity
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 20+
- Yarn package manager
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd rancher-ui-extension-kubedoom
./setup-dev-environment.sh
```

This script will:
- Build the KubeDoom extension
- Start a local Rancher instance
- Create a K3s cluster for testing
- Install sample applications
- Set up VNC access for KubeDoom

### 2. Access the Environment

- **Rancher UI**: https://localhost
  - Username: `admin`
  - Password: `admin123`
- **KubeDoom VNC**: http://localhost:8080
- **Direct VNC**: vnc://localhost:5900

### 3. Install the Extension

1. Open Rancher UI and complete initial setup
2. Import the K3s cluster using the provided kubeconfig
3. Navigate to Extensions and install the KubeDoom extension
4. Access KubeDoom from the main navigation menu

## 🛠️ Development

### Building the Extension

```bash
cd kubedoom-extension
yarn install --ignore-engines
yarn build-pkg kubedoom-extension
```

### Development Mode

```bash
cd kubedoom-extension
yarn dev
```

### Project Structure

```
kubedoom-extension/
├── pkg/kubedoom-extension/
│   ├── index.ts                 # Main extension entry point
│   ├── product.ts              # Product configuration
│   ├── pages/
│   │   ├── Dashboard.vue       # Main dashboard page
│   │   └── KubeDoomGame.vue    # Game interface page
│   ├── routing/
│   │   └── extension-routing.ts # Route definitions
│   ├── utils/
│   │   └── kubedoom-manager.ts # KubeDoom deployment manager
│   ├── manifests/
│   │   └── kubedoom-deployment.yaml # Kubernetes manifests
│   └── styles/
│       └── kubedoom.scss       # Custom styling
├── docker-compose.yml          # Development environment
└── setup-dev-environment.sh    # Setup script
```

## 🎮 How to Use

### 1. Dashboard

- Select your target cluster from the dropdown
- Optionally choose a specific namespace to target
- Review cluster statistics and pod counts
- Click "Start KubeDoom" to deploy the game

### 2. Game Interface

- Use standard Doom controls (WASD, mouse, Ctrl to fire)
- Each enemy represents a pod in your cluster
- Killing enemies will delete the corresponding pods
- Monitor real-time statistics in the side panel
- Use the emergency stop button if needed

### 3. Game Controls

- **WASD**: Move around
- **Mouse**: Look around
- **Ctrl**: Fire weapon
- **Space**: Open doors
- **ESC**: Pause game

### 4. Cheat Codes

- **IDKFA**: All weapons and ammo
- **IDDQD**: God mode (invincibility)
- **IDSPISPOPD**: No-clip mode (walk through walls)

## 🔧 Configuration

### Environment Variables

- `NAMESPACE`: Target namespace for KubeDoom (empty = all namespaces)
- `CATTLE_BOOTSTRAP_PASSWORD`: Rancher admin password
- `K3S_CLUSTER_SECRET`: K3s cluster secret

### Customization

The extension can be customized by modifying:
- `product.ts`: Product configuration and navigation
- `Dashboard.vue`: Main interface and cluster selection
- `KubeDoomGame.vue`: Game interface and VNC integration
- `kubedoom.scss`: Styling and themes

## 🐳 Docker Services

The development environment includes:

- **rancher**: Rancher 2.11 server
- **k3s-server**: K3s Kubernetes cluster (server)
- **k3s-agent**: K3s Kubernetes cluster (agent)
- **kubedoom**: KubeDoom game container
- **novnc**: Web-based VNC client
- **dev-container**: Development container for building

## 📊 Monitoring

The extension provides real-time monitoring of:
- Total pod count
- Running pods
- Pods killed by KubeDoom
- Pods respawned by Kubernetes
- Recent pod kill activity
- Cluster health status

## 🔒 Security Considerations

- KubeDoom requires cluster-wide pod list and delete permissions
- The extension creates a dedicated service account with minimal required permissions
- Network policies restrict access to the KubeDoom pod
- VNC access is limited to the cluster network
- All deployments use security contexts and non-root users

## 🚨 Troubleshooting

### Common Issues

1. **Extension not loading**: Check that the extension is properly built and the dist folder is mounted
2. **VNC connection failed**: Ensure KubeDoom pod is running and VNC service is accessible
3. **Pods not appearing in game**: Check RBAC permissions and namespace configuration
4. **Game controls not working**: Ensure VNC client has focus and keyboard capture is enabled

### Useful Commands

```bash
# View Rancher logs
docker-compose logs -f rancher

# Check KubeDoom pod status
docker exec k3s-server kubectl get pods -n kubedoom

# Restart development environment
./setup-dev-environment.sh stop
./setup-dev-environment.sh start

# Clean up everything
./setup-dev-environment.sh cleanup
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly in the development environment
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [KubeDoom](https://github.com/storax/kubedoom) - The original KubeDoom project
- [Rancher](https://rancher.com/) - For the excellent Kubernetes management platform
- [Doom](https://en.wikipedia.org/wiki/Doom_(1993_video_game)) - The legendary game that started it all

## 📞 Support

For issues and questions:
- Create an issue in this repository
- Check the troubleshooting section
- Review the Rancher extension documentation

---

**Remember**: With great power comes great responsibility. Use KubeDoom wisely! 🎮💀
