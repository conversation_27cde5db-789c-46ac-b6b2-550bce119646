#!/bin/bash

# <PERSON><PERSON>Doom Final Status Check and Testing Guide

echo "🎮 KubeDoom Complete Setup - Final Status"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[STATUS]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo ""
print_status "Checking all services..."

# Check Docker containers
RANCHER_STATUS="❌"
EXTENSION_STATUS="❌"
K3S_STATUS="❌"
KUBEDOOM_STATUS="❌"
VNC_STATUS="❌"

if docker ps | grep -q rancher-server; then
    RANCHER_STATUS="✅"
    print_success "Rancher server is running"
else
    print_error "Rancher server is not running"
fi

if docker ps | grep -q extension-server; then
    EXTENSION_STATUS="✅"
    print_success "Extension server is running"
else
    print_error "Extension server is not running"
fi

if docker ps | grep -q k3s-server; then
    K3S_STATUS="✅"
    print_success "K3s server is running"
else
    print_error "K3s server is not running"
fi

if docker ps | grep -q kubedoom-test; then
    KUBEDOOM_STATUS="✅"
    print_success "KubeDoom is running"
else
    print_error "KubeDoom is not running"
fi

if docker ps | grep -q novnc-client; then
    VNC_STATUS="✅"
    print_success "VNC client is running"
else
    print_error "VNC client is not running"
fi

# Check web services
print_status "Checking web services..."

RANCHER_UI_STATUS="❌"
EXTENSION_SERVER_STATUS="❌"
VNC_WEB_STATUS="❌"

if curl -k -s -o /dev/null -w "%{http_code}" https://localhost | grep -q "200\|302"; then
    RANCHER_UI_STATUS="✅"
    print_success "Rancher UI is accessible"
else
    print_error "Rancher UI is not accessible"
fi

if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/kubedoom-0.1.1/package.json | grep -q "200"; then
    EXTENSION_SERVER_STATUS="✅"
    print_success "Extension server is accessible"
else
    print_error "Extension server is not accessible"
fi

if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200"; then
    VNC_WEB_STATUS="✅"
    print_success "VNC web interface is accessible"
else
    print_error "VNC web interface is not accessible"
fi

# Check KubeDoom extension
print_status "Checking KubeDoom extension..."

EXTENSION_INSTALLED="❌"
if docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system kubedoom-v2 &> /dev/null; then
    EXTENSION_INSTALLED="✅"
    print_success "KubeDoom v2 extension is installed"
else
    print_error "KubeDoom v2 extension is not installed"
fi

# Check demo pods
print_status "Checking demo applications..."

DEMO_PODS_COUNT=$(docker exec kubedoom-test kubectl get pods -n kubedoom-demo --no-headers 2>/dev/null | wc -l)
if [ "$DEMO_PODS_COUNT" -gt 0 ]; then
    DEMO_STATUS="✅ ($DEMO_PODS_COUNT pods)"
    print_success "Demo applications are running ($DEMO_PODS_COUNT pods)"
else
    DEMO_STATUS="❌"
    print_error "Demo applications are not running"
fi

# Check KubeDoom cluster access
CLUSTER_ACCESS="❌"
if docker exec kubedoom-test kubectl get nodes &> /dev/null; then
    CLUSTER_ACCESS="✅"
    print_success "KubeDoom can access the cluster"
else
    print_error "KubeDoom cannot access the cluster"
fi

echo ""
echo "📊 Complete Status Summary"
echo "=========================="
echo ""
echo "🐳 Docker Services:"
echo "  Rancher Server:     $RANCHER_STATUS"
echo "  Extension Server:   $EXTENSION_STATUS"
echo "  K3s Server:         $K3S_STATUS"
echo "  KubeDoom:           $KUBEDOOM_STATUS"
echo "  VNC Client:         $VNC_STATUS"
echo ""
echo "🌐 Web Services:"
echo "  Rancher UI:         $RANCHER_UI_STATUS https://localhost"
echo "  Extension Server:   $EXTENSION_SERVER_STATUS http://localhost:8000"
echo "  VNC Interface:      $VNC_WEB_STATUS http://localhost:8080"
echo ""
echo "🎮 KubeDoom Setup:"
echo "  Extension:          $EXTENSION_INSTALLED"
echo "  Cluster Access:     $CLUSTER_ACCESS"
echo "  Demo Apps:          $DEMO_STATUS"
echo ""

# Determine overall status
if [[ "$RANCHER_STATUS" == "✅" && "$EXTENSION_STATUS" == "✅" && "$K3S_STATUS" == "✅" && "$KUBEDOOM_STATUS" == "✅" && "$VNC_STATUS" == "✅" && "$EXTENSION_INSTALLED" == "✅" && "$CLUSTER_ACCESS" == "✅" && "$DEMO_PODS_COUNT" -gt 0 ]]; then
    echo "🎉 ALL SYSTEMS READY!"
    echo "===================="
    echo ""
    echo "🎮 How to Use KubeDoom:"
    echo "1. Open Rancher UI: https://localhost"
    echo "   - Login: admin / admin123"
    echo "   - Look for 'KubeDoom v2' in the navigation menu"
    echo ""
    echo "2. Open KubeDoom VNC: http://localhost:8080"
    echo "   - This opens the Doom game interface"
    echo "   - Use WASD to move, mouse to look, Ctrl to fire"
    echo ""
    echo "3. Play and Test:"
    echo "   - Shoot demons in the game to kill Kubernetes pods"
    echo "   - Watch pods get terminated and recreated"
    echo "   - Monitor with: docker exec kubedoom-test kubectl get pods -n kubedoom-demo"
    echo ""
    echo "🎯 Demo Applications Available:"
    echo "  - 5x Nginx pods (web servers)"
    echo "  - 3x Redis pods (databases)"
    echo "  - 4x Busybox pods (utilities)"
    echo "  - Total: $DEMO_PODS_COUNT pods ready to be destroyed!"
    echo ""
else
    echo "⚠️ SETUP INCOMPLETE"
    echo "==================="
    echo ""
    echo "Some components are not working properly. Please check the errors above."
    echo ""
    echo "🔧 Common fixes:"
    echo "1. Restart all services: docker-compose restart"
    echo "2. Reinstall extension: ./install-kubedoom-v2.sh"
    echo "3. Redeploy demo apps: ./deploy-k3s-demo-apps.sh"
fi

echo ""
echo "🛠️ Useful Commands:"
echo "=================="
echo ""
echo "Monitor pods in real-time:"
echo "  watch 'docker exec kubedoom-test kubectl get pods -n kubedoom-demo'"
echo ""
echo "Check KubeDoom logs:"
echo "  docker logs kubedoom-test"
echo ""
echo "Restart KubeDoom:"
echo "  docker-compose restart kubedoom"
echo ""
echo "View all services:"
echo "  docker-compose ps"
