#!/bin/bash

# Deploy Demo Applications for KubeDoom Testing

echo "🚀 Deploying Demo Applications for KubeDoom"
echo "==========================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Create demo namespace
print_step "Creating demo namespace..."
docker exec rancher-server kubectl create namespace kubedoom-demo --dry-run=client -o yaml | docker exec -i rancher-server kubectl apply -f -

# Deploy nginx pods (easy targets)
print_step "Deploying nginx pods..."
cat << 'EOF' | docker exec -i rancher-server kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-demo
  namespace: kubedoom-demo
  labels:
    app: nginx-demo
spec:
  replicas: 5
  selector:
    matchLabels:
      app: nginx-demo
  template:
    metadata:
      labels:
        app: nginx-demo
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "32Mi"
            cpu: "50m"
          limits:
            memory: "64Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-demo-service
  namespace: kubedoom-demo
spec:
  selector:
    app: nginx-demo
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF

# Deploy redis pods
print_step "Deploying redis pods..."
cat << 'EOF' | docker exec -i rancher-server kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-demo
  namespace: kubedoom-demo
  labels:
    app: redis-demo
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-demo
  template:
    metadata:
      labels:
        app: redis-demo
    spec:
      containers:
      - name: redis
        image: redis:alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "32Mi"
            cpu: "50m"
          limits:
            memory: "64Mi"
            cpu: "100m"
EOF

# Deploy busybox pods (for testing)
print_step "Deploying busybox pods..."
cat << 'EOF' | docker exec -i rancher-server kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: busybox-demo
  namespace: kubedoom-demo
  labels:
    app: busybox-demo
spec:
  replicas: 4
  selector:
    matchLabels:
      app: busybox-demo
  template:
    metadata:
      labels:
        app: busybox-demo
    spec:
      containers:
      - name: busybox
        image: busybox:latest
        command: ['sleep', '3600']
        resources:
          requests:
            memory: "16Mi"
            cpu: "25m"
          limits:
            memory: "32Mi"
            cpu: "50m"
EOF

# Wait for deployments to be ready
print_step "Waiting for deployments to be ready..."
docker exec rancher-server kubectl wait --for=condition=available --timeout=120s deployment/nginx-demo -n kubedoom-demo
docker exec rancher-server kubectl wait --for=condition=available --timeout=120s deployment/redis-demo -n kubedoom-demo
docker exec rancher-server kubectl wait --for=condition=available --timeout=120s deployment/busybox-demo -n kubedoom-demo

# Show the deployed pods
print_step "Checking deployed pods..."
echo ""
docker exec rancher-server kubectl get pods -n kubedoom-demo -o wide

echo ""
print_success "Demo applications deployed successfully!"
echo ""
print_info "📊 Summary:"
echo "  ✅ Namespace: kubedoom-demo"
echo "  ✅ Nginx pods: 5 replicas"
echo "  ✅ Redis pods: 3 replicas"
echo "  ✅ Busybox pods: 4 replicas"
echo "  ✅ Total targets: 12 pods"
echo ""
print_info "🎮 KubeDoom Usage:"
echo "1. Open KubeDoom VNC: http://localhost:8080"
echo "2. Play Doom and shoot the demons (pods)"
echo "3. Watch pods get terminated and recreated"
echo "4. Test your cluster's resilience!"
echo ""
print_info "🔍 Monitor pods:"
echo "  watch 'docker exec rancher-server kubectl get pods -n kubedoom-demo'"
