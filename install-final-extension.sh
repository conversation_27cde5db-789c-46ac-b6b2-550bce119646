#!/bin/bash

# Install KubeDoom Extension using correct Rancher CRDs

echo "🎮 Installing KubeDoom Extension (Final Version)"
echo "==============================================="

# Check if rancher container is running
if ! docker ps | grep -q rancher-server; then
    echo "❌ Rancher server container not found. Please start it first."
    exit 1
fi

echo "✅ Found Rancher server container"

# Create the cattle-ui-plugin-system namespace
echo "📦 Creating cattle-ui-plugin-system namespace..."
docker exec rancher-server kubectl create namespace cattle-ui-plugin-system --dry-run=client -o yaml | docker exec -i rancher-server kubectl apply -f -

# Create UIPlugin resource
cat > kubedoom-uiplugin.yaml << 'EOF'
apiVersion: catalog.cattle.io/v1
kind: UIPlugin
metadata:
  name: kubedoom-extension
  namespace: cattle-ui-plugin-system
  labels:
    app: kubedoom-extension
spec:
  plugin:
    name: kubedoom-extension
    version: 0.1.0
    endpoint: "http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    noCache: false
    metadata:
      displayName: "KubeDoom"
      description: "Play Doom to kill Kubernetes pods and test cluster resilience"
      icon: "icon-kubedoom"
EOF

# Create NavLink resource for navigation
cat > kubedoom-navlink.yaml << 'EOF'
apiVersion: ui.cattle.io/v1
kind: NavLink
metadata:
  name: kubedoom-navlink
  namespace: cattle-ui-plugin-system
spec:
  toURL: "/c/local/kubedoom"
  label: "KubeDoom"
  description: "Play Doom to kill Kubernetes pods"
  iconSrc: "🎮"
  group: "Tools"
EOF

# Apply the resources
echo "📦 Creating UIPlugin resource..."
docker cp kubedoom-uiplugin.yaml rancher-server:/tmp/kubedoom-uiplugin.yaml
docker exec rancher-server kubectl apply -f /tmp/kubedoom-uiplugin.yaml

echo "📦 Creating NavLink resource..."
docker cp kubedoom-navlink.yaml rancher-server:/tmp/kubedoom-navlink.yaml
docker exec rancher-server kubectl apply -f /tmp/kubedoom-navlink.yaml

# Check if resources were created successfully
echo ""
echo "🔍 Checking installation status..."
echo ""
echo "UIPlugin status:"
docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system

echo ""
echo "NavLink status:"
docker exec rancher-server kubectl get navlinks

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ KubeDoom Extension installed successfully!"
    echo ""
    echo "📋 Installation Summary:"
    echo "  ✅ Namespace: cattle-ui-plugin-system"
    echo "  ✅ UIPlugin: kubedoom-extension"
    echo "  ✅ NavLink: kubedoom-navlink"
    echo "  ✅ Extension endpoint: http://extension-server:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Refresh your Rancher UI (F5 or Ctrl+R)"
    echo "2. Look for 'KubeDoom' in the main navigation menu or Tools section"
    echo "3. If it doesn't appear, wait a few minutes and refresh again"
    echo "4. Check browser console for any JavaScript errors"
    echo ""
    echo "🔍 Troubleshooting commands:"
    echo "  Check UIPlugin: docker exec rancher-server kubectl get uiplugins -n cattle-ui-plugin-system"
    echo "  Check NavLink: docker exec rancher-server kubectl get navlinks"
    echo "  Check extension server: curl http://localhost:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
    echo ""
    echo "🗑️ To uninstall:"
    echo "  docker exec rancher-server kubectl delete uiplugin kubedoom-extension -n cattle-ui-plugin-system"
    echo "  docker exec rancher-server kubectl delete navlink kubedoom-navlink"
else
    echo "❌ Failed to install extension resources"
fi

# Clean up
rm -f kubedoom-uiplugin.yaml kubedoom-navlink.yaml

echo ""
echo "🎮 KubeDoom Extension installation complete!"
echo ""
echo "💡 If the extension still doesn't appear:"
echo "1. The extension might need to be a proper Vue.js component"
echo "2. Try accessing the extension server directly: http://localhost:8000"
echo "3. Check if the JavaScript file loads: http://localhost:8000/kubedoom-extension-0.1.0/kubedoom-extension-0.1.0.umd.min.js"
