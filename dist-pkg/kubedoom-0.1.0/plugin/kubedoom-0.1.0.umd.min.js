(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['kubedoom'] = factory());
}(this, (function () { 'use strict';

  // Package metadata
  const metadata = {
  "name": "kubedoom",
  "description": "Play Doom to kill Kubernetes pods and test cluster resilience",
  "version": "0.1.0",
  "private": false,
  "rancher": {
    "annotations": {
      "catalog.cattle.io/kube-version": ">= 1.16.0-0",
      "catalog.cattle.io/rancher-version": ">= 2.10.0-0",
      "catalog.cattle.io/ui-extensions-version": ">= 3.0.0 < 4.0.0"
    }
  },
  "annotations": {
    "catalog.cattle.io/ui-extensions-version": ">= 3.0.0 < 4.0.0"
  },
  "scripts": {},
  "engines": {
    "node": ">=20"
  },
  "devDependencies": {
    "@vue/cli-plugin-babel": "~5.0.0",
    "@vue/cli-service": "~5.0.0",
    "@vue/cli-plugin-typescript": "~5.0.0"
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not dead"
  ]
};

  // Main plugin function
  function plugin(pluginApi) {
    console.log('🎮 Loading KubeDoom Extension...');

    // Set metadata
    pluginApi.metadata = metadata;

    // Add a simple product
    pluginApi.addProduct({
      init: function(plugin, store) {
        const { product } = plugin.DSL(store, 'kubedoom');

        product({
          inStore: 'management',
          icon: 'icon-kubedoom',
          label: 'KubeDoom',
          removable: false,
          showClusterSwitcher: true,
          category: 'global',
          to: {
            name: 'kubedoom',
            params: { cluster: 'local' }
          }
        });
      }
    });

    // Add a simple route with basic component
    pluginApi.addRoute({
      name: 'kubedoom',
      path: '/kubedoom',
      component: {
        name: 'KubeDoomPage',
        template: `
          <div class="kubedoom-container" style="padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="font-size: 2.5em; margin-bottom: 10px;">🎮 KubeDoom</h1>
              <p style="font-size: 1.2em; color: #666;">Play Doom to kill Kubernetes pods and test cluster resilience</p>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
                <h3>🚀 Quick Start</h3>
                <p>KubeDoom is ready to use! Click the button below to start playing:</p>
                <a href="http://localhost:8080" target="_blank"
                   style="display: inline-block; background-color: #007cbb; color: white; padding: 12px 24px;
                          text-decoration: none; border-radius: 4px; font-weight: bold;">
                  🎮 Open KubeDoom VNC
                </a>
              </div>

              <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
                <h3>📊 Status</h3>
                <div style="padding: 10px; background-color: #d4edda; color: #155724; border-radius: 4px; margin: 10px 0;">
                  ✅ KubeDoom Extension Loaded
                </div>
                <div style="padding: 10px; background-color: #d1ecf1; color: #0c5460; border-radius: 4px; margin: 10px 0;">
                  ℹ️ VNC Server: http://localhost:8080
                </div>
              </div>
            </div>

            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
              <h3>ℹ️ About KubeDoom</h3>
              <p>KubeDoom allows you to kill Kubernetes pods by playing the classic game Doom. It's a fun way to test your cluster's resilience.</p>
              <ul>
                <li>🎯 Target pods by shooting them in the game</li>
                <li>🔄 Watch your cluster recover automatically</li>
                <li>📈 Test application resilience and monitoring</li>
                <li>🎮 Have fun while learning about Kubernetes!</li>
              </ul>
              <h4>🎮 How to Play:</h4>
              <ul>
                <li><strong>WASD</strong> - Move around</li>
                <li><strong>Mouse</strong> - Look around</li>
                <li><strong>Ctrl</strong> - Fire (kill pods)</li>
                <li><strong>Space</strong> - Open doors</li>
              </ul>
            </div>
          </div>
        `
      }
    });

    console.log('✅ KubeDoom Extension loaded successfully!');
  }

  return plugin;

})));