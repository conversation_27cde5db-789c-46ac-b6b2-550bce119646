(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['kubedoom'] = factory());
}(this, (function () { 'use strict';

  function plugin(pluginApi) {
    console.log('🎮 Loading KubeDoom Extension...');

    // Add a simple product
    pluginApi.addProduct({
      init: function(plugin, store) {
        const { product } = plugin.DSL(store, 'kubedoom');

        product({
          inStore: 'management',
          icon: 'icon-pipeline',
          label: 'KubeDoom',
          removable: false,
          showClusterSwitcher: true,
          category: 'global',
          to: {
            name: 'kubedoom',
            params: { cluster: 'local' }
          }
        });
      }
    });

    // Add a simple route
    pluginApi.addRoute({
      name: 'kubedoom',
      path: '/kubedoom',
      component: {
        name: '<PERSON>beDoomPage',
        template: '<div style="padding: 20px;"><h1>🎮 KubeDoom</h1><p>Extension loaded successfully!</p><div style="margin: 20px 0;"><a href="http://localhost:8080" target="_blank" style="background: #007cbb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🎮 Play KubeDoom</a></div><div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px;"><h3>How to Play:</h3><ul><li>WASD - Move</li><li>Mouse - Look</li><li>Ctrl - Fire (kill pods)</li></ul></div></div>',
        data() {
          return {
            message: 'KubeDoom is ready!'
          };
        },
        mounted() {
          console.log('KubeDoom page mounted');
        }
      }
    });

    // Add header action
    if (pluginApi.addAction) {
      pluginApi.addAction('header', {}, {
        tooltip: 'Open KubeDoom VNC',
        icon: 'icon-pipeline',
        invoke() {
          window.open('http://localhost:8080', '_blank');
        }
      });
    }

    console.log('✅ KubeDoom Extension loaded successfully!');
  }

  return plugin;

})));