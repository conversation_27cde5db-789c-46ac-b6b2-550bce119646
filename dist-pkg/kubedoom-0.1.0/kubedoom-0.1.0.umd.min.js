(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global['kubedoom'] = factory());
}(this, (function () { 'use strict';

  function plugin(pluginApi) {
    console.log('🎮 KubeDoom Extension Loading...');

    pluginApi.addRoute({
      name: 'kubedoom',
      path: '/kubedoom',
      component: {
        name: 'KubeDoomPage',
        template: '<div style="padding: 20px;"><h1>🎮 KubeDoom</h1><p>Extension loaded successfully!</p><a href="http://localhost:8080" target="_blank" style="background: #007cbb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Open VNC</a></div>'
      }
    });

    console.log('✅ KubeDoom Extension Loaded!');
  }

  return plugin;

})));